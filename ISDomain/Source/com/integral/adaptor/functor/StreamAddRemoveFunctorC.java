package com.integral.adaptor.functor;

import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.config.BrokerAdaptorMBean;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.fixed.pricing.FixedPricingSchedulerServiceUtility;
import com.integral.broker.fixed.pricing.FixedPricingTimerTask;
import com.integral.broker.model.Stream;
import com.integral.broker.model.StreamC;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.MulticastListenerManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.AdaptorManagerC;
import com.integral.lp.Adaptor;
import com.integral.marketmaker.service.subscription.MMBrokerSubscriptionServiceC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.rds.notification.Notification;
import com.integral.rds.notification.NotificationService;
import com.integral.session.RemoteTransactionNotification;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.transport.multicast.MulticastAddress;
import com.integral.transport.multicast.MulticastAddressC;
import com.integral.user.Organization;

import java.util.*;


public class StreamAddRemoveFunctorC implements RemoteTransactionNotification {

    public static final String ORGANIZATION_OBJECTID = "BROKER_ORGANIZATION_OBJECTID";
    public static final String STREAM_EVENT= "STREAM_EVENT";
    public static final String ADD_STREAM ="ADD_STREAM";
    public static final String STREAM_GUID= "STREAM_GUID";
    public static final String REMVOE_STREAM="REMOVE_STREAM";
    public static final String MULTICAST_ADDR_GUID="MULTICAST_ADDR_GUID";    
    

    public static Log log = LogFactory.getLog(StreamAddRemoveFunctorC.class);

    private static Set streamUpdateListeners = new HashSet();

    /**
     * Process a remote transaction notification for add and remove stream for adaptors
     *
     * @param props
     */
    public void onCommit(HashMap props) {

        // Donot process these notification on proxy server. It can causes ProviderManager initialization which should
        // be avoided on Proxy server.
        ServerMBean serverMBean = ConfigurationFactory.getServerMBean();
        if (ISConstantsC.PROXY_SERVER_TYPE.equalsIgnoreCase(serverMBean.getVirtualServerType())) {
            if (log.isDebugEnabled())
                log.debug("StreamUpdateFunctorC.onCommit Its a proxy server. Do not process message ");
            return;
        }

        if (log.isInfoEnabled())
            log.info("StreamUpdateListenerC.onCommit:received StreamUpdateProps:" + props);

        try {
            // get broker organization id
            String id = (String) props.get(ORGANIZATION_OBJECTID);
            long brokerID = Long.valueOf(id);
            Organization org = (Organization) ReferenceDataCacheC.getInstance().getEntityByObjectId(brokerID, Organization.class);
            String orgName = org.getShortName();

            // Update ProviderListenerManagerC of added/updated stream to join or leave multicast group
            if(props.get(STREAM_EVENT)!=null && props.get(STREAM_EVENT).equals(REMVOE_STREAM ))
            {
                MulticastAddress multicastAddress =  (MulticastAddressC)ReferenceDataCacheC.getInstance().getEntityByGuid( (String)props.get(MULTICAST_ADDR_GUID), MulticastAddressC.class );
                if(multicastAddress!=null && multicastAddress.getStream() != null )
                {
                    String streamName = multicastAddress.getStream().getName();
                    log.warn("INFO: Leaving multicast address- "+multicastAddress );
                    Adaptor adaptor = AdaptorManagerC.getInstance().getAdaptor( orgName );
                    if( adaptor != null )
                    {
                        MulticastListenerManagerC.getInstance().leaveMulticast(adaptor,multicastAddress.getStream().getName()  );
                    }
                    notifyStreamUpdate(org, streamName);
                }
                else
                {
                    if( multicastAddress != null )
                    {
                        log.warn("Called stream update functor with multicast Address- "+multicastAddress+" and stream "+multicastAddress.getStream());
                    }
                    else
                    {
                        log.warn("Called stream update functor with multicast Address- "+multicastAddress );
                    }
                    String streamGUID = (String)props.get(MULTICAST_ADDR_GUID);
                    cancelTimerTaskOfDeletedStream(streamGUID);
                    notifyStreamUpdate(org, "NA");
                }
                return;
            }
            if(props.get(STREAM_EVENT)!=null && props.get(STREAM_EVENT).equals(ADD_STREAM ))
            {
                Stream newlyAddedStream =  (Stream)ReferenceDataCacheC.getInstance().getEntityByGuid( (String)props.get(STREAM_GUID), StreamC.class );
                if( newlyAddedStream!=null && newlyAddedStream.getMulticastAddress() !=null )
                {
                    ResponseHandlerC.getInstance().clearProviderStreamCache(orgName);
                    notifyStreamUpdate(org, newlyAddedStream.getName());
                    scheduleFixedPricing(org, newlyAddedStream);
                }
                else
                {
                    log.warn("Called stream update functor with null stream or multicast Address"+newlyAddedStream);
                }
            }

        } catch (Exception e)
        {
            log.error("Exception happened in processing Stream Add - Remove functor ", e);
        }
    }

    private void cancelTimerTaskOfDeletedStream(String streamGUID) {
        Stream stream = null;
        boolean timeTaskPresent = false;
        for (Map.Entry<Stream, FixedPricingTimerTask> entry : FixedPricingSchedulerServiceUtility.getStreamTaskMap().entrySet())
        {
            stream = entry.getKey();
            if(streamGUID.equals(stream.getMulticastAddress().getGUID())){
                FixedPricingTimerTask timeTask = entry.getValue();
                timeTask.cancel();
                timeTaskPresent = true;
                break;
            }
        }
        if(timeTaskPresent && stream != null){
            log.info("Removing stream from FixedPricingTimerTask, Stream name: "+stream.getName() );
            FixedPricingSchedulerServiceUtility.getStreamTaskMap().remove(stream);
        }
    }

    private void scheduleFixedPricing(Organization org , Stream stream) {
        BrokerAdaptorMBean brokerAdaptorMBean = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(org.getShortName());
        if(brokerAdaptorMBean != null && brokerAdaptorMBean.isStreamSupported(stream)) {
            FixedPricingTimerTask fixedPricingTimerTask = FixedPricingSchedulerServiceUtility.getStreamTaskMap().get(stream);
            if (fixedPricingTimerTask == null && stream.isActive() && stream.isFixedPeriodPricingEnabled()) {
                log.info(new StringBuilder(200).append("StreamAddRemoveFunctorC.scheduleFixedPricing:: Stream: ")
                        .append(stream.getName()).append(", Stream virtual server: ").append(stream.getVirtualServerName()).toString());
                FixedPricingSchedulerServiceUtility.scheduleFixedPricingTimerTask(stream);
            }
        }else{
            log.info(new StringBuilder(200).append("StreamAddRemoveFunctorC.scheduleFixedPricing:: skipping fixed pricing scheduling as Stream: ")
                    .append(stream.getName()).append(" is not supported for broker: ").append(org.getShortName()).
                            append(", Stream virtual server: ").append(stream.getVirtualServerName()).toString());
        }
    }

    private void notifyStreamUpdate(Organization org, String streamName ) {
        try {
            if(log.isDebugEnabled()){
                log.debug("SARFC.notifyStreamUpdate: org:"+ org.getShortName() + ", stream:"+ streamName);
            }
            Collection<String> brokers = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizationNames();
            if (brokers != null && brokers.contains(org.getShortName())) {
                String orgName = org.getShortName();
                Notification notification = new Notification(NotificationService.NotificationEvent.UPDATE, "STREAM_EVENT_STATUS", orgName, streamName,  System.currentTimeMillis());
                MMBrokerSubscriptionServiceC.getInstance().notifyStreamUpdate(notification);
            }
        } catch (Exception e) {
            log.error("SARFC.notifyStreamUpdate: Error occurred while notifying stream update changes. org:"+ org.getShortName() + " stream:"+streamName , e);
        }
    }
}
