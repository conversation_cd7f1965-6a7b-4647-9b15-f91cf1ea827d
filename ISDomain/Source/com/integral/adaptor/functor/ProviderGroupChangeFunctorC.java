package com.integral.adaptor.functor;

import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.session.RemoteTransactionNotification;

import java.util.HashMap;


public class ProviderGroupChangeFunctorC implements RemoteTransactionNotification {

    public static Log log = LogFactory.getLog(ProviderGroupChangeFunctorC.class);
    public static final String PROVIDER_SHORTNAME= "providerShortName"; 


    /**
     * Process a remote transaction notification for change Grp mappings for provider adaptors
     *
     * @param props
     */
    public void onCommit(HashMap props) {
        if (log.isInfoEnabled())
            log.info("ProviderGroupChangeFunctorC.onCommit:received JNDI Entry update :" + props);

        try {
            String lp = (String) props.get(PROVIDER_SHORTNAME);

        	ResponseHandlerC.getInstance().reloadHeartBeatDestinations(lp);
            ISFactory.getInstance().getServicProvidersMBean().onNotification();
        } catch (Exception e)
        {
            log.error("Exception happened in processing Stream Add - Remove functor ", e);
        }
    }
}
