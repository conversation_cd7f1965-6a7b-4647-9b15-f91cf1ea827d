package com.integral.adaptor.comm;

import com.integral.adaptor.request.RequestServiceC;
import com.integral.adaptor.request.ejb.RequestService;
import com.integral.alert.AlertLoggerFactory;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.trade.Tenor;
import com.integral.is.ISCommonConstants;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.is.alerttest.AlertCheckC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.*;
import com.integral.is.message.rfs.*;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.persistence.util.LogUtil;
import com.integral.serialization.ISMessageSerializerUtil;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.time.DateTimeFactory;
import com.integral.time.IdcDate;
import com.integral.util.StringUtilC;

import java.lang.reflect.Method;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 7/14/11
 * Time: 10:06 AM
 * Base class for handling requests.
 */
public class BaseRequestHandler {

    protected RequestService remoteRefernence = null;
    protected Log log = LogFactory.getLog(this.getClass());
    private static final String PIPE = "|";

    /**
     * Convert the objStr to the respective object. Invoke the RequestService method depending upon
     * the object type.
     *
     * @param objType
     * @param objStr
     * @return string representation of ResponseMessage
     */
    public String handleRequest(String objType, String objStr) {
        RequestService requestService = null;
        String respStr = null;
        try {
            AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_REQUESTSERVICE_LOOKUP);
            long st = System.currentTimeMillis();
            requestService = getRequestService();
            long et = System.currentTimeMillis();
            if((et - st) > 1) log.info("detReqServ.tt=" + (et - st) + " ms.");
        } catch (Exception ex) {
            MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_REQUESTSERVICE_LOOKUP, this.getClass().getName(), "Request Service Lookup Failed", null);
            ResponseMessage resp = new ResponseMessageC();
            resp.setStatus(ResponseMessage.FAILURE);
            resp.setFailureReason("Request Service Lookup Failed");
            try {
                respStr = resp.getClass().getName() + PIPE + resp.getToString();
            } catch (Exception e) {
            }
        }
        try {
            AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_REQUEST_PROCESS);
            if (requestService != null)
                respStr = processMessage(requestService, objType, objStr);
        } catch (Exception e) {
            log.error("HTTPRequestHandler.handleRequest : Error in processing request [" + objType + "=" + objStr + "]", e);
            AlertLoggerFactory.getMessageLogger().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_REQUEST_PROCESS, this.getClass().getName(), "Error in processing request", null);
            ResponseMessage resp = new ResponseMessageC();
            resp.setStatus(ResponseMessage.FAILURE);
            resp.setFailureReason("Error in processing request");
            try {
                respStr = resp.getClass().getName() + PIPE + resp.getToString();
            } catch (Exception ex) {
            }
        }
        return respStr;
    }

    /**
     * Create object message from objStr and populate it.
     * Invoke the RequestService and return the response message.
     *
     * @param requestService
     * @param objType
     * @param objStr
     * @return responseString
     * @throws Exception
     */
    private String processMessage(RequestService requestService, String objType, String objStr) throws Exception {

        if (objType.equals(TradeRequestC.class.getName())) {
            try {
                LogUtil.setSwitch( true );
                long st = System.currentTimeMillis();
                TradeRequest isMessage = new TradeRequestC();
                isMessage.populateObject(objStr);
                long et = System.currentTimeMillis();
                if ((et - st) > 1) log.info("deserialize TimeTaken=" + (et - st) + " ms");
                ResponseMessage resp = requestService.quoteAccepted(isMessage);
                return resp.getClass().getName() + PIPE + resp.getToString();
            } finally {
                LogUtil.removeSwitch();
            }
        } else if (objType.equals(MarketRateSubscribeC.class.getName())) {
            MarketRateSubscribe isMessage = new MarketRateSubscribeC();
            isMessage.populateObject(objStr);
            updateInstrumentDetailsWithForward(isMessage);
            ResponseMessage resp = requestService.startSubscription(isMessage);
            return resp.getClass().getName() + PIPE + resp.getToString();
        } else if (objType.equals(MarketRateUnsubscribeC.class.getName())) {
            MarketRateUnsubscribe isMessage = new MarketRateUnsubscribeC();
            isMessage.populateObject(objStr);
            updateInstrumentDetailsWithForward(isMessage);
            ResponseMessage resp = requestService.stopSubscription(isMessage);
            return resp.getClass().getName() + PIPE + resp.getToString();
        } else if (objType.equals(LoginMessageC.class.getName())) {
            LoginMessage isMessage = new LoginMessageC();
            isMessage.populateObject(objStr);
            doPreLogin(isMessage);
            ResponseMessage resp = requestService.login(isMessage);
            doPostLogin(isMessage, resp);
            return resp.getClass().getName() + PIPE + resp.getToString();
        } else if (objType.equals(RFSSubscribeC.class.getName()))
        {
            try
            {
                LogUtil.setSwitch( true );
                log.info ( "BaseRequestHandler.rfsSubscribe:: + " + objStr );
                RFSSubscribe isMessage = new RFSSubscribeC ();
                isMessage.populateObject ( objStr );
                ResponseMessage resp = requestService.startRFSSubscription ( isMessage );
                return resp.getClass ().getName () + PIPE + resp.getToString ();
            }
            finally
            {
                LogUtil.removeSwitch ();
            }
        } else if (objType.equals(RFSUnsubscribeC.class.getName())) {
            RFSUnsubscribe isMessage = new RFSUnsubscribeC();
            isMessage.populateObject(objStr);
            ResponseMessage resp = requestService.stopRFSSubscription(isMessage);
            return resp.getClass().getName() + PIPE + resp.getToString();
        } else if (objType.equals(RFSTradeRequestC.class.getName()))
        {
            try
            {
                LogUtil.setSwitch ( true );
                log.info ( "BaseRequestHandler.rfsTradeRequest:: + " + objStr );
                RFSTradeRequest isMessage = new RFSTradeRequestC ();
                isMessage.populateObject ( objStr );
                ResponseMessage resp = requestService.quoteAccepted ( isMessage );
                return resp.getClass ().getName () + PIPE + resp.getToString ();
            }
            finally
            {
                LogUtil.removeSwitch ();
            }
        }
        else if (objType.equals(BrokerOrderRequest.class.getName())) {
            try {
                LogUtil.setSwitch( true );
                BrokerOrderRequest request = MessageFactory.newBrokerOrderRequest();
                ISMessageSerializerUtil.deserialize( request, objStr );
                updateInstrumentDetailsWithForward(request);
                ResponseMessage resp = requestService.submitBrokerOrder( request );
                return resp.getClass().getName() + PIPE + resp.getToString();
            }
            finally {
                LogUtil.removeSwitch();
            }
        }
        else {
            log.warn("HTTPRequestHandler.processMessage : ObjectType " + objType + " sent by Server. Adaptor is unable to handle it.");
        }
        return "";
    }

    private void updateInstrumentDetailsWithForward ( BrokerOrderRequest request )
    {
        Currency varCcy = request.getVariableCurrency();
        Currency baseCcy = request.getBaseCurrency();
        String tenor = request.getTenor();
        Tenor tenorObj = null;
        IdcDate brokenDate = null;
        int settlementType = request.getSettlementType();
		if( baseCcy != null && varCcy != null && settlementType != 0 )
        {
			Currency.SettlementType type = Currency.SettlementType.getType( settlementType );
            CurrencyPair currencyPair = null;
            if ( tenor != null )
            {
                tenorObj = new Tenor( tenor );
                currencyPair = CurrencyFactory.getCurrencyPair ( baseCcy, varCcy, tenorObj, type );
            }
            else
            {
                brokenDate = DateTimeFactory.newDate ( new Date ( request.getValueDateLong () ) );
            }
            if ( currencyPair == null && RuntimeFactory.getServerRuntimeMBean ().isStreamingNonSpotBrokenDateExecutionEnabled ( request.getOrganization () ) )
            {
                currencyPair = CurrencyFactory.getVirtualNonSpotCurrencyPair ( baseCcy, varCcy, tenorObj, brokenDate, type );
            }
            if( currencyPair == null )
            {
                //TODO: raise alert
                log.error ( "BRH.updateInstrumentDetailsWithForward - No virtual currency pair found for brokerOrderRequest=" + request );
                return;
            }
            request.setTenor( Tenor.SPOT );
            request.setSettlementType( 0 );
            request.setVariableCurrency( currencyPair.getVariableCurrency() );
            if( request.getDealtCurrency().isSameAs( varCcy ) )
            {
                request.setDealtCurrency( currencyPair.getVariableCurrency() );
            }
        }
    }

    protected void doPreLogin(LoginMessage message) {
        //do nothing
    }

    protected void doPostLogin(LoginMessage message, ResponseMessage resp) {
        //do nothing
    }

    /**
     * Lookup and return the reference of the Adaptor RequestService.
     *
     * @return reference of RequestService
     * @throws Exception
     */
    private RequestService getRequestService() throws Exception {
        return RequestServiceC.getRequestService();
    }

    // deprecated , not used in provider adaptors
    private String getToString(Object obj) throws Exception {
        try {
            Method method = obj.getClass().getMethod("getToString", (java.lang.Class[]) null);
            String str = (String) method.invoke(obj, (java.lang.Object[]) null);
            return str;
        } catch (NoSuchMethodException nsme) {
            log.error("HTTPRequestHandler.getToString : Method getToString not found for class " + obj.getClass().getName() + nsme);
        } catch (Exception ex) {
            log.error("HTTPRequestHandler.getToString : Error in converting " + obj.getClass().getName() + " to its string representation");
            throw ex;
        }
        return null;
    }

    private void updateInstrumentDetailsWithForward(MarketRateRequest request){
        String tenorStr = request.getTenor();
        String valueDateStr = request.getValueDate ();
        final boolean isForwardTenor = tenorStr != null && request.getSettlementType() != 0 && !Tenor.SPOT.equalsIgnoreCase (tenorStr);
        boolean virtualCcyPairEnabled = RuntimeFactory.getServerRuntimeMBean ().isStreamingNonSpotBrokenDateEnabled ( ReferenceDataCacheC.getInstance ().getOrganization ( request.getOrgShortName () ) );
        if ( !virtualCcyPairEnabled )
        {
            if ( isForwardTenor )
            {
                String varCcy = request.getVariableCcy ();
                Currency.SettlementType type = Currency.SettlementType.getType ( request.getSettlementType () );
                Currency varCurrency = CurrencyFactory.getCurrency ( varCcy );
                Currency forwardCurrency = CurrencyFactory.getCurrency ( varCurrency, new Tenor ( tenorStr ), type );
                if ( forwardCurrency != null )
                {
                    request.setVariableCcy ( forwardCurrency.getName () );
                    request.setSettlementType ( Currency.SettlementType.SPOT.getId () );
                    request.setTenor ( Tenor.SPOT );
                }

            }
        }
        else
        {
            final boolean isBrokenDate = !StringUtilC.isNullOrEmpty ( valueDateStr );
            if ( isForwardTenor || isBrokenDate )
            {
                String varCcy = request.getVariableCcy ();
                Currency.SettlementType type = Currency.SettlementType.getType ( request.getSettlementType () );
                Currency varCurrency = CurrencyFactory.getCurrency ( varCcy );
                if ( isForwardTenor )
                {
                    Tenor tenor = new Tenor ( tenorStr );
                    request.setVariableCcy ( varCurrency.getShortName () );
                    request.setSettlementType ( type.getId () );
                    request.setTenor ( tenor.getName () );
                }
                else
                {
                    try
                    {
                        IdcDate brokenDate = DateTimeFactory.newDate ( valueDateStr, ISCommonConstants.API_DATE_FORMAT );
                        request.setVariableCcy ( varCurrency.getShortName () );
                        request.setSettlementType ( Currency.SettlementType.FORWARD.getId () );
                        request.setTenor ( null );
                        request.setValueDate ( brokenDate.getFormattedDate ( IdcDate.YYYY_MM_DD_HYPHEN ) );
                    }
                    catch ( Exception e )
                    {
                        log.error ( "BRH.updateInstrumentDetailsWithForward - Exception while updating the market rate request=" + request, e );
                    }
                }
            }
        }
    }
}
