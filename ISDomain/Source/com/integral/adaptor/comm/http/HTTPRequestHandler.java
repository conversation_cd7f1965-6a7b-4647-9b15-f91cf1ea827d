package com.integral.adaptor.comm.http;

//Copyright (c) 2001-2006 Integral Development Corp.  All rights reserved.

import com.integral.adaptor.comm.BaseRequestHandler;

/**
 *  This class is used to handle the requests coming from IS through http.
 *  The class is used to convert request message string to the appropriate object and then calling the method of
 *  RequestService.
 * <AUTHOR> Development Corp.
 */

public class HTTPRequestHandler extends BaseRequestHandler
{

	private static HTTPRequestHandler _instance = new HTTPRequestHandler();

	/**
	 * Constructor
	 */
	private HTTPRequestHandler()
	{
	}

	/**
	 *
	 * @return instance
	 */
	public static HTTPRequestHandler getInstance()
	{
		return _instance;
	}
}
