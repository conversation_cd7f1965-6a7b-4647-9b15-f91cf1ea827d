package com.integral.adaptor.comm.imtp;

import com.integral.adaptor.comm.BaseRequestHandler;
import com.integral.imtp.IMTPMessageReceiver;
import com.integral.imtp.message.*;
import com.integral.imtp.session.IMTPSession;
import com.integral.is.alerttest.AlertCheckC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.LoginMessage;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.ResponseMessageC;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.io.IOException;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 5/23/11
 * Time: 4:44 PM
 * This class is used to handle the requests coming from IS through imtp.
 */
public class IMTPRequestHandler extends BaseRequestHandler implements IMTPMessageReceiver {

    public static final String IMTP_SESSION_ID_KEY = "IMTP.Session.Id";
    private static final Log log = LogFactory.getLog(IMTPRequestHandler.class);
    private static final String OBJECT_TYPE = "OBJECTTYPE";

    private final IMTPSession session;

    public IMTPRequestHandler(IMTPSession session) {
        this.session = session;
    }

    public void messageReceived(IMTPMessage message) {
        String respStr = "";
        try {
            AlertCheckC.callback(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_REQUEST_HANDLER);
            if (log.isDebugEnabled()) {
                log.debug("IMTPRequestHandler: messageReceived(" + message + ")");
            }
            String objType = ((IMTPApplicationMessage) message).getProperty(OBJECT_TYPE);
            String objStr = (String) ((IMTPApplicationMessage) message).getApplicationData();
            respStr = handleRequest(objType, objStr);
            if (log.isDebugEnabled()) {
                log.debug("ISRequestProcessorServlet.processRequest : Response string is " + respStr);
            }
        } catch (Exception e) {
            log.error("ISRequestProcessorServlet.processRequest : Error in handling server imtp request ", e);
            MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_REQUEST_HANDLER, "IMTPRequestHandler.messageReceived",
                    "Error in handling server imtp request ", message.toString());
            ResponseMessage resp = new ResponseMessageC();
            resp.setStatus(ResponseMessage.FAILURE);
            try {
                respStr = resp.getClass().getName() + "|" + resp.getToString();
            } catch (Exception ex) {
            }
        }
        IMTPApplicationMessage resp = IMTPMessageFactory.borrowReturnApplicationMessage(message, respStr);
        resp.setAppId(((ApplicationMessage) message).getAppId()); //set the original appId on response for tracking.
        ((PoolableMessage) resp).incrementReference();
        try {
            session.getMessageHandler().sendMessage(resp, false); //No need to persist responses sent through this request handler as this was designed to replace HTTP response.
        } catch (IOException e) {
            log.error("ISRequestProcessorServlet.processRequest : Error in sending server imtp response. Request: " + message + ", Response: " + resp, e);
            MessageLogger.getInstance().log(ISAlertMBean.ALERT_ACTION_ADAPTOR_IMTP_REQUEST_HANDLER, "IMTPRequestHandler.messageReceived",
                    "Error in handling server imtp response. Request: ", message + ", Response: " + resp);
        } finally {
            ((BaseIMTPMessage) resp).decrementReference(); //free the borrowed message here.
        }
    }

    @Override
    protected void doPreLogin(LoginMessage message) {
        super.doPreLogin(message);
        message.setIMTP(true);
    }

    @Override
    protected void doPostLogin(LoginMessage message, ResponseMessage resp) {
        super.doPostLogin(message, resp);
        if (resp.getStatus() == ResponseMessage.SUCCESS) {
            session.getConnection().setAppActive(); //Set the connection APP_Active after a successful Login.
            message.setProperty(IMTP_SESSION_ID_KEY, session.getSessionId());
        }
    }
}
