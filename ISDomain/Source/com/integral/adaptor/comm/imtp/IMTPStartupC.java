package com.integral.adaptor.comm.imtp;

import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.imtp.config.IMTPConfigMBean;
import com.integral.imtp.config.IMTPConfigMBeanC;
import com.integral.imtp.config.IMTPConfigMBeanFactory;
import com.integral.imtp.config.IMTPVersion;
import com.integral.imtp.connection.*;
import com.integral.imtp.session.IMTPSession;
import com.integral.imtp.session.IMTPSessionManager;
import com.integral.imtp.session.IMTPSessionMessageHandler;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.spaces.fx.listener.SpacesTradeListener;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.staging.oms.conn.imtp.IMTPOMSMessageReceiver;
import com.integral.system.runtime.StartupTask;
import com.integral.system.server.VirtualServer;
import com.integral.system.server.VirtualServerType;

import java.io.IOException;
import java.util.Hashtable;

/**
 * Created by IntelliJ IDEA.
 * User: anejaa
 * Date: 5/24/11
 * Time: 10:05 AM
 * A start up class for IMTP protocol. The adaptor starts an acceptor and listens for IMTP connections from IS servers.
 */
public class IMTPStartupC implements StartupTask, IMTPServiceListener {

    private static final Log log = LogFactory.getLog(IMTPStartupC.class);

    /**
     * The namespace on which the trades will be received.
     */
    private static final String TRADE_NAMESPACE = "TOIS.TRADES";

    public String startup(String aName, Hashtable args) throws Exception{
        String result = "Started";
        IMTPConfigMBeanC conf = IMTPConfigMBeanC.getInstance();
        IMTPConfigMBeanFactory.getInstance().setIMTPConfigMBean(conf);
        Acceptor acceptor = IMTPConnectionManager.getInstance().getAcceptor();
        acceptor.registerIMTPServiceListener(this);
        Initiator initiator = IMTPConnectionManager.getInstance().getInitiator();
        initiator.registerIMTPServiceListener(this); //register listeners for both acceptor and initiator service.
        if (AdaptorConfigurationFactory.getAdaptorConfigurationMBean().isIMTPEnabled()) {

            if (conf.getAcceptorPort() != IMTPConfigMBean.ACCEPTOR_PORT_NUM_NOT_SET) {
                String sessionId = IMTPSessionManager.getSessionId(conf);
                try {
                    IMTPSessionManager.getInstance().getSession(sessionId, null); //Load a local session to warm up.
                } catch (IOException e) {
                    log.error("Error loading session:" + sessionId);
                    throw e;
                }
                try {
                    acceptor.bind(); //Safe to call multiple times... would do nothing if already bound.
                    log.info("IMTPStartupC: IMTP Acceptor started on port:" + acceptor.getBoundPort() + ", IMTPVersion:" + IMTPVersion.IMTP_VERSION_ID);
                } catch (Exception e) {
                    log.error("IMTPStartupC: Unable to start IMTP Acceptor on port:" + conf.getAcceptorPort(), e);
                    result = "Not Started";
                    throw e;
                }
            } else {
                log.warn("IMTPStartupC: AcceptorPort not set. IMTP Acceptor not started.");
                result = "Not Started";
            }
        } else {
            log.warn("IMTPStartupC: IMTP Not enabled. IMTP Acceptor not started.");
            result = "Not Started";
        }
        return result;
    }

    public void notifyIMTPServiceEvent(IMTPService service, IMTPServiceEvent event) {
        switch (event.type) {
            case CONNECTION_ESTABLISHED:
                IMTPConnection conn = (IMTPConnection) event.payload;
                log.info("IMTP Acceptor Service: New Connection Accepted from :" + conn.getPeerInfo());
                break;
            case CONNECTION_ACTIVATED:
                IMTPConnection con = (IMTPConnection) event.payload;
                IMTPSession session = con.getSession();
                IMTPSessionMessageHandler messageHandler = session.getMessageHandler();
                messageHandler.registerReceiver(new IMTPRequestHandler(session), "TOIS.MESSAGES"); //Setting listener for Requests.

                log.info("Enabling SpacesTradeListener to listen to messages from: " + TRADE_NAMESPACE);
                messageHandler.registerReceiver(new SpacesTradeListener(), TRADE_NAMESPACE);

				log.info("Enabling OMS IMTP to listen to messages from: " + IMTPOMSMessageReceiver.SELECTOR);
				messageHandler.registerReceiver(new IMTPOMSMessageReceiver(), IMTPOMSMessageReceiver.SELECTOR);

                log.info("IMTP Acceptor Service: New Connection Activated from :" + con.getPeerInfo());
                break;
        }
    }
}
