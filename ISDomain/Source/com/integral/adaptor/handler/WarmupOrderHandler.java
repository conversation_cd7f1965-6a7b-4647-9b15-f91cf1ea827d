package com.integral.adaptor.handler;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.is.message.*;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.adaptor.AdaptorConstantC;

import javax.jms.JMSException;

/**
 * Introduced to by pass the provider specific code in adaptor.
 * This is introduced to avoid writing warmup code for each adaptor in the first release of warmup workflow
 */
public class WarmupOrderHandler
{

	protected static WarmupOrderHandler current = new WarmupOrderHandler();

	protected Log log = LogFactory.getLog(this.getClass());

	private static boolean nextVerify;

	public static WarmupOrderHandler getInstance()
	{
		return current;
	}

	private WarmupOrderHandler()
	{
	}

	/**
	 * Send quote acceptance to provider.
	 *
	 * @param request a TradeRequest object containing quote acceptance information.
	 * @return ResponseMessage contains the status of the method call
	 */
	public ResponseMessage quoteAccepted(TradeRequest request)
	{
		long acceptanceReceivedByAdaptor = System.currentTimeMillis();
		if ( log.isDebugEnabled() )
			log.debug("WarmupOrderHandler.quoteAccepted: TXN-ID - " + request.getTradeId());

		ResponseMessage response = new ResponseMessageC();
		response.setStatus(ResponseMessage.SUCCESS);

		TradeResponse tradeResponse = null;
		if ( !nextVerify )
		{
			tradeResponse = createRejectResponse(request);
			nextVerify = true;
		}
		else
		{
			tradeResponse = createVerifyResponse(request);
			nextVerify = false;
		}
		setTimingInfo(tradeResponse, request, System.currentTimeMillis());
		AsyncSender asynSender = new AsyncSender(tradeResponse);
		asynSender.start();

		return response;
	}

	private class AsyncSender extends Thread
	{
		TradeResponse tradeResponse;

		public AsyncSender(TradeResponse tradeResponse)
		{
			this.tradeResponse = tradeResponse;
		}

		public void run()
		{
			try
			{
				Thread.sleep(2000);
				ResponseHandlerC.getInstance().tradeRecieved(tradeResponse, true);
				log.warn("WarmUpOrderResponseHandler: Trade Response Sent to IS for  TXN-ID - " + tradeResponse.getTradeId());
			}
			catch ( InterruptedException ie )
			{
				ie.printStackTrace();
			}
            catch (JMSException e) {
                e.printStackTrace();
            }

        }
	}

	private TradeVerify createVerifyResponse(TradeRequest request)
	{

		TradeVerify message = MessageFactory.newTradeVerify();
		ISCommonUtilC.setAsWarmUpObject(message);
		message.setProviderTradeId(request.getTradeId());
		message.setTradeId(request.getTradeId());
		message.setStatus(TradeResponse.FILLED);
		message.setBaseCcy(request.getBaseCcy());
		message.setVariableCcy(request.getVariableCcy());
		message.setAcceptedPrice(request.getRate());
		message.setAcceptedAmount(request.getAmount());
		message.setBuySell(request.getBuySell());
		message.setAcceptedSettledAmount(request.getAmount());
		message.setProperty("STREAM", request.getStreamId());
		return message;
	}

	private TradeReject createRejectResponse(TradeRequest request)
	{
		TradeReject message = MessageFactory.newTradeReject();
		ISCommonUtilC.setAsWarmUpObject(message);
		message.setReasonDescription("Rejected during Warmup");
		message.setReasonCode("");
		message.setTradeId(request.getTradeId());
		message.setStatus(TradeResponse.REJECTED);
		message.setBaseCcy(request.getBaseCcy());
		message.setVariableCcy(request.getVariableCcy());
		message.setBuySell(request.getBuySell());
		return message;
	}

	private void setTimingInfo(TradeResponse response, TradeRequest request, long executionReceivedTime)
	{
		if ( request != null )
		{
			Timing timing = response.getTiming();
			if ( response instanceof TradeReject )
			{
				timing.setTime(AdaptorConstantC.EVENT_TIME_DISP_REC_REJ_FROM_PROVIDER, executionReceivedTime);
			}
			else
			{
				timing.setTime(AdaptorConstantC.EVENT_TIME_DISP_REC_VERIFY_FROM_PROVIDER, executionReceivedTime);
			}
			timing.setTime(AdaptorConstantC.EVENT_TIME_DISP_ADAPTER_REC_ACC, request.getTiming().getLongTime(AdaptorConstantC.EVENT_TIME_DISP_ADAPTER_REC_ACC));
			timing.setTime(AdaptorConstantC.EVENT_TIME_DISP_ACC_SENT_TO_PROVIDER, request.getTiming().getLongTime(AdaptorConstantC.EVENT_TIME_DISP_ACC_SENT_TO_PROVIDER));
		}
	}

}
