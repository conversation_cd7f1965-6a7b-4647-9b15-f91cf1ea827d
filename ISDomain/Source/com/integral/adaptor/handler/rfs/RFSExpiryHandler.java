package com.integral.adaptor.handler.rfs;

import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.log.Log;
import com.integral.adaptor.manager.rfs.RFSAdaptorManager;

/**
 *
 * <AUTHOR>
 * This is the class which handles Request Expiry. It calls handleExpiry(RFSSubscribe request) of RFSExpiryEventHandler on expiry of each request.
 * RFSExpiryEventHandler Interface needs to implemented in Adaptor specific code for sending RFS Expiry Message to IS and RFS Unsubscribe Message
 * to Provider etc.. 
 */
public class RFSExpiryHandler
{

	private static RFSExpiryHandler rfsExpHdlr = new RFSExpiryHandler();
	private Map<String, RFSExpiryTask> expRequests = new ConcurrentHashMap(new HashMap<String, RFSExpiryTask>());
	private Log log = com.integral.log.LogFactory.getLog(this.getClass());
	private Timer expTimer = new Timer();

	private RFSExpiryHandler()
	{
	}

	/**
	 *
	 * @return instance of this RFSExpiryHandler
	 */
	public static RFSExpiryHandler getInstance()
	{
		return rfsExpHdlr;
	}
	
	public Map<String, RFSExpiryTask> getExpiryTasks()
	{
		return expRequests;
	}

	/**
	 * Start Request Expiry
	 * @param request
	 * @param providerRFSExpiryTime
	 * @param rfsExpEventHdlr
	 */
	public void startRequestExpiry(RFSSubscribe request, int providerRFSExpiryTime, RFSExpiryEventHandler rfsExpEventHdlr)
	{
		try
		{
			String key = request.getProviderShortName() + "-" + request.getRequestId();
			RFSExpiryTask expTask = null;
			synchronized ( expRequests )
			{
				expTask = expRequests.get(key);
			}
			if ( expTask == null )
			{
				if ( request != null )
				{
					long delay;
					if ( providerRFSExpiryTime < 0 )
					{
						delay = request.getRequestExpiryTimeInSeconds() * 1000;
					}
					else
					{
						delay = request.getRequestExpiryTimeInSeconds() < providerRFSExpiryTime ? request.getRequestExpiryTimeInSeconds() * 1000 : providerRFSExpiryTime * 1000;
					}
					expTask = new RFSExpiryTask(request.getRequestId(), request.getProviderShortName(), delay, rfsExpEventHdlr);
					synchronized ( expRequests )
					{
						expRequests.put(key, expTask);
					}
					expTimer.schedule(expTask, delay);
					log.warn("RFSExpiryTask.startRequestExpiry: RequestExpiry Timer scheduled for " + key + " with Delay=" + delay);
				}
			}
			else
				log.warn("RFSExpiryTask.startRequestExpiry: Request already added for " + key);
		}
		catch ( Exception e )
		{
			e.printStackTrace();
		}
	}

	/**
	 *
	 * @param provider
	 * @param requestId
	 * @return true if the Expiry task exists
	 */
	public boolean isRequestExists(String provider, String requestId)
	{
		return expRequests.containsKey(provider + "-" + requestId);
	}

	/**
	 * Stop the Request
	 * @param provider
	 * @param requestId
	 */
	public void stopRequestExpiry(String provider, String requestId)
	{
		String key = provider + "-" + requestId;
		try
		{
			RFSExpiryTask expTask = null;
			synchronized ( expRequests )
			{
				expTask = expRequests.remove(key);
			}
			if ( expTask == null )
			{
				log.warn("RFSExpiryHandler.stopRequestExpiry : Expiry Task is Null for " + key);
			}
			else
			{
				log.warn("RFSExpiryHandler.stopRequestExpiry : Removed Expiry Task for " + key);
				if ( expTask.cancel() )
				{
					log.warn("RFSExpiryHandler.stopRequestExpiry : Cancelled Expiry Task for " + key);
				}
			}
		}
		catch ( Exception e )
		{
			log.error("RFSExpiryHandler.stopRequestExpiry : Exception while stopping Expiry for " + key);
		}
	}

	/**
	 * Cancel All RFS Request Expiry
	 */
	public void cancelAllRequestExpiry()
	{
		RFSExpiryTask expTask = null;
		synchronized ( expRequests )
		{
			for ( Map.Entry<String, RFSExpiryTask> key : expRequests.entrySet() )
			{
				expTask = key.getValue();
				if ( expTask != null )
				{
					stopRequestExpiry(expTask.providerName, expTask.requestId);
				}
			}
		}
	}

	/**
	 * Expire All RFS Requests. Send RFS Expired msg to IS and Unsubscription msg to Provider as well.
	 */
	public void expireAllRequests()
	{
		RFSExpiryTask expTask = null;
		synchronized ( expRequests )
		{
			for ( Map.Entry<String, RFSExpiryTask> key : expRequests.entrySet() )
			{
				expTask = expRequests.remove(key.getKey());
				if ( expTask != null )
				{
					log.warn("RFSExpiryHandler.expireAllRequests : Removed Expiry Task for " + key.getKey());
					if ( expTask.cancel() )
					{
						log.warn("RFSExpiryHandler.stopRequestExpiry : Cancelled Expiry Task for " + key.getKey());
					}
					if ( expTask.rfsExpEventHdlr != null )
					{
						expireRequest(expTask.providerName, expTask.requestId, expTask.rfsExpEventHdlr, true);
					}
				}
			}
		}
	}

	/**
	 * Expire All RFS Requests. unsubscribe flag indicates whether unsubscription needs to be sent or not
	 * @param unsubscribe
	 */
	public void expireAllRequests(boolean unsubscribe)
	{
		RFSExpiryTask expTask = null;
		synchronized ( expRequests )
		{
			for ( Map.Entry<String, RFSExpiryTask> key : expRequests.entrySet() )
			{
				expTask = expRequests.remove(key.getKey());
				if ( expTask != null )
				{
					log.warn("RFSExpiryHandler.expireAllRequests : Removed Expiry Task for " + key.getKey());
					if ( expTask.cancel() )
					{
						log.warn("RFSExpiryHandler.stopRequestExpiry : Cancelled Expiry Task for " + key.getKey());
					}
					if ( expTask.rfsExpEventHdlr != null )
					{
						expireRequest(expTask.providerName, expTask.requestId, expTask.rfsExpEventHdlr, unsubscribe);
					}
				}
			}
		}
	}

	/**
	 * Expire All RFS Requests. streamId indicateds particular stream.
	 * unsubscribe flag indicates whether unsubscription needs to be sent or not
	 * @param unsubscribe
	 * @param streamId
	 */
	public void expireAllRequests(String streamId, boolean unsubscribe)
	{
		RFSExpiryTask expTask = null;

		if ( streamId != null )
		{
			synchronized ( expRequests )
			{
				for ( Map.Entry<String, RFSExpiryTask> key : expRequests.entrySet() )
				{
					expTask = expRequests.remove(key.getKey());
					if ( expTask != null )
					{
						RFSSubscribe req = RFSAdaptorManager.getInstance().getRFSSubscribe(expTask.requestId, expTask.providerName);
						if ( req != null )
						{
							if ( streamId.equalsIgnoreCase(req.getStreamId()) )
							{
								expTask = expRequests.remove(key.getKey());
								log.warn("RFSExpiryHandler.expireAllRequests : Removed Expiry Task for " + key.getKey());
								if ( expTask.cancel() )
								{
									log.warn("RFSExpiryHandler.stopRequestExpiry : Cancelled Expiry Task for " + key.getKey());
								}
								if ( expTask.rfsExpEventHdlr != null )
								{
									expireRequest(req, expTask.rfsExpEventHdlr, unsubscribe);
								}
							}
						}
					}
				}
			}
		}
	}

	/**
	 *
	 * @param providerName
	 * @param requestId
	 */
	private void removeExpiryTask(String providerName, String requestId)
	{
		String key = providerName + "-" + requestId;
		synchronized ( expRequests )
		{
			Object obj = expRequests.remove(key);
			if ( obj != null )
			{
				log.warn("RFSExpiryHandler.removeExpiryTask : Removed Expiry Task for " + key);
			}
		}
	}

	/**
	 *
	 * @param providerName
	 * @param requestId
	 * @return Net Expiry Time of RFS Subscribe request
	 */
	public int getNetRequestExpiryTime(String providerName, String requestId)
	{
		String key = providerName + "-" + requestId;
		try
		{
			RFSExpiryTask expTask = null;
			synchronized ( expRequests )
			{
				expTask = expRequests.get(key);
			}
			if ( expTask == null )
			{
				return 0;
			}
			else
			{
				long result = expTask.expiryTime - System.currentTimeMillis();
				if ( result < 0 )
					result = 0;
				return (int) (result / 1000);
			}
		}
		catch ( Exception e )
		{
			e.printStackTrace();
			return 0;
		}
	}

	/**
	 *
	 * @param req
	 * @param rfsExpiryEventHandler
	 * @param unsubscribe
	 */
	private void expireRequest(RFSSubscribe req, RFSExpiryEventHandler rfsExpiryEventHandler, boolean unsubscribe)
	{
		String requestId = req.getRequestId();
		String providerName = req.getProviderShortName();
		synchronized ( RFSAdaptorManager.getInstance().getLock(requestId, providerName) )
		{
			rfsExpiryEventHandler.handleExpiry(req, unsubscribe);
			RFSAdaptorManager.getInstance().removeRFSSubscribe(requestId, providerName);
			RFSAdaptorManager.getInstance().removeLock(requestId, providerName);
		}
	}

	/**
	 *
	 * @param providerName
	 * @param requestId
	 * @param rfsExpiryEventHandler
	 * @param unsubscribe
	 */
	private void expireRequest(String providerName, String requestId, RFSExpiryEventHandler rfsExpiryEventHandler, boolean unsubscribe)
	{
		synchronized ( RFSAdaptorManager.getInstance().getLock(requestId, providerName) )
		{
			RFSSubscribe req = RFSAdaptorManager.getInstance().getRFSSubscribe(requestId, providerName);
			if ( req != null )
			{
				rfsExpiryEventHandler.handleExpiry(req, unsubscribe);
				RFSAdaptorManager.getInstance().removeRFSSubscribe(requestId, providerName);
			}
			RFSAdaptorManager.getInstance().removeLock(requestId, providerName);
		}
	}

	private class RFSExpiryTask extends TimerTask
	{
		private String requestId = null;
		private String providerName = null;
		private long expiryTime;
		private RFSExpiryEventHandler rfsExpEventHdlr;

		public RFSExpiryTask(String requestId, String providerName, long validTime, RFSExpiryEventHandler rfsExpEventHdlr)
		{
			this.requestId = requestId;
			this.providerName = providerName;
			this.expiryTime = validTime + System.currentTimeMillis();
			this.rfsExpEventHdlr = rfsExpEventHdlr;
		}

		public void run()
		{
			if ( rfsExpEventHdlr != null )
			{
				expireRequest(providerName, requestId, rfsExpEventHdlr, true);
				removeExpiryTask(providerName, requestId);
			}
		}
		
		public String toString()
		{
			return "RequestExpiryTask- RequestId=" + requestId + ", provider=" + providerName + ",ExpiryTime=" + expiryTime + ",RFSExpiryEventHandler" + rfsExpEventHdlr;
		}
	}
}
