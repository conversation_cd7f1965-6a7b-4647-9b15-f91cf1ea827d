package com.integral.adaptor.handler.rfs;

import com.integral.is.message.rfs.RFSSubscribe;

/**
 *
 * <AUTHOR>
 * interface to be implemented by each Provider. On RFS Request expiry, handleExpiry method will be called from 
 * RFSExpiryHandler class. Do adaptor specific processing like sending RFS Expiry Response to IS, RFS Unsubsribe to Provider based
 * on the flag unsubscribe .
 */

public interface RFSExpiryEventHandler
{

	void handleExpiry(RFSSubscribe req, boolean unsubscribe);
}
