package com.integral.adaptor.rate.filter.config;

import com.integral.adaptor.rate.filter.RateFilterManager;
import com.integral.adaptor.rate.filter.cache.RateCache;
import com.integral.adaptor.rate.filter.cache.RateCacheC;
import com.integral.adaptor.rate.filter.cache.SingleStreamRateCacheC;
import com.integral.adaptor.rate.filter.model.*;
import com.integral.is.message.MarketRate;

public class RateFilterFactory {
    protected static RateFilterFactory current = new RateFilterFactory();

    FilterMBean config = null;

    /**
     * Default constructor.
     */
    private RateFilterFactory() {
    }

    /**
     * Returns an instance of the factory.
     *
     * @return singleton instance of factory
     */
    public static RateFilterFactory getInstance() {
        return current;
    }

    public FilterMBean getRateFilterMBean() {
        if (config == null) {
            config = new FilterConfiguration();
        }
        return config;
    }

    public RateFilterManager newRateFilterManager(boolean supportSingleStream) {
        return new RateFilterManager(supportSingleStream);
    }

    public RateFilterManager newRateFilterManager() {
        return newRateFilterManager(false);
    }

    public RateFilterManager newRateFilterManager(RateFilterParameters rateFilterParams, boolean supportSingleStream) {
        return new RateFilterManager(rateFilterParams, supportSingleStream);
    }

    public RateFilterManager newRateFilterManager(RateFilterParameters rateFilterParams) {
        return newRateFilterManager(rateFilterParams, false);
    }

    public ProviderRateFilter<MarketRate, RateFilterParameters> newMaxSpreadFilter(boolean singleStreamSupported) {
        return new MaxSpreadFilterC();
    }

    public ProviderRateFilter<MarketRate, RateFilterParameters> newMaxPercentPipsDeviationFilter(boolean singleStreamSupported) {
        return new MaxPercentPipsDeviationFilterC(newRateFilterCache(singleStreamSupported));
    }

    public ProviderRateFilter<MarketRate, RateFilterParameters> newMaxPipsDeviationFilter(boolean singleStreamSupported) {
        return new MaxPipsDeviationFilterC(newRateFilterCache(singleStreamSupported));
    }

    public ProviderRateFilter<MarketRate, RateFilterParameters> newMaxSpreadFilter() {
        return newMaxSpreadFilter(false);
    }

    public ProviderRateFilter<MarketRate, RateFilterParameters> newMaxPercentPipsDeviationFilter() {
        return newMaxPercentPipsDeviationFilter(false);
    }

    public ProviderRateFilter<MarketRate, RateFilterParameters> newMaxPipsDeviationFilter() {
        return newMaxPipsDeviationFilter(false);
    }

    public RateCache newRateFilterCache(boolean singleStreamSupported) {
        return singleStreamSupported ? new SingleStreamRateCacheC() : new RateCacheC();
    }

    public RateFilterParameters newDefaultRateFilterParameters() {
        return new DefaultRateFilterParametersC();
    }
}

