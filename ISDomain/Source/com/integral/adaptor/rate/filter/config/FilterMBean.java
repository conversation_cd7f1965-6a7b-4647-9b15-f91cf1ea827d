package com.integral.adaptor.rate.filter.config;

import com.integral.system.configuration.IdcMBean;

public interface FilterMBean extends IdcMBean {
	
	//constants 
	//public String ENABLED = ".Enabled";

	//public String PREFIX = "Idc.RateFilter";

    /*public String MAX_SPREAD_FILTER = "MaximumSpread";

    public String MAX_PIPS_DEVIATION_FILTER = "MaximumPipsDeviation";

    public String MAX_PERCENT_PIPS_DEVIATION_FILTER = "MaximumPipsPercent"; */

    public final String STANDARD_QUOTE_CONVENTION = "STDQOTCNV";

    public String IDC_IS_RATEFILTER_ENABLE = "Idc.RateFilter.Enabled";

    public String IDC_IS_ACTIVE_FILTERS = "Idc.RateFilter.ActiveFilters";

    public String IDC_IS_RATEFILTER_IS_MAXSPREAD_FILTER_ENABLED = "Idc.RateFilter.Is.MaximumSpread.Filter.Enabled";

    public String IDC_IS_RATEFILTER_IS_MAXPIPSDEVIATION_FILTER_ENABLED = "Idc.RateFilter.Is.MaximumPipsDeviation.Filter.Enabled";

    public String IDC_IS_RATEFILTER_IS_MAXPIPSPERCENTDEVIATION_FILTER_ENABLED = "Idc.RateFilter.Is.MaximumPipsPercent.Filter.Enabled";

    public String IDC_IS_RATEFILTER_IS_STALENESS_FILTER_ENABLED = "Idc.RateFilter.Is.StalenessCheck.Filter.Enabled";

    public String IDC_IS_RATEFILTER_MAXSPREAD = "Idc.RateFilter.MaximumSpread";

    public String IDC_IS_RATEFILTER_MAXPIPSDEVIATION = "Idc.RateFilter.MaximumPipsDeviation";

    public String IDC_IS_RATEFILTER_MAXPIPSPERCENTDEVIATION = "Idc.RateFilter.MaximumPipsPercent";
    
    public String IDC_IS_RATEFILTER_STALENESS = "Idc.RateFilter.StalenessCheck";
    
    public String IDC_IS_RATEFILTER_QUOTECONVENTION = "Idc.RateFilter.QuoteConvention";
    
    public String IDC_IS_RATEFILTER_RESET_CACHE = "Idc.RateFilter.ResetCache";

    public boolean isMaxSpreadFilterEnabled();

    public boolean isMaxPipsDeviationFilterEnabled();

    public boolean isMaxPercentPipsDeviationFilterEnabled();

    public boolean isFiltersEnabled();
    
    public String getQuoteConvention();

	public boolean isStalenessCheckEnabled();
	
	public double getMaxSpread();

	public double getMaximumPipsDeviation();

	public double getMaximumPercentDeviation();

	public double getStalenessInterval();

    public double getMaxSpread(String ccyPair);

    public double getMaximumPipsDeviation(String ccyPair);

    public double getMaximumPercentDeviation(String ccyPair);

    public double getStalenessInterval(String ccyPair);
}
