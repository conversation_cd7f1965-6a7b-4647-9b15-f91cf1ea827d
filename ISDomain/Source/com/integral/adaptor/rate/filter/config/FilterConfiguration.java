package com.integral.adaptor.rate.filter.config;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.IdcMBeanC;

import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

@SuppressWarnings("serial")
public class FilterConfiguration extends IdcMBeanC implements FilterMBean {

    protected Log log = LogFactory.getLog(this.getClass());

    private boolean isFiltersEnabled;
    private boolean isMaxPercentPipsDeviationFilterEnabled;
    private boolean isMaxPipsDeviationFilterEnabled;
    private boolean isMaxSpreadFilterEnabled;
    private boolean isStalenessCheckEnabled;

    private String quoteConvention;
    private double maxSpread;
    private double maxPipsDeviation;
    private double maxPercentDeviation;
    private double stalenessInterval;

    private Map<String, Double> maxSpreads;
    private Map<String, Double> maxPipsDeviations;
    private Map<String, Double> maxPercentDeviations;
    private Map<String, Double> stalenessIntervals;

    protected FilterConfiguration() {
        super("com.integral.adaptor.rate.filter.config.IdcRateFilterMBean");
        initialize();
    }

    public void initialize() {
        isFiltersEnabled = getBooleanProperty(IDC_IS_RATEFILTER_ENABLE, false);
        isMaxPercentPipsDeviationFilterEnabled = getBooleanProperty(IDC_IS_RATEFILTER_IS_MAXPIPSPERCENTDEVIATION_FILTER_ENABLED, true);
        isMaxPipsDeviationFilterEnabled = getBooleanProperty(IDC_IS_RATEFILTER_IS_MAXPIPSDEVIATION_FILTER_ENABLED, true);
        isMaxSpreadFilterEnabled = getBooleanProperty(IDC_IS_RATEFILTER_IS_MAXSPREAD_FILTER_ENABLED, true);
        isStalenessCheckEnabled = getBooleanProperty(IDC_IS_RATEFILTER_IS_STALENESS_FILTER_ENABLED, true);
        quoteConvention = getStringProperty(IDC_IS_RATEFILTER_QUOTECONVENTION, STANDARD_QUOTE_CONVENTION);
        maxSpread = getDoubleProperty(IDC_IS_RATEFILTER_MAXSPREAD, -1);
        maxPipsDeviation = getDoubleProperty(IDC_IS_RATEFILTER_MAXPIPSDEVIATION, -1);
        maxPercentDeviation = getDoubleProperty(IDC_IS_RATEFILTER_MAXPIPSPERCENTDEVIATION, -1);
        stalenessInterval = getDoubleProperty(IDC_IS_RATEFILTER_STALENESS, -1);

        initializeMaxSpreads();
        initializeMaxPipsDeviations();
        initializeMaxPercentDeviations();
        initializeStalenessIntervals();
    }

    private void initializeStalenessIntervals() {
        if (stalenessIntervals == null) {
            stalenessIntervals = new ConcurrentHashMap<String, Double>(10);
        }

        stalenessIntervals.clear();
        Properties properties = getPropertiesWithPrefix(IDC_IS_RATEFILTER_STALENESS);

        for (Map.Entry<Object, Object> entry : properties.entrySet()) {
            String key = (String) entry.getKey();

            if (!IDC_IS_RATEFILTER_STALENESS.equals(key)) {
                String ccyPair = key.substring(IDC_IS_RATEFILTER_STALENESS.length() + 1);
                String value = entry.getValue() == null || ((String)entry.getValue()).trim().equals("") ? "-1" : (String)entry.getValue();
                stalenessIntervals.put(ccyPair, Double.valueOf(value));
            }
        }
    }

    private void initializeMaxPercentDeviations() {
        if (maxPercentDeviations == null) {
            maxPercentDeviations = new ConcurrentHashMap<String, Double>(10);
        }

        maxPercentDeviations.clear();
        Properties properties = getPropertiesWithPrefix(IDC_IS_RATEFILTER_MAXPIPSPERCENTDEVIATION);

        for (Map.Entry<Object, Object> entry : properties.entrySet()) {
            String key = (String) entry.getKey();

            if (!IDC_IS_RATEFILTER_MAXPIPSPERCENTDEVIATION.equals(key)) {
                String ccyPair = key.substring(IDC_IS_RATEFILTER_MAXPIPSPERCENTDEVIATION.length() + 1);
                String value = entry.getValue() == null || ((String)entry.getValue()).trim().equals("") ? "-1" : (String)entry.getValue();
                maxPercentDeviations.put(ccyPair, Double.valueOf(value));
            }
        }
    }

    private void initializeMaxPipsDeviations() {
        if (maxPipsDeviations == null) {
            maxPipsDeviations = new ConcurrentHashMap<String, Double>(10);
        }

        maxPipsDeviations.clear();
        Properties properties = getPropertiesWithPrefix(IDC_IS_RATEFILTER_MAXPIPSDEVIATION);

        for (Map.Entry<Object, Object> entry : properties.entrySet()) {
            String key = (String) entry.getKey();

            if (!IDC_IS_RATEFILTER_MAXPIPSDEVIATION.equals(key)) {
                String ccyPair = key.substring(IDC_IS_RATEFILTER_MAXPIPSDEVIATION.length() + 1);
                String value = entry.getValue() == null || ((String)entry.getValue()).trim().equals("") ? "-1" : (String)entry.getValue();
                maxPipsDeviations.put(ccyPair, Double.valueOf(value));
            }
        }
    }

    private void initializeMaxSpreads() {
        if (maxSpreads == null) {
            maxSpreads = new ConcurrentHashMap<String, Double>(10);
        }

        maxSpreads.clear();
        Properties properties = getPropertiesWithPrefix(IDC_IS_RATEFILTER_MAXSPREAD);

        for (Map.Entry<Object, Object> entry : properties.entrySet()) {
            String key = (String) entry.getKey();

            if (!IDC_IS_RATEFILTER_MAXSPREAD.equals(key)) {
                String ccyPair = key.substring(IDC_IS_RATEFILTER_MAXSPREAD.length() + 1);
                String value = entry.getValue() == null || ((String)entry.getValue()).trim().equals("") ? "-1" : (String)entry.getValue();
                maxSpreads.put(ccyPair, Double.valueOf(value));
            }
        }
    }

    public boolean isFiltersEnabled() {
        return isFiltersEnabled;
    }

    public boolean isMaxPercentPipsDeviationFilterEnabled() {
        return isMaxPercentPipsDeviationFilterEnabled;
    }

    public boolean isMaxPipsDeviationFilterEnabled() {
        return isMaxPipsDeviationFilterEnabled;
    }

    public boolean isMaxSpreadFilterEnabled() {
        return isMaxSpreadFilterEnabled;
    }

    public boolean isStalenessCheckEnabled() {
        return isStalenessCheckEnabled;
    }

    public String getQuoteConvention() {
        return quoteConvention;
    }

    public double getMaxSpread() {
        return maxSpread;
    }

    public double getMaxSpread(String ccyPair) {
        Double val = maxSpreads.get(ccyPair);
        return val == null ? -1 : val;
    }

    public double getMaximumPipsDeviation() {
        return maxPipsDeviation;
    }

    public double getMaximumPipsDeviation(String ccyPair) {
        Double val = maxPipsDeviations.get(ccyPair);
        return val == null ? -1 : val;
    }

    public double getMaximumPercentDeviation() {
        return maxPercentDeviation;
    }

    public double getMaximumPercentDeviation(String ccyPair) {
        Double val = maxPercentDeviations.get(ccyPair);
        return val == null ? -1 : val;
    }

    public double getStalenessInterval() {
        return stalenessInterval;
    }

    public double getStalenessInterval(String ccyPair) {
        Double val = stalenessIntervals.get(ccyPair);
        return val == null ? -1 : val;
    }

    protected String getKey(String propertyName, String detail) {
        final String key;
        if (detail == null) {
            key = propertyName;
        } else {
            StringBuilder sb = new StringBuilder(53).append(propertyName).append('.').append(detail);
            key = sb.toString();
        }
        return key;
    }

}
