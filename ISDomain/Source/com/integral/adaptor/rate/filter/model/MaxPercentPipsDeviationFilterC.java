package com.integral.adaptor.rate.filter.model;

import com.integral.adaptor.rate.filter.FilterLogger;
import com.integral.adaptor.rate.filter.cache.RateCache;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.is.message.MarketRate;
import com.integral.util.MathUtilC;

public class MaxPercentPipsDeviationFilterC extends MaxPipsDeviationFilterC {
	public static final String name = "MaximumPipsPercent";

    public MaxPercentPipsDeviationFilterC(RateCache cache) {
        super(cache);
    }

    public boolean isFiltered(MarketRate rate, RateFilterParameters parameters) {
        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("MaxPercentPipsDeviationFilterC.isFiltered");
            sbf.append(name).append(' ');
            sbf.append(rate.getBaseCcy()).append(rate.getVariableCcy()).append(' ');
            sbf.append(rate.getProviderShortName()).append(' ');
            sbf.append(rate.getStreamId()).append(' ');
            sbf.append(rate.isStale()).append(' ');
            sbf.append(isFilterEnabled(parameters)).append(' ');
            log.debug(sbf.toString());
        }

        if (!isFilterEnabled(parameters)) {
            return true;
        }

        String ccyPair = CurrencyFactory.getCurrencyPairName(rate.getBaseCcy(), rate.getVariableCcy());
        String stream = rate.getStreamId();

        if (rate.isStale()) {
            rateFilterCache.remove(stream, ccyPair);
            return true;
        }

        MarketRate oldProviderPrice = rateFilterCache.getCachedRate(stream, ccyPair);

        if (oldProviderPrice == null) {
            rateFilterCache.replace(rate, stream, ccyPair);
            return true;
        }

        double oldBid = oldProviderPrice.getBidRate();
        double oldOffer = oldProviderPrice.getOfferRate();
        double newBid = rate.getBidRate();
        double newOffer = rate.getOfferRate();

        //Check deviation on bid and offer side.
        if (isDeviated(oldBid, newBid, ccyPair, parameters) || isDeviated(oldOffer, newOffer, ccyPair, parameters)) {
            FilterLogger.getInstance().logDeviatedRate(name, rate, oldProviderPrice, parameters.getMaxPercentDeviation(ccyPair));
            return false;
        }

        rateFilterCache.replace(rate, stream, ccyPair);
        return true;
    }

    protected boolean isFilterEnabled(RateFilterParameters parameters) {
        return parameters.isMaxPercentDeviationFilterEnabled();
    }

    protected boolean isDeviated(double oldRate, double newRate, String ccyPair, RateFilterParameters parameters) {
        double maxPercentDeviation = parameters.getMaxPercentDeviation(ccyPair);

        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("MaxPercentPipsDeviationFilterC.isDeviated");
            sbf.append(name).append(' ');
            sbf.append(oldRate).append(' ').append(newRate).append(' ');
            sbf.append(maxPercentDeviation).append(' ');
            log.debug(sbf.toString());
        }

        if (maxPercentDeviation != RateFilterParameters.DEFAULT_VALUE &&
                !isZeroPrice(oldRate, newRate) && isDeviated(oldRate, newRate, maxPercentDeviation)) {
            return true;
        }

        return false;
    }

    private boolean isDeviated(double oldRate, double newRate, double maxPercentDeviation) {
        return Math.abs( MathUtilC.subtract(oldRate, newRate) ) * 100 / oldRate > maxPercentDeviation;
    }
    
    @Override
    public String getName(){
    	return name;
    }
}
