package com.integral.adaptor.rate.filter.model;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.message.MarketRate;

import java.util.List;

/**
 *
 */

public interface RateFilterParameters {

    public static final int DEFAULT_VALUE = -1;

    /**
     * Returns true if max pips deviation filter is enabled.
     *
     * @return
     */
    boolean isMaxPipsDeviationFilterEnabled();

    /**
     * Sets max pips deviation filter enabled
     *
     * @param maxPipsDeviationFilterEnabled
     */
    void setMaxPipsDeviationFilterEnabled(boolean maxPipsDeviationFilterEnabled);

    /**
     * Returns absolute value of max pips deviation. It will return the DEFAULT_VALUE
     * for empty value.
     *
     * @return
     * @see #DEFAULT_VALUE
     */
    double getMaxPipsDeviation();

    /**
     * Sets absolute value of max pips deviation. Set the DEFAULT_VALUE for
     * empty value.
     *
     *
     * @param maxPipsDeviation
     * @see #DEFAULT_VALUE
     */
    void setMaxPipsDeviation( double maxPipsDeviation );

    /**
     * Returns true if max pips percent deviation filter is enabled
     *
     * @return
     */
    boolean isMaxPercentDeviationFilterEnabled();

    /**
     * Sets max pips percent deviation filter enabled
     *
     * @param maxPercentDeviationFilterEnabled
     *
     */
    void setMaxPercentDeviationFilterEnabled(boolean maxPercentDeviationFilterEnabled);

    /**
     * Returns percentage value of max pips deviation. It will return the DEFAULT_VALUE
     * for empty value.
     *
     * @return
     * @see #DEFAULT_VALUE
     */
    double getMaxPercentDeviation();

    /**
     * Sets percentage value of max percent deviation. Set the DEFAULT_VALUE for
     * empty value.
     *
     *
     * @param maxPercentDeviation
     * @see #DEFAULT_VALUE
     */
    void setMaxPercentDeviation(double maxPercentDeviation);

    /**
     * Returns true if Max Spread Filter is enabled .
     *
     * @return
     */
    boolean isMaxSpreadFilterEnabled();

    /**
     * Sets max spread filter enabled .
     *
     * @param maxSpreadFilterEnabled
     */
    void setMaxSpreadFilterEnabled(boolean maxSpreadFilterEnabled);

    /**
     * Returns maximum allowed spread between price. It will return the DEFAULT_VALUE
     * for empty value.
     *
     * @return
     * @see #DEFAULT_VALUE
     */
    double getMaxSpread();

    /**
     * Sets maximum allowed spread between price. Set the DEFAULT_VALUE for
     * empty value.
     *
     *
     * @param maxSpread
     * @see #DEFAULT_VALUE
     */
    void setMaxSpread( double maxSpread );

    /**
     * Returns max pips deviation for the specified currency pair from the sorted list of SingleProviderRateFilterCcyPairParams.
     * If deviation is not defined under the currency pair and currency pair group, it will return the default maxPipsDeviation
     * value.
     *
     *
     * @param ccyPair
     * @return
     */
    double getMaxPipsDeviation(String ccyPair);

    /**
     * Returns max percent deviation for the specified currency pair from the sorted list of SingleProviderRateFilterCcyPairParams.
     * If deviation is not defined under the currency pair and currency pair group, it will return the default maxPercentDeviation
     * value.
     *
     *
     * @param ccyPair
     * @return
     */
    double getMaxPercentDeviation(String ccyPair);

    /**
     * Returns max spread for the specified currency pair from the sorted list of SingleProviderRateFilterCcyPairParams.
     * If spread is not defined under the currency pair and currency pair group, it will return the default maxSpread
     * value.
     *
     *
     * @param ccyPair
     * @return
     */
    double getMaxSpread(String ccyPair);

    /**
     * Returns true if staleness check is enabled.
     * @return
     */
    boolean isStalenessCheckEnabled();

    /**
     * Returns true if staleness check is enabled.
     * @param stalenessCheckEnabled
     */
    void setStalenessCheckEnabled(boolean stalenessCheckEnabled);

    /**
     * Returns true if filters are enabled.
     * @return
     */
    boolean isFiltersEnabled();


    double getStalenessInterval();

    double getStalenessInterval(String ccyPair);

    FXRateConvention getFXRateConvention();

    void setMaxPipsDeviation(String ccyPair, double maxPipsDeviation);

    void setMaxPercentDeviation(String ccyPair, double maxPercentDeviation);

    void setMaxSpread(String ccyPair, double maxSpread);

    void setStalenessInterval(double stalenessInterval);

    void setStalenessInterval(String ccyPair, double stalenessInterval);

    void setFiltersEnabled(boolean filtersEnabled);

    void setFXRateConvention(FXRateConvention rateConv);
}
