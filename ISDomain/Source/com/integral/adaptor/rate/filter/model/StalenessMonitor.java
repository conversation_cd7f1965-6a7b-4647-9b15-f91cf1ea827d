package com.integral.adaptor.rate.filter.model;

import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.config.AdaptorConfigurationMBean;
import com.integral.adaptor.rate.filter.config.RateFilterFactory;
import com.integral.adaptor.rate.filter.config.FilterMBean;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class StalenessMonitor
{
	static StalenessMonitor instance = new StalenessMonitor();
	ConcurrentHashMap<String, Task> tasks = new ConcurrentHashMap<String, Task>();
	ScheduledThreadPoolExecutor monitor = new ScheduledThreadPoolExecutor(50);
	FilterMBean config = RateFilterFactory.getInstance().getRateFilterMBean();
	AdaptorConfigurationMBean configMbean = AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
	Log log = LogFactory.getLog(this.getClass());

	private StalenessMonitor()
	{
	}

	public static StalenessMonitor getInstance()
	{
		return instance;
	}

	/**
	 * Start monitoring currency pair for staleness.
	 * @param ccyPair
	 * @param stream
	 */
	//Key is EUR/USDStream1
	public void start(String ccyPair, String stream)
	{
		if(config.isFiltersEnabled() && config.isStalenessCheckEnabled())
		{
			StringBuilder key = new StringBuilder(12);
			key.append(ccyPair);
			if ( stream != null && !"null".equalsIgnoreCase(stream) )
				key.append(stream);
			Task task = new Task(key.toString());
			RateFilterParameters params = new DefaultRateFilterParametersC();
			long interval = (long) params.getStalenessInterval(ccyPair);
			Task result = tasks.putIfAbsent(key.toString(), task);
			if( result == null ) //It is possible that multiple subscriptions may hit adaptor simultaneously
			{
				ScheduledFuture t1 = monitor.schedule(task, interval, TimeUnit.SECONDS);
				task.setScheduledFuture(t1);
			}
		}
		else
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("StalenessMonitor.start - Staleness check is not enabled. Timer not started for Key -" + ccyPair + stream);
			}
		}
	}

	/**
	 * Stop monitoring currency pair for staleness
	 * @param ccyPair
	 * @param stream
	 */
	//Key is EUR/USDStream1
	public void stop(String ccyPair, String stream)
	{
//		StringBuilder key = new StringBuilder(12);
//		key.append(ccyPair);
//		if ( stream != null && !"null".equalsIgnoreCase(stream) )
//			key.append(stream);
//		Task task = tasks.remove(key.toString());
//		if ( task != null )
//		{
//			task.getScheduledFuture().cancel(true);
//			this.monitor.remove(task);
//		}
//		if ( log.isDebugEnabled() )
//		{
//			log.debug("StalenessMonitor.stop . Timer stopped for Key -" + ccyPair + stream );
//		}
	}

	//Key is EUR/USDStream1
	public void onUpdate(MarketRate marketRate, RateFilterParameters params, String rateKey)
	{
		if(params.isStalenessCheckEnabled())
		{
			Task currentTask = tasks.remove(rateKey);
			if ( currentTask != null )
			{
				if(currentTask.getScheduledFuture() != null)
					currentTask.getScheduledFuture().cancel(true);
				this.monitor.remove(currentTask);
			}
			Task task = new Task(rateKey);            
            String ccyPair = CurrencyFactory.getCurrencyPairName(marketRate.getBaseCcy(), marketRate.getVariableCcy());
			long interval = (long) params.getStalenessInterval(ccyPair);
			tasks.put(rateKey, task);
			ScheduledFuture t1 = monitor.schedule(task, interval, TimeUnit.SECONDS);
			task.setScheduledFuture(t1);
		}
	}
	
	public ScheduledThreadPoolExecutor getMonitor()
	{
		return monitor;
	}
	
	public ConcurrentHashMap<String, Task> getTasks()
	{
		return tasks;
	}
	
	public void cancelAllActiveTasks()
	{
		log.warn("StalenessMonitor.cancelAllActiveTasks . Cancelling all the active tasks");
		for ( Entry<String, Task> key : tasks.entrySet() )
		{
			Task currentTask = tasks.remove(key.getKey());
			if ( currentTask != null )
			{
				currentTask.getScheduledFuture().cancel(true);
				this.monitor.remove(currentTask);
				log.warn("StalenessMonitor.cancelAllActiveTasks. Task cancelled. Id -  " + currentTask);
			}
		}
	}

	class Task implements Runnable
	{

		String key;
		ScheduledFuture result;

		/*
		 * Key is in 'EUR/USDStream' format.
		 */
		public Task(String key)
		{
			this.key = key;
		}

		public void run()
		{
			try
			{
				log.warn("StalenessMonitor.Task.run - Currency pair became stale. " + key);
				String data = key;
				MessageLogger.getInstance().log("CURRENCYPAIRSTALE", "StalenessMonitor", "Currency pair has became stale for key - " + key, data);
			}
			catch ( Exception e )
			{
				log.warn("StalenessMonitor.Task.run - Failed to send stale rate notification to IS. " + e.getMessage());
				log.debug("StalenessMonitor.Task.run - Exception ", e);
				tasks.remove(key);
			}
		}
		
		public String toString()
		{
			return key;
		}

		public ScheduledFuture getScheduledFuture()
		{
			return result;
		}

		public void setScheduledFuture(ScheduledFuture result)
		{
			this.result = result;
		}
	}

}
