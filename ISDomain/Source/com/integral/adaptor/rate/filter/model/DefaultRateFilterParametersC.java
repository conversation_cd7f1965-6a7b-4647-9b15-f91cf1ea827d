package com.integral.adaptor.rate.filter.model;

import com.integral.adaptor.rate.filter.config.RateFilterFactory;
import com.integral.adaptor.rate.filter.config.FilterMBean;
import com.integral.adaptor.util.AdaptorUtil;
import com.integral.finance.fx.FXRateConvention;

/**
 *
 */
public class DefaultRateFilterParametersC implements RateFilterParameters {
    private FilterMBean filterConfig = null;    

    public DefaultRateFilterParametersC() {
        filterConfig = RateFilterFactory.getInstance().getRateFilterMBean();
    }

    public boolean isMaxPipsDeviationFilterEnabled() {
        return filterConfig.isMaxPipsDeviationFilterEnabled();
    }

    public void setMaxPipsDeviationFilterEnabled(boolean maxPipsDeviationFilterEnabled) {

    }

    public double getMaxPipsDeviation() {
        return filterConfig.getMaximumPipsDeviation();
    }

    public void setMaxPipsDeviation(double maxPipsDeviation) {
    }

    public double getMaxPipsDeviation(String ccyPair) {
        double val = filterConfig.getMaximumPipsDeviation(ccyPair);
        return val == DEFAULT_VALUE ? getMaxPipsDeviation() : val;
    }

    public boolean isMaxPercentDeviationFilterEnabled() {
        return filterConfig.isMaxPercentPipsDeviationFilterEnabled();
    }

    public void setMaxPercentDeviationFilterEnabled(boolean maxPercentDeviationFilterEnabled) {
    }

    public double getMaxPercentDeviation() {
        return filterConfig.getMaximumPercentDeviation();
    }

    public void setMaxPercentDeviation(double maxPercentDeviation) {
    }

    public double getMaxPercentDeviation(String ccyPair) {
        double val = filterConfig.getMaximumPercentDeviation(ccyPair);
        return val == DEFAULT_VALUE ? getMaxPercentDeviation() : val;
    }

    public boolean isMaxSpreadFilterEnabled() {
        return filterConfig.isMaxSpreadFilterEnabled();
    }

    public void setMaxSpreadFilterEnabled(boolean maxSpreadFilterEnabled) {
    }

    public double getMaxSpread() {
        return filterConfig.getMaxSpread();
    }

    public void setMaxSpread(double maxSpread) {
    }

    public double getMaxSpread(String ccyPair) {
        double val = filterConfig.getMaxSpread(ccyPair);
        return val == DEFAULT_VALUE ? getMaxSpread() : val;
    }

    public boolean isStalenessCheckEnabled() {
        return filterConfig.isStalenessCheckEnabled();
    }

    public void setStalenessCheckEnabled(boolean stalenessCheckEnabled) {
    }

    public double getStalenessInterval() {
        return filterConfig.getStalenessInterval();
    }

    public double getStalenessInterval(String ccyPair) {
        double val = filterConfig.getStalenessInterval(ccyPair);
        return val == DEFAULT_VALUE ? getStalenessInterval() : val;
    }

    public boolean isFiltersEnabled() {
        return filterConfig.isFiltersEnabled();
    }

    public FXRateConvention getFXRateConvention() {
        String quoteConvention = filterConfig.getQuoteConvention();
        return AdaptorUtil.getFXRateConvention(quoteConvention);
    }

    public void setMaxPipsDeviation(String ccyPair, double maxPipsDeviation) {
    }

    public void setMaxPercentDeviation(String ccyPair, double maxPercentDeviation) {
    }

    public void setMaxSpread(String ccyPair, double maxSpread) {
    }

    public void setStalenessInterval(double stalenessInterval) {
    }

    public void setStalenessInterval(String ccyPair, double stalenessInterval) {
    }

    public void setFiltersEnabled(boolean filtersEnabled) {
    }

    public void setFXRateConvention(FXRateConvention rateConv) {
    }

    public String toString() {
        StringBuilder builder = new StringBuilder(100);
        builder.append("RateFilterParameters [key=Default");
        builder.append(", maxPercentDeviation=");
        builder.append(getMaxPercentDeviation());
        builder.append(", maxPipsDeviation=");
        builder.append(getMaxPipsDeviation());
        builder.append(", maxSpread=");
        builder.append(getMaxSpread());
        builder.append(", quoteConvention=");
        builder.append(getFXRateConvention().getShortName());
        builder.append(", stalenessInterval=");
        builder.append(getStalenessInterval());
        builder.append("]");
        return builder.toString();

    }
}
