package com.integral.adaptor.rate.filter.model;

import com.integral.adaptor.rate.filter.FilterLogger;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtilC;

public class MaxSpreadFilterC implements ProviderRateFilter<MarketRate, RateFilterParameters> {
    protected Log log = LogFactory.getLog(this.getClass());
    public static final String name = "MaximumSpread";
    
    public MaxSpreadFilterC() {
    }

    public boolean isFiltered(MarketRate rate, RateFilterParameters parameters) {
        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("MaxSpreadFilterC.isFiltered");
            sbf.append(name).append(' ');
            sbf.append(rate.getBaseCcy()).append(rate.getVariableCcy()).append(' ');
            sbf.append(rate.getProviderShortName()).append(' ');
            sbf.append(rate.getStreamId()).append(' ');
            sbf.append(rate.isStale()).append(' ');
            sbf.append(isFilterEnabled(parameters)).append(' ');
            log.debug(sbf.toString());
        }

        if (!isFilterEnabled(parameters)) {
            return true;
        }
        
        String ccyPair = CurrencyFactory.getCurrencyPairName(rate.getBaseCcy(), rate.getVariableCcy());
        FXRateBasis rateBasis = parameters.getFXRateConvention().getFXRateBasis(ccyPair);

        double spread = parameters.getMaxSpread(ccyPair);
        if (spread == RateFilterParameters.DEFAULT_VALUE) return true;

        double bid = rate.getBidRate();
        double offer = rate.getOfferRate();
        double pipsFactor = rateBasis.getPipsFactor();

        if (bid > 0 && offer > 0 && Math.abs( MathUtilC.subtract(offer, bid)) * pipsFactor > spread) {
            FilterLogger.getInstance().logRateDropped(name, rate, spread);
            return false;
        }

        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("MaxSpreadFilterC.isFiltered");
            sbf.append(name).append(' ');
            sbf.append(bid).append(' ').append(offer).append(' ');
            sbf.append(spread).append(' ');
            sbf.append(pipsFactor).append(' ');
            log.debug(sbf.toString());
        }

        return true;
    }

    public boolean reset() {
        return true;
    }

    public boolean reset(String event) {
        return true;
    }

    protected boolean isFilterEnabled(RateFilterParameters parameters) {
        return parameters.isMaxSpreadFilterEnabled();
    }

	@Override
	public String getName(){
		return name;
	}
}
