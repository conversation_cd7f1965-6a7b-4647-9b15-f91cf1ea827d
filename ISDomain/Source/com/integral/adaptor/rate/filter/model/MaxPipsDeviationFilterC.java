package com.integral.adaptor.rate.filter.model;

import com.integral.adaptor.rate.filter.FilterLogger;
import com.integral.adaptor.rate.filter.cache.RateCache;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtilC;

public class MaxPipsDeviationFilterC implements ProviderRateFilter<MarketRate, RateFilterParameters> {
    protected Log log = LogFactory.getLog(this.getClass());

    public static final String name = "MaximumPipsDeviation";
    protected RateCache rateFilterCache;

    public MaxPipsDeviationFilterC(RateCache cache) {
        rateFilterCache = cache;
    }

    public boolean isFiltered(MarketRate rate, RateFilterParameters parameters) {

        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("MaxPipsDeviationFilterC.isFiltered");
            sbf.append(name).append(' ');
            sbf.append(rate.getBaseCcy()).append(rate.getVariableCcy()).append(' ');
            sbf.append(rate.getProviderShortName()).append(' ');
            sbf.append(rate.getStreamId()).append(' ');
            sbf.append(rate.isStale()).append(' ');
            sbf.append(isFilterEnabled(parameters)).append(' ');
            log.debug(sbf.toString());
        }

        if (!isFilterEnabled(parameters)) {
            return true;
        }

        String ccyPair = CurrencyFactory.getCurrencyPairName(rate.getBaseCcy(), rate.getVariableCcy());
        String stream = rate.getStreamId();

        if (rate.isStale()) {
            rateFilterCache.remove(stream, ccyPair);
            return true;
        }

        MarketRate oldProviderPrice = rateFilterCache.getCachedRate(stream, ccyPair);

        if (oldProviderPrice == null) {
            rateFilterCache.replace(rate, stream, ccyPair);
            return true;
        }
        
        double oldBid = oldProviderPrice.getBidRate();
        double oldOffer = oldProviderPrice.getOfferRate();
        double newBid = rate.getBidRate();
        double newOffer = rate.getOfferRate();

        //Check deviation on bid and offer side.
        if (isDeviated(oldBid, newBid, ccyPair, parameters) || isDeviated(oldOffer, newOffer, ccyPair, parameters)) {
            FilterLogger.getInstance().logDeviatedRate(name, rate, oldProviderPrice, parameters.getMaxPipsDeviation(ccyPair));
            return false;
        }

        rateFilterCache.replace(rate, stream, ccyPair);
        return true;
    }

    public boolean reset() {
        return rateFilterCache != null && rateFilterCache.reset(name);
    }

    public boolean reset(String event) {
        return rateFilterCache != null && rateFilterCache.reset(name + " - " + event);
    }

    protected boolean isFilterEnabled(RateFilterParameters parameters) {
        return parameters.isMaxPipsDeviationFilterEnabled();
    }

    protected boolean isDeviated(double oldRate, double newRate, String ccyPair, RateFilterParameters parameters) {
        double maxPipsDeviation = parameters.getMaxPipsDeviation(ccyPair);
        FXRateBasis rateBasis = parameters.getFXRateConvention().getFXRateBasis(ccyPair);
        double pipsFactor = rateBasis.getPipsFactor();

        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("MaxPipsDeviationFilterC.isDeviated");
            sbf.append(name).append(' ');
            sbf.append(oldRate).append(' ').append(newRate).append(' ');
            sbf.append(maxPipsDeviation).append(' ');
            sbf.append(pipsFactor).append(' ');
            log.debug(sbf.toString());
        }

        if (maxPipsDeviation != RateFilterParameters.DEFAULT_VALUE &&
                !isZeroPrice(oldRate, newRate) && isDeviated(oldRate, newRate, maxPipsDeviation, pipsFactor)) {
            return true;
        }

        return false;
    }

    protected boolean isZeroPrice(double oldRate, double newRate) {
        return oldRate == 0 || newRate == 0;
    }

    private boolean isDeviated(double oldRate, double newRate, double maxPipsDeviation, double pipsFactor) {
        // bz 44341: 10000*(1.219 - 1.217) = 20.000000000000018 not 20 as one might expect.... use MathUtilC
		// true == (Math.abs( 1.2195 - 1.2175 ) * pipsFactor) > 20.0
		// false == (Math.abs( MathUtilC.subtract(1.2195, 1.2175) ) * price.getPipsFactor()) > 20.0
        return Math.abs( MathUtilC.subtract(oldRate, newRate) ) * pipsFactor > maxPipsDeviation;
    }
    
    @Override
    public String getName(){
    	return name;
    }
}
