package com.integral.adaptor.rate.filter;

import com.integral.finance.dealing.DealingPrice;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class FilterLogger {

	private Log log = LogFactory.getLog("com.integral.rate.filter.log");
	private static FilterLogger instance = new FilterLogger();
	private static final char RATE_START = '[';
	private static final char RATE_END = ']';
	private static final char SEPERATOR = ' ';
	private static final char NEW = 'N';
	private static final char OLD = 'O';

	protected FilterLogger() {
	}

	/**
	 * returns singleton instance of logger
	 * @return
	 */
	public static FilterLogger getInstance() {
		return instance;
	}

	/**
	* Prints details of rates dropped by max spread rate filter.
	*
	* @param filterName
	* @param quote
	* @param param
	*/

	public void logRateDropped(String filterName, MarketRate quote, double param) {
		if (log.isInfoEnabled()) {
			StringBuilder sb = new StringBuilder(100);
			sb.append(filterName).append(SEPERATOR);
			sb.append(quote.getProviderShortName()).append(SEPERATOR);
			sb.append(quote.getStreamId()).append(SEPERATOR);
			sb.append(quote.getBaseCcy()).append(quote.getVariableCcy()).append(SEPERATOR);
			sb.append(RATE_START).append(SEPERATOR);
			sb.append(quote.getQuoteId()).append(SEPERATOR);
			sb.append(quote.getBidRate()).append(SEPERATOR);
			sb.append(quote.getOfferRate()).append(SEPERATOR);
			sb.append(RATE_END).append(SEPERATOR);
			sb.append(param);
			log.info(sb.toString());
		}
	}

	/**
	 * Prints details of rates dropped by pips diviation rate filter.
	 *
	 * @param filterName
	 * @param quote
	 * @param previous
	 * @param param
	 */

	public void logDeviatedRate(String filterName, MarketRate quote, MarketRate previous, double param) {
		if (log.isInfoEnabled()) {
			StringBuilder sb = new StringBuilder(150);
			sb.append(filterName).append(SEPERATOR);
			sb.append(quote.getProviderShortName()).append(SEPERATOR);
			sb.append(quote.getStreamId()).append(SEPERATOR);
			sb.append(quote.getBaseCcy()).append(quote.getVariableCcy()).append(SEPERATOR);
			appendRate(quote.getProviderQuoteId(0,DealingPrice.BID),quote.getProviderQuoteId(0,DealingPrice.OFFER) ,quote.getBidRate(), quote.getOfferRate(), NEW, sb);
			appendRate(previous.getProviderQuoteId(0,DealingPrice.BID),previous.getProviderQuoteId(0,DealingPrice.OFFER), previous.getBidRate(), previous.getOfferRate(), OLD, sb);
			sb.append(param);
			log.info(sb.toString());
		}
	}

	private void appendRate(String bidQuoteID, String offerQuoteID,double bid, double offer, char state, StringBuilder sb) {
		sb.append(RATE_START).append(SEPERATOR);
		sb.append(bidQuoteID).append(SEPERATOR);
		sb.append(offerQuoteID).append(SEPERATOR);
		sb.append(bid).append(SEPERATOR);
		sb.append(offer).append(SEPERATOR);
		sb.append(state).append(SEPERATOR);
		sb.append(RATE_END).append(SEPERATOR);
	}

}
