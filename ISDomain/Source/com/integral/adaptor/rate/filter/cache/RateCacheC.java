package com.integral.adaptor.rate.filter.cache;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class RateCacheC implements RateCache {
    private static Log log = LogFactory.getLog(RateCacheC.class);

    protected ConcurrentMap<String, ConcurrentMap<String, MarketRate>> rateCache = new ConcurrentHashMap<String, ConcurrentMap<String, MarketRate>>();

    public RateCacheC() {
    }

    public boolean reset() {
        reset("Forced reset");
        return true;
    }

    /**
     * Used to reset cache when provider status changes to INACTIVE or STALE.
     *
     * @param event
     * @return
     */
    public boolean reset(String event) {
        log.warn("RateCacheC.reset - Rate cache reset done. Event - " + event);
        rateCache.clear();
        return true;
    }

    public void replace(MarketRate newRate, String stream, String ccyPair) {
        ConcurrentMap<String, MarketRate> map = this.rateCache.get(stream);

        if (null == map) {
            map = new ConcurrentHashMap<String, MarketRate>();
            map = this.rateCache.putIfAbsent(stream, map);
            if (map == null) {
                map = this.rateCache.get(stream);
            }
        }

        boolean replaced = false;
        MarketRate cachedRate = null;
        int attempt = 0;

        while (!replaced && attempt < 10) {
            cachedRate = map.get(ccyPair);
            if (cachedRate != null) {
                replaced = map.replace(ccyPair, cachedRate, newRate);

                if (!replaced) { // if in between new market rate is put in cache
                    // log replaced failed.
                }
            } else {
                cachedRate = map.putIfAbsent(ccyPair, newRate);// this will check if rate is already there by this time.
                if (cachedRate == null) { // if return value is null, that means operation successful.
                    replaced = true;
                }
            }
        }
    }

    public MarketRate getCachedRate(String stream, String ccyPair) {
        ConcurrentMap<String, MarketRate> map = this.rateCache.get(stream);
        MarketRate rate = null;

        if (null == map) {
            map = new ConcurrentHashMap<String, MarketRate>();
            this.rateCache.putIfAbsent(stream, map);
        } else {
            rate = map.get(ccyPair);
        }
        return rate;
    }

    public ConcurrentMap<String, ConcurrentMap<String, MarketRate>> getAllCachedRates() {
        return rateCache;
    }

    public void remove(String stream, String ccyPair) {
        ConcurrentMap<String, MarketRate> map = this.rateCache.get(stream);
        if (map != null) map.remove(ccyPair);        
    }

}
