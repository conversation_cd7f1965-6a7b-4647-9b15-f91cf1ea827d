package com.integral.adaptor.rate.filter.cache;

import com.integral.is.message.MarketRate;

import java.util.concurrent.ConcurrentMap;

/**
 *
 */
public interface RateCache {
    boolean reset();

    boolean reset(String event);

    void replace(MarketRate newRate, String stream, String ccyPair);

    MarketRate getCachedRate(String stream, String ccyPair);

    void remove(String stream, String ccyPair);
}
