package com.integral.adaptor.rate.filter.cache;

import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 *
 */
public class SingleStreamRateCacheC implements RateCache {
    private static Log log = LogFactory.getLog(SingleStreamRateCacheC.class);
    private MarketRate rate;

    public boolean reset() {
        reset("Forced reset");
        return true;
    }

    public boolean reset(String event) {
        log.warn("SingleStreamRateCacheC.reset - Rate cache reset done. Event - " + event);
        rate = null;
        return true;
    }

    public void replace(MarketRate newRate, String stream, String ccyPair) {
        rate = newRate;
    }

    public MarketRate getCachedRate(String stream, String ccyPair) {
        return rate;  //To change body of implemented methods use File | Settings | File Templates.
    }

    public void remove(String stream, String ccyPair) {
        rate = null;
    }
}
