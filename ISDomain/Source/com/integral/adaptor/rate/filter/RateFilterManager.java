package com.integral.adaptor.rate.filter;

import com.integral.adaptor.rate.filter.config.RateFilterFactory;
import com.integral.adaptor.rate.filter.model.*;
import com.integral.broker.aggregate.FilterConfigFactory;
import com.integral.broker.filter.FilterStats;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
public class RateFilterManager {
    private Log log = LogFactory.getLog(this.getClass());
    private RateFilterParameters rateFilterParams;
    private boolean isSingleStreamSupported = false;

    private List<ProviderRateFilter<MarketRate, RateFilterParameters>> rateFilters = null;

    public RateFilterManager() {
    }

    public RateFilterManager(boolean isSingleStreamSupported) {
        this.isSingleStreamSupported = isSingleStreamSupported;
    }

    public RateFilterManager(RateFilterParameters rateFilterParams, boolean isSingleStreamSupported) {
        this.rateFilterParams = rateFilterParams;
        this.isSingleStreamSupported = isSingleStreamSupported;
    }

    public void init() {
        long startTime = System.currentTimeMillis();
        log.info(new StringBuilder("RateFilterManager - Rate Filter definition loaded/initialized - ")
                .append(toString()).append(". Time taken - ").append(System.currentTimeMillis() - startTime).toString());
    }

    public RateFilterParameters getRateFilterParams() {
        if (rateFilterParams == null) {
            rateFilterParams = RateFilterFactory.getInstance().newDefaultRateFilterParameters();
        }
        return rateFilterParams;
    }

    public boolean isFiltered(MarketRate rate, FilterConfigFactory filterConfigFactory) {
        boolean result = true;
        RateFilterParameters rateFilterParameters = getRateFilterParams();

        if (log.isDebugEnabled()) {
            StringBuilder sbf = new StringBuilder("RateFilterManager.isFiltered");
            sbf.append(rate.getBaseCcy()).append(rate.getVariableCcy()).append(' ');
            sbf.append(rate.getProviderShortName()).append(' ');
            sbf.append(rate.getStreamId()).append(' ');
            sbf.append(rateFilterParameters.isFiltersEnabled());
            log.debug(sbf.toString());
        }

        if (!rateFilterParameters.isFiltersEnabled()) {
            return result;//True means -pass -send it to Apps
        }

        String ccyPair = CurrencyFactory.getCurrencyPairName(rate.getBaseCcy(), rate.getVariableCcy());
        String rateCacheKey = ccyPair;
        if (rate.getStreamId() != null) {
            rateCacheKey = ccyPair + rate.getStreamId();
        }

        if (rateFilterParameters.isStalenessCheckEnabled() && rateFilterParameters.getStalenessInterval(ccyPair) != RateFilterParameters.DEFAULT_VALUE) {
            StalenessMonitor.getInstance().onUpdate(rate, rateFilterParameters, rateCacheKey);
        }

		FilterStats stats = filterConfigFactory.getFilterStats();
		List<ProviderRateFilter<MarketRate, RateFilterParameters>> rateFilters = getRateFilters();
        for (ProviderRateFilter<MarketRate, RateFilterParameters> rateFilter : rateFilters) {
            if (!rateFilter.isFiltered(rate, rateFilterParameters))
            {
                rate.setStalenessReason ( rateFilter.getName() );
            	if(stats != null) stats.addEvent(rateFilter.getName(), "MarketRate");
                result = false;
                break;
            }
        }
        return result;
    }

    public List<ProviderRateFilter<MarketRate, RateFilterParameters>> getRateFilters() {
        if (rateFilters == null) {

            ArrayList<ProviderRateFilter<MarketRate, RateFilterParameters>> filters =
                    new ArrayList<ProviderRateFilter<MarketRate, RateFilterParameters>>(3);
            if (getRateFilterParams().isMaxSpreadFilterEnabled()) {
                filters.add(RateFilterFactory.getInstance().newMaxSpreadFilter());
            }

            if (getRateFilterParams().isMaxPipsDeviationFilterEnabled()) {
                filters.add(RateFilterFactory.getInstance().newMaxPipsDeviationFilter(isSingleStreamSupported));
            }

            if (getRateFilterParams().isMaxPercentDeviationFilterEnabled()) {
                filters.add(RateFilterFactory.getInstance().newMaxPercentPipsDeviationFilter(isSingleStreamSupported));
            }
            rateFilters = filters;
        }

        return rateFilters;
    }

    public void reset() {
        if (rateFilters != null) {
            for (ProviderRateFilter<MarketRate, RateFilterParameters> rateFilter : rateFilters) {
                rateFilter.reset();
            }
        }
    }

    public void reset(String event) {
        if (rateFilters != null) {
            for (ProviderRateFilter<MarketRate, RateFilterParameters> rateFilter : rateFilters) {
                rateFilter.reset(event);
            }
        }
    }

    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("RateFilterManager");
        builder.append(" FiltersEnabled=").append(getRateFilterParams().isFiltersEnabled());
        builder.append(" MaximumSpreadEnabled=").append(getRateFilterParams().isMaxSpreadFilterEnabled());
        builder.append(" MaximumPipsDeviationEnabled=").append(getRateFilterParams().isMaxPipsDeviationFilterEnabled());
        builder.append(" MaximumPercentPipsEnabled=").append(getRateFilterParams().isMaxPercentDeviationFilterEnabled());
        builder.append(" StalenessCheckEnabled=").append(getRateFilterParams().isStalenessCheckEnabled());
        builder.append(" [baseParams=");
        builder.append(getRateFilterParams().toString());
        builder.append("]");
        builder.append("[Currency Params=").append("").append("]");
        return builder.toString();
    }
}
