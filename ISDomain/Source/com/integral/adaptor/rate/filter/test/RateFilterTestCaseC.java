package com.integral.adaptor.rate.filter.test;

import com.integral.adaptor.rate.filter.RateFilterManager;
import com.integral.adaptor.rate.filter.config.RateFilterFactory;
import com.integral.adaptor.rate.filter.model.RateFilterParameters;
import com.integral.broker.aggregate.BrokerFilterFactoryC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateC;
import com.integral.test.PTestCaseC;
import com.integral.test.functor.ReadNamedEntityC;

/**
 *
 */
public class RateFilterTestCaseC extends PTestCaseC {
    private RateFilterParameters params;

    public RateFilterTestCaseC(String aName) {
        super(aName);
    }

    protected void setUp() throws Exception {
		super.setUp();
        FXRateConvention stdConv = ( FXRateConvention ) new ReadNamedEntityC().execute( FXRateConvention.class, "STDQOTCNV" );

        params = new RateFilterParametersMock();
        params.setFXRateConvention(stdConv);
        params.setFiltersEnabled(true);
	}

    public void testSingleStreamMaxSpreadFilter() {
        params.setMaxSpreadFilterEnabled(true);
        params.setMaxPercentDeviationFilterEnabled(false);
        params.setStalenessCheckEnabled(false);
        params.setMaxPipsDeviationFilterEnabled(false);

        params.setMaxSpread(10);

        RateFilterManager rateFilterManager = RateFilterFactory.getInstance().newRateFilterManager(params, true);
        MarketRate rate = createMarketRate("EUR", "USD", 1.2345, 1.2355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false);

        params.setMaxSpread(7);
        rate = createMarketRate("EUR", "USD", 1.2345, 1.2355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false);

        params.setMaxSpread(12);
        rate = createMarketRate("EUR", "USD", 1.2345, 1.2355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true);

        params.setMaxSpread(10);
        rate = createMarketRate("EUR", "USD", 1.2345, 1.2350);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true);
    }

    public void testSingleStreamMaxPipsFilter() {
        params.setMaxSpreadFilterEnabled(false);
        params.setMaxPercentDeviationFilterEnabled(false);
        params.setStalenessCheckEnabled(false);
        params.setMaxPipsDeviationFilterEnabled(true);

        params.setMaxPipsDeviation(10);

        RateFilterManager rateFilterManager = RateFilterFactory.getInstance().newRateFilterManager(params, true);
        MarketRate rate = createMarketRate("EUR", "USD", 1.2345, 1.2355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true); // Filtered.

        rate = createMarketRate("EUR", "USD", 1.2357, 1.2364);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false);  // Dropped.

        rate = createMarketRate("EUR", "USD", 1.2350, 1.2355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true); // Filtered.

        rate = createMarketRate("EUR", "USD", 1.2345, 1.2370);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false); // Dropped.

        rate = createMarketRate("EUR", "USD", 1.2357, 1.2366);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false); // Dropped.
    }

    public void testSingleStreamMaxPercentFilter() {
        params.setMaxSpreadFilterEnabled(false);
        params.setMaxPercentDeviationFilterEnabled(true);
        params.setStalenessCheckEnabled(false);
        params.setMaxPipsDeviationFilterEnabled(false);

        params.setMaxPercentDeviation(2);

        RateFilterManager rateFilterManager = RateFilterFactory.getInstance().newRateFilterManager(params, true);
        MarketRate rate = createMarketRate("EUR", "USD", 1.2345, 1.2355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true); // Filtered.

        rate = createMarketRate("EUR", "USD", 1.2357, 1.2364);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true);  // Filtered.

        rate = createMarketRate("EUR", "USD", 1.3350, 1.3355);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false); // Dropped.

        rate = createMarketRate("EUR", "USD", 1.2345, 1.2370);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), true); // Filtered.

        rate = createMarketRate("EUR", "USD", 1.2357, 1.3366);
        assertEquals(rateFilterManager.isFiltered(rate, BrokerFilterFactoryC.getBrokerFilterFactory()), false); // Dropped.
    }

    private MarketRate createMarketRate(String baseCcy, String termCcy, double bidRate, double varRate) {
        MarketRate rate = new MarketRateC();
        rate.setBaseCcy(baseCcy);
        rate.setVarCcy(termCcy);
        rate.setBidRate(bidRate, 0);
        rate.setOfferRate(varRate, 0);

        return rate;
    }
}

