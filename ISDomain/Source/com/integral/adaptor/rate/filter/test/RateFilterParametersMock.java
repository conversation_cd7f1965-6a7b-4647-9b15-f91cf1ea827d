package com.integral.adaptor.rate.filter.test;

import com.integral.adaptor.rate.filter.model.RateFilterParameters;
import com.integral.finance.fx.FXRateConvention;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 */
public class RateFilterParametersMock implements RateFilterParameters {
    private boolean isFiltersEnabled = false;
    private boolean isMaxPipsDeviationFilterEnabled = false;
    private boolean isMaxPercentPipsDeviationFilterEnabled = false;
    private boolean isMaxSpreadFilterEnabled = false;
    private boolean isStainlessFilterEnabled = false;

    private double maxPipsDeviation;
    private double maxPercentDeviation;
    private double maxSpread;
    private double stalenessInterval;

    private FXRateConvention rateConvention;

    private Map<String, Double> maxSpreads = new HashMap<String, Double>();
    private Map<String, Double> maxPipsDeviations = new HashMap<String, Double>();
    private Map<String, Double> maxPercentDeviations = new HashMap<String, Double>();
    private Map<String, Double> stalenessIntervals = new HashMap<String, Double>();

    public boolean isMaxPipsDeviationFilterEnabled() {
        return isMaxPipsDeviationFilterEnabled;
    }

    public void setMaxPipsDeviationFilterEnabled(boolean maxPipsDeviationFilterEnabled) {
        this.isMaxPipsDeviationFilterEnabled = maxPipsDeviationFilterEnabled;
    }

    public double getMaxPipsDeviation() {
        return maxPipsDeviation;
    }

    public void setMaxPipsDeviation(double maxPipsDeviation) {
        this.maxPipsDeviation = maxPipsDeviation;
    }

    public double getMaxPipsDeviation(String ccyPair) {
        return maxPipsDeviations.get(ccyPair) == null ? getMaxPipsDeviation() : maxPipsDeviations.get(ccyPair);
    }

    public void setMaxPipsDeviation(String ccyPair, double maxPipsDeviation) {
        this.maxPipsDeviations.put(ccyPair, maxPipsDeviation);
    }

    public boolean isMaxPercentDeviationFilterEnabled() {
        return isMaxPercentPipsDeviationFilterEnabled;
    }

    public void setMaxPercentDeviationFilterEnabled(boolean maxPercentDeviationFilterEnabled) {
        this.isMaxPercentPipsDeviationFilterEnabled = maxPercentDeviationFilterEnabled;
    }

    public double getMaxPercentDeviation() {
        return maxPercentDeviation;
    }

    public void setMaxPercentDeviation(double maxPercentDeviation) {
        this.maxPercentDeviation = maxPercentDeviation;
    }

    public double getMaxPercentDeviation(String ccyPair) {
        return maxPercentDeviations.get(ccyPair) == null ? getMaxPercentDeviation() : maxPercentDeviations.get(ccyPair);
    }

    public void setMaxPercentDeviation(String ccyPair, double maxPercentDeviation) {
        this.maxPercentDeviations.put(ccyPair, maxPercentDeviation);
    }


    public boolean isMaxSpreadFilterEnabled() {
        return isMaxSpreadFilterEnabled;
    }

    public void setMaxSpreadFilterEnabled(boolean maxSpreadFilterEnabled) {
        this.isMaxSpreadFilterEnabled = maxSpreadFilterEnabled;
    }

    public double getMaxSpread() {
        return maxSpread;
    }

    public void setMaxSpread(double maxSpread) {
        this.maxSpread = maxSpread;
    }

    public double getMaxSpread(String ccyPair) {
        return maxSpreads.get(ccyPair) == null ? getMaxSpread() : maxSpreads.get(ccyPair);
    }

    public void setMaxSpread(String ccyPair, double maxSpread) {
        this.maxSpreads.put(ccyPair, maxSpread);
    }


    public boolean isStalenessCheckEnabled() {
        return isStainlessFilterEnabled;
    }

    public void setStalenessCheckEnabled(boolean stalenessCheckEnabled) {
        this.isStainlessFilterEnabled = stalenessCheckEnabled;
    }

    public double getStalenessInterval() {
        return stalenessInterval;
    }

    public void setStalenessInterval(double stalenessInterval) {
        this.stalenessInterval = stalenessInterval;
    }

    public double getStalenessInterval(String ccyPair) {
        return stalenessIntervals.get(ccyPair) == null ? getStalenessInterval() : stalenessIntervals.get(ccyPair);
    }

    public void setStalenessInterval(String ccyPair, double stalenessInterval) {
        this.stalenessIntervals.put(ccyPair, stalenessInterval);
    }

    public boolean isFiltersEnabled() {
        return isFiltersEnabled;
    }

    public void setFiltersEnabled(boolean filtersEnabled) {
        this.isFiltersEnabled = filtersEnabled;
    }

    public FXRateConvention getFXRateConvention() {
        return rateConvention;
    }

    public void setFXRateConvention(FXRateConvention rateConv) {
        this.rateConvention = rateConv;
    }
}
