package com.integral.adaptor.config;

import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;

/**
 * 
 */
public class AdaptorConfigurationFactory
{

	protected static Log log = LogFactory.getLog(AdaptorConfigurationFactory.class);
	protected static AdaptorConfigurationFactory current;
	protected volatile ResponseHandlerC responseHandler;

	static
	{
		AdaptorConfigurationFactory.current = new AdaptorConfigurationFactory();
	}

	private static AdaptorConfiguration mbeanInstance = null;

	protected AdaptorConfiguration _getAdaptorConfigurationMBean()
	{
		if ( mbeanInstance == null )
		{
			synchronized ( this )
			{
				if ( mbeanInstance == null )
				{
					mbeanInstance = new AdaptorConfiguration();
					ConfigurationFactory.registerMBean(mbeanInstance);
				}
			}
		}
		return mbeanInstance;
	}

	public static AdaptorConfiguration getAdaptorConfigurationMBean()
	{
		return current._getAdaptorConfigurationMBean();
	}

	/**
	 * This method will be called by the non-fix adaptors with the MBean implementation class name.
	 *
	 * @param className
	 */
	public static void loadMBeanClass(String className)
	{
		try
		{

			mbeanInstance = (AdaptorConfiguration) Class.forName(className).newInstance();
			ConfigurationFactory.registerMBean(mbeanInstance);

		}
		catch ( Exception ex )
		{
			current.log.warn("AdaptorConfigurationFactory.loadMBeanClass:: The class name to load:  " + className);
			current.log.error("Exception while executing AdaptorConfigurationFactory.loadMBeanClass Exception::  ", ex);
		}

	}

	/**
	 *  This method will be called by FixAdaptorMBeanFactory (FixAdaptors), after instantiating the adaptor specific MBean.
	 *
	 * @param mBean
	 */
	public static void loadMBeanClass(AdaptorConfiguration mBean)
	{
		try
		{

			mbeanInstance = mBean;
			ConfigurationFactory.registerMBean(mbeanInstance);
		}
		catch ( Exception e )
		{
			log.error("Exception in AdaptorConfigurationFactory.loadMBeanClass(AdaptorConfiguration mBean) e = ", e);
		}
	}

	public static ResponseHandlerC getResponseHandlerInstance()
	{
		return current._getResponseHandlerInstance();
	}

	protected ResponseHandlerC _getResponseHandlerInstance()
	{
		if ( responseHandler == null )
		{
			synchronized ( this )
			{
				if ( responseHandler == null )
				{
					String responseHandlerClassVal = null;
					try
					{
						if ( mbeanInstance == null )
						{
							current._getAdaptorConfigurationMBean();
						}
						responseHandlerClassVal = mbeanInstance.getMulticastResponseHandler();
						Class responseHandlerClass = Class.forName(responseHandlerClassVal);
						responseHandler = (ResponseHandlerC) responseHandlerClass.newInstance();

					}
					catch ( Exception e )
					{
						log.error("Exception happened in initializing response handler instance - please set correct class value in MulticastMBean.properties file based on deployemnt : " + responseHandlerClassVal, e);
					}
				}
			}
		}
		return responseHandler;
	}

}
