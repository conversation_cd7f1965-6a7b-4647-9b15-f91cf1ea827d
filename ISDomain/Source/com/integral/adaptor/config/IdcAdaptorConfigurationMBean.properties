Idc.Adaptor.AutoCancellation=false
Idc.Adaptor.StalenessCheck=false
Idc.Adaptor.Override.StalenessCheck=false
Idc.Adaptor.Stalenesscheck.Time=0
Idc.Adaptor.AutoCancellation.Time=5
Idc.Adaptor.Heartbeat.DeliveryMode=104
Idc.Adaptor.Heartbeat.Destination.JNDI.Prefix=TOIS.MESSAGES.
Idc.Adaptor.Rates.DeliveryMode=104
#This property needs to be overridden in hostname.properties
#eg. Idc.Adaptor.Provider.InitProps=jboss40|jnp://localhost:1299|DRKWRequestService
Idc.Adaptor.Provider.InitProps=
Idc.Adaptor.Provider.HeartbeatCheckInterval=5000
Idc.Adaptor.ThreadPool.Size=10
Idc.Adaptor.ThreadPool.WorkQueue.Size=5
# Set to 100 & 100 to handle 100 concurrent updates from provider.
Idc.Adaptor.Rates.ThreadPool.Size=100
Idc.Adaptor.Rates.ThreadPool.WorkQueue.Size=100
Idc.Adaptor.Use.Provider.Sequence=false