package com.integral.adaptor.config;

import com.integral.admin.utils.StringUtils;
import com.integral.log.LogFactory;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.is.common.comm.messaging.MessagingEnums;
import com.integral.transport.multicast.provisioning.MulticastProvisioningClientFactoryC;
import com.integral.transport.multicast.configuration.MulticastConfigurationFactory;
import com.integral.transport.multicast.configuration.MulticastMBean;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

public class AdaptorConfiguration extends IdcMBeanC implements AdaptorConfigurationMBean
{

	public AdaptorConfiguration(String fileName)
	{
		super(fileName);
		initProperties();
	}

	public AdaptorConfiguration()
	{
		super("com.integral.adaptor.config.IdcAdaptorConfigurationMBean");
		initProperties();
	}

	boolean autoCancellation = getBooleanProperty(IDC_ADAPTOR_AUTOCANCELLATION, false);
	boolean stalenessCheck = getBooleanProperty(IDC_ADAPTOR_STALENESSCHECK, false);
	boolean overrideStalenessCheck = getBooleanProperty(IDC_ADAPTOR_OVERRIDE_STALENESSCHECK, false);
	int stalenessCheckTime = getIntProperty(IDC_ADAPTOR_STALENESSCHECK_TIME, 0);
	int autoCancellationTime = getIntProperty(IDC_ADAPTOR_AUTOCANCELLATION_TIME, 0);
	String adaptorInitProps = getStringProperty(IDC_ADAPTOR_PROVIDER_INITPROPS, "");
	long heartBeatCheckInterval = getLongProperty(IDC_ADAPTOR_PROVIDER_HEARTBEATCHECK_INTERVAL, 5000);
	int heartBeatDeliveryMode = getIntProperty(IDC_ADAPTOR_HEARTBEAT_DELIVERYMODE, 104);
	int ratesDeliveryMode = getIntProperty(IDC_ADAPTOR_RATES_DELIVERYMODE, 104);
	String defaultheartbeatsender = getStringProperty(IDC_ORDER_FIX_HEARTBEATCHECK, "true");
	int ratesThreadPoolSize = getIntProperty(IDC_ADAPTOR_RATES_THREADPOOL_SIZE, 100);
	int ratesThreadPoolWorkQueueSize = getIntProperty(IDC_ADAPTOR_RATES_THREADPOOL_WORKQUEUE_SIZE, 100);
	int threadPoolSize = getIntProperty(IDC_ADAPTOR_THREADPOOL_SIZE, 10);
	int threadPoolWorkQueueSize = getIntProperty(IDC_ADAPTOR_THREADPOOL_WORKQUEUE_SIZE, 5);
	boolean useProviderSeqNum = getBooleanProperty(IDC_ADAPTOR_USE_PROVIDER_SEQUENCE_NUMBER, false);
	boolean useEjb = getBooleanProperty(IDC_ADAPTOR_USE_EJB, true);
    int serializerVersion = getIntProperty(IDC_MCAST_RATE_SERIALIZER_VERSION, 2);
    boolean imtpEnabled = getBooleanProperty(IDC_ADAPTOR_IMTP_ENABLED, false);
	int tradeSchedulerServiceThreadPoolSize;

	private MessagingEnums.RatesTransports ratesTransport;

	MulticastMBean multicastMBean;
	private Map<String, Boolean> processNonDeployedAdaptorMap;



	private boolean isVenueIntegrationEnabled;

	private int mdfMulticastPort;
	private String mdfMulticastGroup;
	private Map<String, String> mdfMulticastGroupVsCcyPairFromProperty;

	private Map<String, String> mdfMulticastGroupVsCcyPair;
	private int mdfQuoteDepth;

	private boolean isV4BrokerEnabled;

	public void initialize()
	{
		super.initialize();

		multicastMBean = MulticastConfigurationFactory.getInstance().getMulticastMBean();
		MulticastProvisioningClientFactoryC.setServiceBaseUrl(multicastMBean.getProvisioningServerBaseURL());
		MulticastProvisioningClientFactoryC.setContextBase(multicastMBean.getProvisioningContextBase());
        imtpEnabled = getBooleanProperty(IDC_ADAPTOR_IMTP_ENABLED, false);
		this.isVenueIntegrationEnabled = getBooleanProperty(ADAPTOR_VENUEINTEGRATION_ENABLED,false);
		this.isV4BrokerEnabled = getBooleanProperty(BROKER_V4_ENABLED,false);
		mdfQuoteDepth = getIntProperty(IDC_ADAPTOR_QUOTE_MARKET_DEPTH,5);
		mdfMulticastGroup = getStringProperty(IDC_ADAPTOR_MARKET_DATA_GROUP,null);
		mdfMulticastPort = getIntProperty(IDC_ADAPTOR_MARKET_DATA_PORT,3605);
		mdfMulticastGroupVsCcyPairFromProperty = initMultipleSuffixStringPropertyMap(IDC_ADAPTOR_MARKET_DATA_GROUP_PREFIX,null);
		mdfMulticastGroupVsCcyPair = new ConcurrentHashMap<String, String>();
		tradeSchedulerServiceThreadPoolSize = getIntProperty(IDC_ADAPTOR_TRADE_SCHEDULER_SERVICE_THREAD_POOLSIZE, 1);
		processNonDeployedAdaptorMap = new HashMap<String, Boolean>();
		initProcessNonDeployedAdaptorsEnabledMap();
		if (log.isDebugEnabled()) {
			log.debug("Initialized the MulticastProvisioningClient serviceUrl to " + MulticastProvisioningClientFactoryC.getServiceUrl());
		}
	}

	protected void initProperties()
	{
		String tmp = null;
	}

	public void setAutoCancellationApplicable(boolean autoCancellation)
	{
		this.autoCancellation = autoCancellation;
		LogFactory.getLog(this.getClass()).warn("AdaptorConfiguration.setAutoCancellationApplicable : autoCancellation=" + autoCancellation);
	}

	public boolean isAutoCancellationApplicable()
	{
		return this.autoCancellation;
	}

	public boolean isDefaultHeartBeatSenderApplicable()
	{
		return ("yes".equalsIgnoreCase(this.defaultheartbeatsender) || "true".equalsIgnoreCase(this.defaultheartbeatsender));

	}

	public void setStalenessCheckApplicable(boolean stalenessCheck)
	{
		this.stalenessCheck = stalenessCheck;
		LogFactory.getLog(this.getClass()).warn("AdaptorConfiguration.setStalenessCheckApplicable : stalenessCheck=" + stalenessCheck);
	}

	public boolean isStalenessCheckApplicable()
	{
		return this.stalenessCheck;
	}

	public int getStalenessCheckTime()
	{
		return this.stalenessCheckTime;
	}

	public void setStalenessCheckTime(int stalenessCheckTime)
	{
		if ( stalenessCheckTime >= 0 )
		{
			this.stalenessCheckTime = stalenessCheckTime;
			LogFactory.getLog(this.getClass()).warn("AdaptorConfiguration.setStalenessCheckTime : stalenessCheckTime=" + stalenessCheckTime);
		}
	}

	public int getAutoCancellationTime()
	{
		return this.autoCancellationTime;
	}

	public void setAutoCancellationTime(int autoCancellationTime)
	{
		if ( autoCancellationTime > 0 )
		{
			this.autoCancellationTime = autoCancellationTime;
			LogFactory.getLog(this.getClass()).warn("AdaptorConfiguration.setAutoCancellationTime : autoCancellationTime=" + autoCancellationTime);
		}
	}

	public boolean isOverrideStalenessCheck()
	{
		return this.overrideStalenessCheck;
	}

	public void setOverrideStalenessCheck(boolean overrideStalenessCheck)
	{
		this.overrideStalenessCheck = overrideStalenessCheck;
		LogFactory.getLog(this.getClass()).warn("AdaptorConfiguration.setOverrideStalenessCheck : overrideStalenessCheck=" + overrideStalenessCheck);
	}

	public String getProviderInitProps()
	{
		return getStringProperty(IDC_ADAPTOR_PROVIDER_INITPROPS, "");
	}

	public long getHeartbeatCheckInterval()
	{
		return getIntProperty(IDC_ADAPTOR_PROVIDER_HEARTBEATCHECK_INTERVAL, 5000);
	}

	public void setHeartbeatCheckInterval(long heartBeatCheckInt)
	{
		this.heartBeatCheckInterval = heartBeatCheckInt;
		LogFactory.getLog(this.getClass()).warn("AdaptorConfiguration.setOverrideStalenessCheck : HeartbeatCheckInterval=" + heartBeatCheckInterval);
	}

	public String getShortName()
	{
		return getStringProperty(IDC_IS_PROVIDERS_SHORTNAME, "");
	}

	public String getProviderList()
	{
		return getStringProperty(IDC_IS_PROVIDERS_LIST_SHORTNAME, "");
	}

	public boolean isHeartBeatCheckEnabled()
	{
		return getBooleanProperty(IDC_IS_PROVIDERS_HEARTBEAT_CHECKENABLED, true);
	}

	public int getHeartbeatDeliveryMode()
	{
		return heartBeatDeliveryMode;
	}

	public String getDeclineCount()
	{
		return getStringProperty(IDC_IS_ADAPTERS_SIMULATOR_DECLINE_COUNT, "5");
	}

	public boolean isSimulate()
	{
		String env = getStringProperty(IDC_ADAPTOR_ENVIRONMENT, "");
		if ( env.trim().equalsIgnoreCase("simulator") )
		{
			return true;
		}
		else
		{
			return false;
		}

	}

	public String getEnvironment()
	{
		return getStringProperty(IDC_ADAPTOR_ENVIRONMENT, "");
	}

	public String getSimulatorSpread()
	{
		return getStringProperty(IDC_IS_ADAPTORS_SIMULATOR_SPREAD, "5");
	}

	public int getRatesDeliveryMode()
	{
		return ratesDeliveryMode;
	}

	public String getMBeanName()
	{
		return "AdaptorConfiguration";
	}

	public String getHeartbeatDestinationJNDIPrefix()
	{
		return getStringProperty(IDC_ADAPTOR_HEARTBEAT_DESTINATION_JNDI_PREFIX, "TOIS.MESSAGES.");
	}

	public String getServerURL()
	{
		return getStringProperty(IDC_SERVER_URL, null);
	}

	public int getThreadPoolSize()
	{
		return this.threadPoolSize;
	}

	public int getThreadPoolWorkQueueSize()
	{
		return this.threadPoolWorkQueueSize;
	}

	public int getRatesThreadPoolSize()
	{
		return this.ratesThreadPoolSize;
	}

	public int getRatesThreadPoolWorkQueueSize()
	{
		return this.ratesThreadPoolWorkQueueSize;
	}

	public boolean isUseProviderSequenceNumber()
	{
		return this.useProviderSeqNum;
	}

	public boolean isEJBInUse()
	{
		return this.useEjb;
	}

	public String getRequestHandlerFactory()
	{
		return getStringProperty(IDC_ADAPTOR_REQUEST_HANLDER_FACTORY, null);
	}

	public String getMulticastResponseHandler()
	{
		return getStringProperty(AdaptorConfigurationMBean.IDC_MCAST_RESPONSE_HANDLER_CLASS, "com.integral.adaptor.response.LocalResponseHandlerC");
	}

	public MessagingEnums.RatesTransports getRatesTransport()
	{
		return ratesTransport;
	}

	public MulticastMBean getMulticastMBean()
	{
		return multicastMBean;
	}

    public boolean isIMTPEnabled() {
        return imtpEnabled;
    }

    public int getSerializerVersion()
    {
        return serializerVersion;
    }

    public int getTradeSchedulerServiceThreadPoolSize() {
        return tradeSchedulerServiceThreadPoolSize;
    }
    
   	@Override
	public boolean isMulticastHeartBeatEnabled(){
		return getBooleanProperty(IDC_ADAPTOR_MULTICAST_HEARTBEAT_ENABLED, false);
	}
	
	@Override
	public String getMulticastHeartBeatGroup(){
		return getStringProperty(IDC_ADAPTOR_MULTICAST_HEARTBEAT_GROUP, "224.24.24.24");
	}
	
	@Override
	public int getMulticastHeartBeatPort(){
		return getIntProperty(IDC_ADAPTOR_MULTICAST_HEARTBEAT_PORT, 31914);
	}

	private void initProcessNonDeployedAdaptorsEnabledMap() {
		Properties properties = getPropertiesWithPrefix(IDC_PROCESS_NON_DEPLOYED_ADAPTOR);
		if (properties.size() > 0)
			for (Map.Entry entry : properties.entrySet()) {
				String key = (String) entry.getKey();
				if (key.length() > IDC_PROCESS_NON_DEPLOYED_ADAPTOR.length()) {
					String adaptorName = key.substring(IDC_PROCESS_NON_DEPLOYED_ADAPTOR.length() + 1);
					String value = (String) entry.getValue();
					if (!StringUtils.isNullOrEmptyString(value)) {
						boolean isEnabled = Boolean.parseBoolean(value);
						processNonDeployedAdaptorMap.put(adaptorName, isEnabled);
					}
				}
			}
		;
	}

	@Override
	public boolean isProcessNonDeployedAdaptorsEnabled(String adaptorName) {
		Boolean lpLevel = processNonDeployedAdaptorMap.get(adaptorName);
		boolean result = lpLevel != null ? lpLevel : getBooleanProperty(IDC_PROCESS_NON_DEPLOYED_ADAPTOR, false);
		return result;
	}

	public boolean isVenueIntegrationEnabled() {
		return isVenueIntegrationEnabled;
	}

	public int getMDFQuoteDepth() {
		return mdfQuoteDepth;
	}


	public String getMdfMulticastGroup() {
		return mdfMulticastGroup;
	}


	public int getMdfMulticastPort() {
		return mdfMulticastPort;
	}

	public String getMDFMulticastGroup(String orgName, String ccyPair){
		String key = orgName+ccyPair;
		String multicastGroup = mdfMulticastGroupVsCcyPair.get(orgName+ccyPair);
		if(multicastGroup == null){
			multicastGroup = getHierarchicalStringProperty(mdfMulticastGroupVsCcyPairFromProperty,mdfMulticastGroup,orgName,ccyPair);
			mdfMulticastGroupVsCcyPair.put(key,multicastGroup);
		}
		return multicastGroup;
	}
	protected String getHierarchicalStringProperty( Map<String, String> stringMap, String defaultValueIfNotFound, String venue, String ccyPair) {
		String defaultValue = stringMap.get(venue);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		defaultValue = stringMap.get("*." + ccyPair);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		defaultValue = stringMap.get( venue + "." + ccyPair);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		return defaultValueIfNotFound;
	}

	protected Boolean getHierarchicalBooleanProperty( Map<String, Boolean> booleanMap, Boolean defaultValueIfNotFound, String venue, String ccyPair) {
		Boolean defaultValue = booleanMap.get(venue);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		defaultValue = booleanMap.get("*." + ccyPair);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		defaultValue = booleanMap.get( venue + "." + ccyPair);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		return defaultValueIfNotFound;
	}
	protected Integer getHierarchicalIntegerProperty( Map<String, Integer> integerMap, Integer defaultValueIfNotFound, String venue, String ccyPair) {
		Integer defaultValue = integerMap.get(venue);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		defaultValue = integerMap.get("*." + ccyPair);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		defaultValue = integerMap.get( venue + "." + ccyPair);
		defaultValueIfNotFound = defaultValue!=null?defaultValue:defaultValueIfNotFound;
		return defaultValueIfNotFound;
	}

	public boolean isV4BrokerEnabled() {
		return isV4BrokerEnabled;
	}

}
