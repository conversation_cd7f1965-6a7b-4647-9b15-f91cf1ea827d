package com.integral.adaptor.config;

import com.integral.system.configuration.IdcMBean;

/**
 * Created by IntelliJ IDEA.
 * User: kumark
 * Date: Mar 2, 2006
 * Time: 10:45:31 AM
 * To change this template use File | Settings | File Templates.
 */
public interface AdaptorConfigurationMBean extends IdcMBean
{

	public static final String IDC_ADAPTOR_AUTOCANCELLATION = "Idc.Adaptor.AutoCancellation";
	public static final String IDC_ADAPTOR_STALENESSCHECK = "Idc.Adaptor.StalenessCheck";
	public static final String IDC_ADAPTOR_OVERRIDE_STALENESSCHECK = "Idc.Adaptor.Override.StalenessCheck";
	public static final String IDC_ADAPTOR_STALENESSCHECK_TIME = "Idc.Adaptor.Stalenesscheck.Time";
	public static final String IDC_ADAPTOR_AUTOCANCELLATION_TIME = "Idc.Adaptor.AutoCancellation.Time";
	public static final String IDC_ADAPTOR_PROVIDER_INITPROPS = "Idc.Adaptor.Provider.InitProps";
	public static final String IDC_ADAPTOR_RATES_DELIVERYMODE = "Idc.Adaptor.Rates.DeliveryMode";
	public static final String IDC_ADAPTOR_HEARTBEAT_DELIVERYMODE = "Idc.Adaptor.Heartbeat.DeliveryMode";
	public static final String IDC_ADAPTOR_HEARTBEAT_DESTINATION_JNDI_PREFIX = "Idc.Adaptor.Heartbeat.Destination.JNDI.Prefix";
	public static final String IDC_ADAPTOR_PROVIDER_HEARTBEATCHECK_INTERVAL = "Idc.Adaptor.Provider.HeartbeatCheckInterval";
	public static final String IDC_IS_PROVIDERS_SHORTNAME = "IDC.IS.Providers.Shortname";
	public static final String IDC_IS_PROVIDERS_LIST_SHORTNAME = "IDC.IS.Providers.list.ShortName";
	public static final String IDC_IS_PROVIDERS_HEARTBEAT_CHECKENABLED = "IDC.IS.Providers.Heartbeat.checkEnabled";
	public static final String IDC_IS_ADAPTERS_SIMULATOR_DECLINE_COUNT = "IDC.IS.Adapters.Simulator.DeclineCount";
	public static final String IDC_ADAPTOR_ENVIRONMENT = "IDC.Adaptor.Environment";
	public static final String IDC_IS_ADAPTORS_SIMULATOR_SPREAD = "IDC.IS.Adapters.Simulator.Spread";
	public static final String IDC_ORDER_FIX_HEARTBEATCHECK = "IDC.ORDER.FIX.Heartbeatcheck";
	public static final String IDC_SERVER_URL = "IDC.Server.URL";
	public static final String IDC_ADAPTOR_THREADPOOL_SIZE = "Idc.Adaptor.ThreadPool.Size";
	public static final String IDC_ADAPTOR_THREADPOOL_WORKQUEUE_SIZE = "Idc.Adaptor.ThreadPool.WorkQueue.Size";
	public static final String IDC_ADAPTOR_RATES_THREADPOOL_SIZE = "Idc.Adaptor.Rates.ThreadPool.Size";
	public static final String IDC_ADAPTOR_RATES_THREADPOOL_WORKQUEUE_SIZE = "Idc.Adaptor.Rates.ThreadPool.WorkQueue.Size";
	public static final String IDC_ADAPTOR_USE_PROVIDER_SEQUENCE_NUMBER = "Idc.Adaptor.Use.Provider.Sequence";
	public static final String IDC_ADAPTOR_USE_EJB = "Idc.Adaptor.Use.EJB";
	public static final String IDC_ADAPTOR_REQUEST_HANLDER_FACTORY = "Idc.Adaptor.RequestHandlerFactory";
	public static final String IDC_ADAPTOR_GUID_VERSION = "Idc.Adaptor.Guid.Version";
	public static final String IDC_PROVISIONING_BASE_URL = "Idc.IS.Provisioning.Base.Url";
	public static final String IDC_MCAST_RESPONSE_HANDLER_CLASS = "Idc.Multicast.Response.Handler.Class";
    public static final String IDC_MCAST_RATE_SERIALIZER_VERSION="Idc.Multicast.Rate.Serializer.Version";
    String IDC_ADAPTOR_IMTP_ENABLED = "Idc.Adaptor.IMTPEnabled";
	public static final String IDC_ADAPTOR_TRADE_SCHEDULER_SERVICE_THREAD_POOLSIZE="Idc.Adaptor.Trade.SechedulerServcie.ThreadPoolSize";
	final String IDC_ADAPTOR_MULTICAST_HEARTBEAT_ENABLED = "Idc.Adaptor.Multicast.HeartBeat.Enabled";

	final String IDC_ADAPTOR_MULTICAST_HEARTBEAT_GROUP = "Idc.Adaptor.Multicast.HeartBeat.Group";

	final String IDC_ADAPTOR_MULTICAST_HEARTBEAT_PORT = "Idc.Adaptor.Multicast.HeartBeat.Port";
	final String IDC_PROCESS_NON_DEPLOYED_ADAPTOR = "IDC.IS.Process.NonDeployed.Adaptor";

	final String ADAPTOR_VENUEINTEGRATION_ENABLED ="Idc.Adaptor.VenueIntegration.Enabled";

	final String BROKER_V4_ENABLED = "Idc.BrokerAdaptor.BrokerV4.Enabled";
	 final String IDC_ADAPTOR_QUOTE_MARKET_DEPTH="Idc.Adaptor.MDF.Quote.MarketDepth";

	final String	IDC_ADAPTOR_MARKET_DATA_GROUP    	= "Idc.MV.Clob.MDF.Multicast.Group";
	 final String	IDC_ADAPTOR_MARKET_DATA_GROUP_PREFIX     	= IDC_ADAPTOR_MARKET_DATA_GROUP+".";
	 final String IDC_ADAPTOR_MARKET_DATA_PORT = "Idc.MV.Clob.MDF.Multicast.Port";

	
	public void setAutoCancellationApplicable(boolean autoCancellation);

	public boolean isAutoCancellationApplicable();

	public boolean isDefaultHeartBeatSenderApplicable();

	public void setStalenessCheckApplicable(boolean stalenessCheck);

	public boolean isStalenessCheckApplicable();

	public int getStalenessCheckTime();

	public void setStalenessCheckTime(int stalenessCheckTime);

	public int getAutoCancellationTime();

	public void setAutoCancellationTime(int autoCancellationTime);

	public boolean isOverrideStalenessCheck();

	public void setOverrideStalenessCheck(boolean overrideStalenessCheck);

	public String getProviderInitProps();

	public long getHeartbeatCheckInterval();

	public void setHeartbeatCheckInterval(long heartBeatInterval);

	public int getHeartbeatDeliveryMode();

	String getHeartbeatDestinationJNDIPrefix();

	public String getShortName();

	public String getDeclineCount();

	public boolean isSimulate();

	public boolean isHeartBeatCheckEnabled();

	public String getSimulatorSpread();

	public String getEnvironment();

	public String getProviderList();

	public String getServerURL();

	public int getRatesDeliveryMode();

	public int getThreadPoolSize();

	public int getThreadPoolWorkQueueSize();

	public int getRatesThreadPoolSize();

	public int getRatesThreadPoolWorkQueueSize();

	public boolean isUseProviderSequenceNumber();

	public boolean isEJBInUse();

	public String getRequestHandlerFactory();

	public String getMulticastResponseHandler();

    public int getSerializerVersion();

    boolean isIMTPEnabled();
	
	int getTradeSchedulerServiceThreadPoolSize();

	public boolean isMulticastHeartBeatEnabled();
    
	public String getMulticastHeartBeatGroup();
	
	public int getMulticastHeartBeatPort();
	boolean isProcessNonDeployedAdaptorsEnabled(String adaptorName);
	public String getMDFMulticastGroup(String orgName, String ccyPair);
	public boolean isV4BrokerEnabled();

}
