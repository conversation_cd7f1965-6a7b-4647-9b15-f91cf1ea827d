package com.integral.adaptor.login;

import com.integral.adaptor.comm.imtp.IMTPRequestHandler;
import com.integral.imtp.session.IMTPSession;
import com.integral.imtp.session.IMTPSessionManager;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.message.LoginMessage;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.ResponseMessageC;
import com.integral.jmsx.JMSXMLMBeanC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.adaptor.util.AdaptorUtil;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class AdaptorLoginManagerC
{

	private Log log = LogFactory.getLog(this.getClass());

	private static Map uniqueRateDest = new ConcurrentHashMap();
	private static Map loginInfoMap = new HashMap();
	private static final String SEPARATOR = "#";
	private static AdaptorLoginManagerC _instance = new AdaptorLoginManagerC();
	private static Map<String, Boolean> serverMulticastMap = new ConcurrentHashMap<String, Boolean>();
	private static boolean sendMulticastRates = true;

    private static boolean textMessageEnabledForAllServers = false;

	private AdaptorLoginManagerC()
	{
	}

	public static AdaptorLoginManagerC getInstance()
	{
		return _instance;
	}

	public ResponseMessage handleLogin(LoginMessage message)
	{
		ResponseMessage res = new ResponseMessageC();
		String providerName = (String) message.getProperty("PROVIDER.NAME");
        String providerType = AdaptorUtil.getProviderType( message );
		String isid = message.getServerId();
		String brokerId = message.getBrokerId();
		if ( !brokerExists(brokerId) && !ISFactory.getInstance().getISMBean().getVirtualServer().isLPAutoDeployEnabled())
		{
			res.setStatus(ResponseMessage.FAILURE);
			res.setFailureReason("Broker : " + brokerId + " does not exist");
			log.error("AdaptorLoginManagerC.handleLogin: Login failed for Integration Server " + isid + ". Broker->" + brokerId + " does not exist");
			return res;
		}
		updateLoginInfo(isid, message);
		if ( !message.isMulticastListener() && message.getRateDestination() != null && !message.getRateDestination().trim().equals("") )
		{
			updateUniqueRateDestinations(providerName,providerType, message.getBrokerId() + SEPARATOR + message.getRateDestination());
		}
		if ( message.isMulticastListener() )
		{
			log.info("Received subscription from a multicast listener: " + isid);
		}
		updateServerMulticastCounter(isid, message);
        updateTextMessageFlag();
		res.setStatus(ResponseMessage.SUCCESS);
		log.warn("AdaptorLoginManagerC.handleLogin: Integration server " + isid + " has logged in. Message[" + message + "]");
		return res;
	}

	private static void updateServerMulticastCounter(String isid, LoginMessage message)
	{
//		synchronized ( serverMulticastMap )
//		{
//			boolean sendRatesOverMulticastLocal = false;
//			serverMulticastMap.put(isid, message.isMulticastListener());
//			Collection<Boolean> values = serverMulticastMap.values();
//			for ( Boolean multicastEnable : values )
//			{
//				if ( multicastEnable )
//				{
//					sendRatesOverMulticastLocal = true;
//					break;
//				}
//			}
//			//sendMulticastRates = sendRatesOverMulticastLocal;
//		}
	}

    private static void updateTextMessageFlag() 
    {
        synchronized (loginInfoMap)
        {
            boolean textMessageEnabledForAllServersLocal = true;
            Collection <LoginMessage> values = loginInfoMap.values();
            for (LoginMessage loginMessage : values)
            {
                if(!loginMessage.isTextMessages())
                {
                	textMessageEnabledForAllServersLocal = false;
                    break;
                }
            }
            textMessageEnabledForAllServers = textMessageEnabledForAllServersLocal;
        }
    }

	public boolean isSendMulticastRates()
	{
		return sendMulticastRates;
	}

    public boolean isServerIMTPEnabled(String serverName){
        Object obj = loginInfoMap.get(serverName);
        if(obj == null){
            return false;
        }
        else{
            return ((LoginMessage)obj).isIMTPListener();
        }
    }

    public IMTPSession getLoggedInIMTPSession(String serverId){
        IMTPSession result = null;
        if(AdaptorLoginManagerC.getInstance().isServerIMTPEnabled(serverId)){
            LoginMessage login = (LoginMessage)AdaptorLoginManagerC.getInstance().getLoginInfo().get(serverId);
            String sessionId = (String) login.getProperty(IMTPRequestHandler.IMTP_SESSION_ID_KEY);
            try {
                result = IMTPSessionManager.getInstance().getSession(sessionId, null); //don't want to load a new one here.
            } catch (IOException e) {
                log.error("Unable to obtain IMTP Session for:"+serverId+", serverId:"+serverId,e);
            }
        }
        return result;
    }


	public List getUniqueRateDestinations(String providerName,String providerType)
	{
		return (List) uniqueRateDest.get( getRateDestinationKey(providerName,providerType));
	}

	private void updateLoginInfo(String isid, LoginMessage message)
	{
		synchronized ( loginInfoMap )
		{
			if ( isid != null && !"".equals(isid.trim()) )
			{
				String isList = checkTradeQueue(message);
				if ( isList != null )
					log.warn("AdaptorLoginManagerC.updateLoginInfo: Integration server" + isList + " already logged in with the same trade queue " + message.getTradeDestination());
				loginInfoMap.put(isid, message);
			}
		}
	}

	private String checkTradeQueue(LoginMessage message)
	{
		Iterator iter = loginInfoMap.values().iterator();
		StringBuffer isList = new StringBuffer();
		int count = 0;
		while (iter.hasNext())
		{
			LoginMessage msg = (LoginMessage) iter.next();
			if ( message.getTradeDestination() != null && message.getTradeDestination().equals(msg.getTradeDestination()) )
			{
				isList.append(" : " + msg.getServerId());
				count++;
			}
		}
		if ( count > 0 )
			return isList.toString();
		else
			return null;
	}

	private void updateUniqueRateDestinations(String providerName,String providerType, String rateTopic)
	{
		synchronized ( uniqueRateDest )
		{
			List topicList = (List) uniqueRateDest.get( getRateDestinationKey(providerName,providerType));
			if ( topicList != null )
			{
				if ( rateTopic != null && !"".equals(rateTopic.trim()) )
					if ( !topicList.contains(rateTopic) )
						topicList.add(rateTopic);
			}
			else
			{
				topicList = new ArrayList();
				topicList.add(rateTopic);
				uniqueRateDest.put( getRateDestinationKey(providerName,providerType), topicList);
			}
		}
	}

	public Map getLoginInfo()
	{
		return loginInfoMap;
	}
	
	public Map getRateDestinations()
	{
		return uniqueRateDest;
	}

	public void removeLoginInfo(String serverId)
	{
		synchronized ( loginInfoMap )
		{
			loginInfoMap.remove(serverId);
		}
	}

	public void resetUniqueRateDestinations()
	{
		uniqueRateDest.clear();
		Iterator iter = loginInfoMap.keySet().iterator();
		while (iter.hasNext())
		{
			String serverId = (String) iter.next();
			LoginMessage msg = (LoginMessage) loginInfoMap.get(serverId);
			String brokerId = msg.getBrokerId();
			String providerName = (String) msg.getProperty("PROVIDER.NAME");
			String providerType = AdaptorUtil.getProviderType( msg );
			//Update the rate destination ONLY is the listener is not a multicast listener.
			if ( !msg.isMulticastListener() && msg.getRateDestination() != null && !msg.getRateDestination().trim().equals("") )
			{
				updateUniqueRateDestinations(providerName,providerType, brokerId + SEPARATOR + msg.getRateDestination());
			}
			if ( msg.isMulticastListener() )
			{
				log.info("Received subscription from a multicast listener: " + msg.getServerId());
			}
		}
	}

	public String getPersistenceMessageQueue(String serverId)
	{
		LoginMessage msg = (LoginMessage) loginInfoMap.get(serverId);
		if ( msg == null )
		{
			log.error("AdaptorLoginManagerC.getPersistenceMessageQueue: No Login info for server id " + serverId);
			return null;
		}
		return msg.getPersistenceMessageQueue();
	}

	public String getTradeQueue(String serverName)
	{
		Object obj = loginInfoMap.get(serverName);
		if ( obj == null )
		{
			return null;
		}
		else
		{
			return ((LoginMessage) obj).getTradeDestination();
		}
	}

	/**
     * Check whether TextMessage enabled on server or not.
     * @param serverName
     * @return
     */
    public boolean isTextMessageEnabled(String serverName) {
    	if(isTextMessageEnabledForAllServers())
    		return true;
    	if(serverName == null)
    		return false;
        Object obj = loginInfoMap.get(serverName);
        if (obj == null) {
            return false; // Send false since server handles Object messages.
        }
        else{
            return ((LoginMessage) obj).isTextMessages();
        }
    }
    
    public boolean isTextMessageEnabledForAllServers() 
    {
    	return textMessageEnabledForAllServers;
    }

    /**
	 *
	 * @param serverName
	 * @return BrokerId configured for the serverName
	 */
	public String getBroker(String serverName)
	{
		Object obj = loginInfoMap.get(serverName);
		if ( obj == null )
		{
			return null;
		}
		else
		{
			return ((LoginMessage) obj).getBrokerId();
		}
	}

	/**
	 *
	 * @param serverId
	 * @return true if the server is logged in
	 */
	public boolean isServerLoggedIn(String serverId)
	{
		return loginInfoMap.get(serverId) != null;
	}

	/**
	 * rateDestination is in format "BROKERID#TOPICNAME"
	 * @param rateDestination
	 * @return
	 */
	public String getBrokerName(String rateDestination)
	{
		return rateDestination.substring(0, rateDestination.indexOf(SEPARATOR));
	}

	/**
	 *
	 * @param rateDestination
	 * @return
	 */
	public String getTopicName(String rateDestination)
	{
		return rateDestination.substring(rateDestination.indexOf(SEPARATOR) + 1);
	}

	public String getRFSTopic(String serverName)
	{
		Object obj = loginInfoMap.get(serverName);
		if ( obj == null )
		{
			return null;
		}
		else
		{
			return ((LoginMessage) obj).getRFSRateTopic();
		}
	}

	/**
	 *
	 * @param brokerId
	 * @return true if broker existes
	 */
	private boolean brokerExists(String brokerId)
	{
		if ( brokerId != null && !brokerId.trim().equals("") )
		{
			if ( JMSXMLMBeanC.getInstance().getBroker(brokerId) != null )
			{
				return true;
			}
		}
		return false;
	}

    private String getRateDestinationKey(String providerName,String providerType)
    {
       return  new StringBuilder(50).append( providerName ).append( providerType ).toString();
    }
}
