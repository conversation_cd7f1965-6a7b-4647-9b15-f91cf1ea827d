package com.integral.adaptor.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.finance.fx.FXRateConvention;
import com.integral.is.ISCommonConstants;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.ResponseMessageC;
import com.integral.is.message.ISMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.sessions.Session;

public class AdaptorUtil
{

	private static Log log = LogFactory.getLog(AdaptorUtil.class);
    private static ConcurrentHashMap<String, FXRateConvention> rateConventions = new ConcurrentHashMap<String, FXRateConvention>();

	public static ResponseMessage getResponseMessage(boolean success, String reason, long startTime, long processedTime, long sentTime)
	{
		ResponseMessage resp = new ResponseMessageC();
		if ( success )
		{
			resp.setStatus(ResponseMessage.SUCCESS);
		}
		else
		{
			resp.setStatus(ResponseMessage.FAILURE);
			resp.setFailureReason(reason);
		}
		return resp;
	}

	public static ResponseMessage getResponseMessage(boolean success, String reason)
	{
		ResponseMessage resp = new ResponseMessageC();
		if ( success )
		{
			resp.setStatus(ResponseMessage.SUCCESS);
		}
		else
		{
			resp.setStatus(ResponseMessage.FAILURE);
			resp.setFailureReason(reason);
		}
		return resp;
	}

	public static String getCurrencyPair(String baseCcy, String variableCcy)
	{
		return baseCcy + AdaptorConstantC.CCYPAIR_SEPERATOR + variableCcy;
	}

	public static HashMap<String, String> getAdaptorInstanceURLMap()
	{
		HashMap<String, String> adaptorInstanceURL = new HashMap<String, String>();
		String adaptorInstanceHostAddress = getAdaptorInstanceInitURLAddress();
		String adaptorInstanceHostName = getAdaptorInstanceInitURLName();
		if ( adaptorInstanceHostName != null )
			adaptorInstanceURL.put(ISCommonConstants.HOSTNAME_URL, adaptorInstanceHostName);
		if ( adaptorInstanceHostAddress != null )
			adaptorInstanceURL.put(ISCommonConstants.IP_ADDRESS_URL, adaptorInstanceHostAddress);

		log.warn("AdaptorUtil.getAdaptorInstanceURLMap ->" + adaptorInstanceURL);

		return adaptorInstanceURL;
	}

	private static String getLocalServerName()
	{
		String hostname = null;
		try
		{
			hostname = InetAddress.getLocalHost().getHostName();
		}
		catch ( UnknownHostException e )
		{
			log.warn("AdaptorUtil.getLocalServerName: ServerHostAddress is not found");
		}
		return hostname;
	}

	private static String getLocalServerAddress()
	{
		String hostAddress = null;

		try
		{
			hostAddress = InetAddress.getLocalHost().getHostAddress();
		}
		catch ( UnknownHostException e )
		{
			log.warn("AdaptorUtil.getLocalServerAddress: ServerHostName is not found");
		}
		return hostAddress;
	}

	public static String getAdaptorInstanceInitURLName()
	{
		String serverName = getLocalServerName();
		if ( serverName != null )
		{
			return getAdaptorInstanceInitURL(serverName);
		}

		return null;

	}

	public static String getAdaptorInstanceInitURLAddress()
	{
		String serverAddress = getLocalServerAddress();
		if ( serverAddress != null )
		{
			return getAdaptorInstanceInitURL(serverAddress);
		}

		return null;
	}

	private static String getAdaptorInstanceInitURL(String key)
	{
		AdaptorConfiguration adaptorConfig = AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
		String serverURL = adaptorConfig.getServerURL();
		if ( serverURL != null )
		{
			serverURL = serverURL.trim();
			int index = serverURL.lastIndexOf(AdaptorConstantC.ADAPTOR_INIT_URL_PORT_PREFIX);
			if ( index != -1 )
			{
				try
				{
					int serverPort = Integer.parseInt(serverURL.substring(index + 1));
					String instanceURL = AdaptorConstantC.ADAPTOR_INIT_URL_PREFIX + key + AdaptorConstantC.ADAPTOR_INIT_URL_PORT_PREFIX + serverPort + AdaptorConstantC.ADAPTOR_INIT_URL_SUFFIX;
					return instanceURL;
				}
				catch ( NumberFormatException nfe )
				{
					log.warn("AdaptorUtil.getAdaptorInstanceInitURL: serverURL port is not valid");
				}
			}
			log.warn("AdaptorUtil.getAdaptorInstanceInitURL: serverURL property is not valid");
		}
		log.warn("AdaptorUtil.getAdaptorInstanceInitURL: serverURL property is not found");
		return null;
	}

    public static String getProviderType( ISMessage message)
    {
       return (String) message.getProperty(ISCommonConstants.PROVIDER_TYPE);
    }

    public static FXRateConvention getFXRateConvention(String name) {
    	FXRateConvention rateCon = rateConventions.get(name);
		if ( rateCon == null) {
			rateCon = (FXRateConvention) getObject(com.integral.finance.fx.FXRateConvention.class, "shortName", name);
			rateConventions.put(name, rateCon);
		}
		return rateCon;
	}

    protected static Object getObject(Class ifsClass, String fieldName, String value) {
		try {
			Session session = PersistenceFactory.newSession();
			ExpressionBuilder eb = new ExpressionBuilder();
			Expression expr = eb.get(fieldName).equal(value);
			return session.readObject(ifsClass, expr);
		} catch (PersistenceException e) {
			log.error("AdaptorUtil.getObject: " + ifsClass + ":" + fieldName + ":" + value, e);
			return null;
		}
	}
}
