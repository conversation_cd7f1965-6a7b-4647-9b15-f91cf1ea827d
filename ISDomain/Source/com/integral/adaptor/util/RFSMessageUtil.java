package com.integral.adaptor.util;

import java.util.Map;
import java.io.Serializable;

import com.integral.adaptor.handler.rfs.RFSExpiryHandler;
import com.integral.is.common.ProviderQuoteIdFactory;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.rfs.RFSFXLeg;
import com.integral.is.message.rfs.RFSFXLegC;
import com.integral.is.message.rfs.RFSFXRate;
import com.integral.is.message.rfs.RFSFXRateC;
import com.integral.is.message.rfs.RFSMarketRate;
import com.integral.is.message.rfs.RFSMarketRateC;
import com.integral.is.message.rfs.RFSRequestMessage;
import com.integral.is.message.rfs.RFSResponseMessage;
import com.integral.is.message.rfs.RFSUnsubscribe;
import com.integral.is.ISCommonConstants;

/**
 *
 * <AUTHOR>
 * Util class for converting RFS Objects one into another.
 *
 */
public class RFSMessageUtil
{

	/**
	 * get unsupported RFSMarketRate from the RFSRequestMessage
	 * @param request
	 * @return unsupported RFS Rate
	 */
	public static RFSMarketRate getUnsupportedRFSRate(RFSRequestMessage request)
	{
		String providerShortName = request.getProviderShortName();
		String requestId = request.getRequestId();
		RFSMarketRate staleRate = new RFSMarketRateC();
		staleRate.setStale(true);
		staleRate.setQuoteStaleReason("UnsupportedRFSRate");
		staleRate.setProviderQuoteId("");
		staleRate.setQuoteId(ProviderQuoteIdFactory.newProviderQuoteId(providerShortName, System.currentTimeMillis()));
		staleRate.setBaseCurrency(request.getBaseCurrency());
		staleRate.setVariableCurrency(request.getVariableCurrency());
		staleRate.setProviderShortName(providerShortName);
		staleRate.setStreamId(request.getStreamId());
		staleRate.setUserShortName(request.getUserShortName());
		staleRate.setLeShortName(request.getLeShortName());
		staleRate.setOrgShortName(request.getOrgShortName());
		staleRate.setRequestId(requestId);
		staleRate.setTradeClassification(request.getTradeClassification());
		RFSFXLeg oldNearLeg = request.getNearLeg();
		if ( oldNearLeg != null )
		{
			RFSFXLeg nearLeg = new RFSFXLegC();
			nearLeg.setDealtCurrency(oldNearLeg.getDealtCurrency());
			nearLeg.setTenor(oldNearLeg.getTenor());
			if ( oldNearLeg.getBidRate() != null )
				nearLeg.setBidRate(new RFSFXRateC());
			if ( oldNearLeg.getOfferRate() != null )
				nearLeg.setOfferRate(new RFSFXRateC());
			staleRate.setNearLeg(nearLeg);
		}
		RFSFXLeg oldFarLeg = request.getFarLeg();
		if ( oldFarLeg != null )
		{
			RFSFXLeg farLeg = new RFSFXLegC();
			farLeg.setDealtCurrency(oldFarLeg.getDealtCurrency());
			farLeg.setTenor(oldFarLeg.getTenor());
			if ( oldFarLeg.getBidRate() != null )
				farLeg.setBidRate(new RFSFXRateC());
			if ( oldFarLeg.getOfferRate() != null )
				farLeg.setOfferRate(new RFSFXRateC());
			staleRate.setFarLeg(farLeg);
		}
		staleRate.setTimeToLiveInSeconds(RFSExpiryHandler.getInstance().getNetRequestExpiryTime(providerShortName, requestId));
		return staleRate;
	}

	/**
	 * get unsupported RFSMarketRate from the given RFSMarketRate
	 * @param rate
	 * @return Unsupported RFS Rate
	 */
	public static RFSMarketRate getUnsupportedRFSRate(RFSMarketRate rate)
	{
		String providerShortName = rate.getProviderShortName();
		String requestId = rate.getRequestId();
		RFSMarketRate staleRate = new RFSMarketRateC();
		staleRate.setStale(true);
		staleRate.setQuoteStaleReason("UnsupportedRFSRate");
		staleRate.setProviderQuoteId("");
		staleRate.setQuoteId(ProviderQuoteIdFactory.newProviderQuoteId(providerShortName, System.currentTimeMillis()));
		staleRate.setBaseCurrency(rate.getBaseCurrency());
		staleRate.setVariableCurrency(rate.getVariableCurrency());
		staleRate.setProviderShortName(providerShortName);
		staleRate.setStreamId(rate.getStreamId());
		staleRate.setUserShortName(rate.getUserShortName());
		staleRate.setLeShortName(rate.getLeShortName());
		staleRate.setOrgShortName(rate.getOrgShortName());
		staleRate.setRequestId(requestId);
		staleRate.setTradeClassification(rate.getTradeClassification());
		RFSFXLeg oldNearLeg = rate.getNearLeg();
		if ( oldNearLeg != null )
		{
			RFSFXLeg nearLeg = new RFSFXLegC();
			nearLeg.setDealtCurrency(oldNearLeg.getDealtCurrency());
			nearLeg.setTenor(oldNearLeg.getTenor());
			nearLeg.setValueDate(oldNearLeg.getValueDate());
			if ( oldNearLeg.getBidRate() != null )
				nearLeg.setBidRate(new RFSFXRateC());
			if ( oldNearLeg.getOfferRate() != null )
				nearLeg.setOfferRate(new RFSFXRateC());
			staleRate.setNearLeg(nearLeg);
		}
		RFSFXLeg oldFarLeg = rate.getFarLeg();
		if ( oldFarLeg != null )
		{
			RFSFXLeg farLeg = new RFSFXLegC();
			farLeg.setDealtCurrency(oldFarLeg.getDealtCurrency());
			farLeg.setTenor(oldFarLeg.getTenor());
			farLeg.setValueDate(oldFarLeg.getValueDate());
			if ( oldFarLeg.getBidRate() != null )
				farLeg.setBidRate(new RFSFXRateC());
			if ( oldFarLeg.getOfferRate() != null )
				farLeg.setOfferRate(new RFSFXRateC());
			staleRate.setFarLeg(farLeg);
		}
		staleRate.getTiming().setTime(ISCommonConstants.EVENT_TIME_RATE_EFFECTIVE, System.currentTimeMillis());
		staleRate.getTiming().setTime(ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE, System.currentTimeMillis());
		staleRate.getTiming().setTime(ISCommonConstants.EVENT_TIME_DISP_ADAPTER_SENT_RATE, System.currentTimeMillis());
		staleRate.setTimeToLiveInSeconds(RFSExpiryHandler.getInstance().getNetRequestExpiryTime(providerShortName, requestId));
		return staleRate;
	}

	/**
	 * get RFSUnsubscibe from the given RFSRequestMessage
	 * @param request
	 * @return RFSUnsubscribe message
	 */
	public static RFSUnsubscribe getRFSUnsubscibe(RFSRequestMessage request)
	{
		RFSUnsubscribe rfsUnsubscibe = MessageFactory.newRFSUnsubscribe();
		rfsUnsubscibe.setBaseCurrency(request.getBaseCurrency());
		rfsUnsubscibe.setVariableCurrency(request.getVariableCurrency());
		rfsUnsubscibe.setRequestId(request.getRequestId());
		rfsUnsubscibe.setServerId(request.getServerId());
		rfsUnsubscibe.setStreamId(request.getStreamId());
		rfsUnsubscibe.setTradeClassification(request.getTradeClassification());
		rfsUnsubscibe.setProviderShortName(request.getProviderShortName());
		rfsUnsubscibe.setLeShortName(request.getLeShortName());
		rfsUnsubscibe.setUserShortName(request.getUserShortName());
		rfsUnsubscibe.setOrgShortName(request.getOrgShortName());
		rfsUnsubscibe.setNearLeg(request.getNearLeg());
		rfsUnsubscibe.setFarLeg(request.getFarLeg());
		Map properties = request.getProperties();
		if ( properties != null )
		{
			for ( Object key : properties.keySet() )
			{
				rfsUnsubscibe.setProperty((Serializable) key, (Serializable) (properties.get(key)));
			}
		}
		return rfsUnsubscibe;
	}

	/**
	 * update RFSResponseMessage message from the RFSRequestMessage
	 * @param response
	 * @param request
	 */
	public static void update(RFSResponseMessage response, RFSRequestMessage request)
	{
		response.setProvider(request.getProviderShortName());
		response.setOrganization(request.getOrgShortName());
		response.setUser(request.getUserShortName());
		response.setLegalEntity(request.getLeShortName());
	}

	/**
	 *
	 * @param rate
	 * @param req
	 */
	public static void update(RFSMarketRate rate, RFSRequestMessage req)
	{
		rate.setTradeClassification(req.getTradeClassification());
		rate.setProviderShortName(req.getProviderShortName());
		rate.setStreamId(req.getStreamId());
		rate.setOrgShortName(req.getOrgShortName());
		rate.setUserShortName(req.getUserShortName());
		rate.setLeShortName(req.getLeShortName());
		RFSFXLeg reqNearLeg = req.getNearLeg();
		RFSFXLeg rateNearLeg = rate.getNearLeg();
		RFSFXLeg reqFarLeg = req.getFarLeg();
		RFSFXLeg rateFarLeg = rate.getFarLeg();
		if ( reqNearLeg != null && rateNearLeg != null )
			rateNearLeg.setTenor(reqNearLeg.getTenor());
		if ( reqFarLeg != null && rateFarLeg != null )
			rateFarLeg.setTenor(reqFarLeg.getTenor());
	}

	/**
	 *
	 * @param rate
	 * @return if the rate is stale
	 */
	public static boolean isStale(RFSMarketRate rate)
	{
		return checkStalenessOfLeg(rate.getNearLeg()) && checkStalenessOfLeg(rate.getFarLeg());
	}

	private static boolean checkStalenessOfLeg(RFSFXLeg fxLeg)
	{
		if ( fxLeg == null )
			return true;
		else
		{
			RFSFXRate bidRate = fxLeg.getBidRate();
			RFSFXRate offerRate = fxLeg.getOfferRate();
			return (bidRate == null || bidRate.getDealtAmount() == 0 || bidRate.getRate() == 0) && (offerRate == null || offerRate.getDealtAmount() == 0 || offerRate.getRate() == 0);
		}
	}
}
