package com.integral.adaptor.order;

import com.integral.finance.trade.Tenor;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;

import java.util.Hashtable;

public interface CommonAdaptorFrameworkConstants
{
	public static final String WF_PARAM_TRADE_CHANNEL = "TradeChannel";
	//    public static final String WF_PARAM_MAX_SHOW = "DS";
	//    public static final String WF_PARAM_MIN_QTY = "MinQty";
	public static final String WF_PARAM_EXPIRY_TIME = "ExpiryTime";
	public static final String WF_PARAM_LP_CROSSING_ENABLED = "LPCrossingEnabled";
	public static final String WF_PARAM_EXECUTION_TYPE = "ExecutionType";
	public static final String WF_PARAM_ORDER_TYPE = "OrderType";
	public static final String WF_PARAM_TRANSACTION_ID = "transactionId";

	/**
	 * Boolean parameter meaning that the order should be re-loaded by the OrderRequestService.
	 *
	 * @see OrderAdaptorStartupC#startup(String,Hashtable)
	 */
	public static final String WF_PARAM_RELOAD = "Reload";

	public static final String EXECINST_OK_TO_CROSS = "B";
	public static final String MESSAGE_ID = "MessageId";
	public static final String MSG_TOPIC_REQUEST = "REQUEST";
	public static final String MESSAGE_HANDLER = "messageHandler";
	public static final String TRADER_TRADE_CHANNEL = "Trader/Order";
	public static final String TRADER_CHANNEL = "DirectFXTrader";
	public static final String MAKEPRICE_CREATE_TYPE = "LIMIT";
	public static final String ACCEPT_TYPE = "QUOTED";
	public static final String CREATE_TYPE = "PRICE";
	public static final String QUOTE_CONVENTION = "STDQOTCNV";
	public static final String SINGLE_LEG = "singleLeg";
	public static final Tenor SPOT_TENOR = new Tenor(Tenor.SPOT);
	public static final String DATE_TIME_FORMAT = "yyyyMMdd";

	public static final String INVALID_TRADING_PARTY = "InvalidTradingParty";
	public static final String LE_NOT_SET_ON_TRADING_PARTY = "LegalEntityNotSetOnTradingParty";
	public static final String ORDERS_ORG_NOT_CONFIGURED = "OrdersOrgNotConfigured";
	public static final String INVALID_USER = "InvalidUser";
	public static final String UNKNOWN_ORDER = "UnknownOrder";
	public static final String TRADE_RECOVERY_DATA_MISMATCH_LEGAL_ENTITY = "LegalEntityMismatch";
	public static final String TRADE_RECOVERY_DATA_MISMATCH_CURRENCY_PAIR = "CurrencyPairMismatch";
	public static final String TRADE_RECOVERY_DATA_MISMATCH_BUY_SELL = "BuySellMismatch";
	public static final String CURRENCY_PAIR = "CP";
	public static final String MSG_REC_AT_WORKFLOW_TS = "messageRecievedTS";
	public static final MessageEvent MSG_EVENT_WITHDRAW = MessageFactory.newMessageEvent("WITHDRAW");
	public static final MessageEvent MSG_EVENT_WITHDRAW_ALL = MessageFactory.newMessageEvent("WITHDRAWALL");
	public static final MessageEvent MSG_EVENT_TRADE_CONFIRMED = MessageFactory.newMessageEvent("CONFIRM_TRADE");
	public static final String TOO_LATE_TO_CANCEL = "TooLateToCancel";
	public static final String CANCELLATION_DUE_TO_MIN_FILL_FACTOR = "CancellationDueToMinFillFactor";
	public static final String SUBSCRIPTION_USER = "SubscriptionUser";
	public static final String AUTODEALER_USER = "AutoDealer";
	public static final String AUTODEALER_USER_LEGAL_ENTITY = "AutoDealerLegalEntity";
}
