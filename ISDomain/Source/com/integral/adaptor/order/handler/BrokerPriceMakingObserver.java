package com.integral.adaptor.order.handler;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.config.BrokerConfigurationService;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.exception.IdcException;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.functor.ConfigChangeListener;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.mdf.MDFConfigMBeanC;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.server.VirtualServerType;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;

import java.util.Collection;
import java.util.Map;

/**
 * Handle changes are done to the Broker PriceMaking relation configurations
 *  * Broker PriceMaking MasterControls: Send notifications to MatchingEngine
 *  * LiquidityProviders enabled/disable: Send notifications to MatchingEngine
 *  * Product StreamPrices/OrderExecution enable/disable: Send notifications to MatchingEngine
 *  * New LiquidityGroup is created: Refresh the providerList if the currency server is BA & hosts the broker
 */
public class BrokerPriceMakingObserver implements MessageHandler
{
	public static final String LG_CREATION = "LgCreated";
	public static final String BROKER_PRICE_MAKING = "BrokerPriceMaking";
//	public static final String CHANNEL_ESP = "ESP";
//	public static final String CHANNEL_RFS = "RFS";
//	public static final String CHANNEL_SPOT_MDS = "SPOT_MDS";
    private Log log = LogFactory.getLog(BrokerPriceMakingObserver.class);

	public Message handle(Message message) throws IdcException {
		// Check if organization is a broker
		// Send LP Status enable/disable
		Map props = message.getMap();
		String orgName = (String) props.get(ISCommonConstants.ORGANIZATION_KEY);
		String actionKey = (String) props.get(ISCommonConstants.ACTION_KEY);

		if (MDFConfigMBeanC.getInstance().isMDFConfigPublisherEnabled()) {
			Organization organization = ISUtilImpl.getInstance().getOrg(orgName);
			if (organization == null || organization.isMasked() ||
					(!organization.isBroker() && !organization.isLiquidityGroup())) {
				return message;
			}

			String newStatusString = (String) props.get(ISCommonConstants.MSG_EVENT_STATUS);
			String channel = (String) props.get(ISCommonConstants.CHANNEL);
			if (actionKey.equals(ISCommonConstants.ORDER_EXECUTION_KEY)) {
				if (channel != null && channel.equals(BROKER_PRICE_MAKING)) {
					// Broker PriceMaking --> Master Control --> Order Execution is changed
					 ConfigChangeListener.getInstance().notifyBrokerPriceMakingMasterControlOrderExecutionUpdate(organization,
							newStatusString.equals(ISCommonConstants.ON_KEY));
				}
				else {
					// Master Control --> Order Execution is changed (for LG / Broker)
					ConfigChangeListener.getInstance().notifyMasterControlOrderExecutionUpdate(organization,
							newStatusString.equals(ISCommonConstants.ON_KEY));
				}
			}
			else if (actionKey.equals(ISCommonConstants.STREAM_PRICES_KEY)) {
				if (channel != null && channel.equals(BROKER_PRICE_MAKING)) {
					// Broker PriceMaking --> Master Control --> Stream Prices --> All/ESP is changed
					// For now, consider only ESP, as ME supports only ESP
					ConfigChangeListener.getInstance().notifyBrokerPriceMakingMasterControlStreamPricesUpdate(organization,
							newStatusString.equals(ISCommonConstants.ON_KEY));
				}
				else {
					// Master Control --> Stream Prices is changed (for LG / Broker)
					ConfigChangeListener.getInstance().notifyMasterControlStreamPricesUpdate(organization,
							newStatusString.equals(ISCommonConstants.ON_KEY));
				}
			}
			else if (actionKey.equals(ISCommonConstants.LP_ORGANIZATION)) {
				// Currently, this is sent only when Broker PriceMaking --> Liquidity Providers --> LP Status is changed
				String lpName = (String) props.get(ISCommonConstants.MAKER_KEY);
				Organization lp = ISUtilImpl.getInstance().getOrg(lpName);
				if (lp != null) {
					ConfigChangeListener.getInstance().notifyBrokerPriceMakingLpStatusUpdate(organization, lp,
							newStatusString.equals(ISCommonConstants.ON_KEY));
				}
			}
		}

		String s = ConfigurationFactory.getServerMBean().getVirtualServerType();
		Organization org = OrganizationUtil.getOrganization(orgName);
		if (org != null && s != null && s.equals(VirtualServerType.BrokerAdaptor)) {
			BrokerConfigurationService brokerConfigurationService = BrokerConfigurationServiceFactory.getBrokerConfigurationService();
			Collection<String> brokerNames = brokerConfigurationService.getDeployedBrokerOrganizationNames();
			if (org.isLiquidityGroup()) {
				Organization broker = OrganizationUtil.getBrokerOfLiquidityGroup(org);
				if (broker != null) {
					if (brokerNames != null && brokerNames.contains(broker.getShortName())) {
						if (actionKey.equals(LG_CREATION)) {
							// When LG is created, add the newly created LG in the provider list of the server
							ISFactory.getInstance().getServicProvidersMBean().onNotification();
						}
						else {
							// LG --> MasterControl Pricing/Execution status change
							BrokerAdaptorUtil.getInstance().recreateProductProvisionForLiquidityGroup(org, broker);
						}
					}
				}
			}
			else if (org.isBroker() && brokerNames != null && brokerNames.contains(org.getShortName())) {
				// Broker -->
				// 	*  Liquidity Rules --> MasterControl Pricing/Execution status change
				// 	*  PriceMaking --> MasterControl Pricing/Execution status change
				// 	*  PriceMaking --> Liquidity Providers --> LP Status is changed
				Collection<Organization> lgList = org.getRelatedActiveOrganizations(OrganizationRelationship.B_LG_RELATIONSHIP);
				if (lgList != null && !lgList.isEmpty()) {
					for (Organization lg: lgList) {
						BrokerAdaptorUtil.getInstance().recreateProductProvisionForLiquidityGroup(lg, org);
					}
				}
			}
		}
		return message;
	}

}
