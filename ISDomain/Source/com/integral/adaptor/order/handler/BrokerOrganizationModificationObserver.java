package com.integral.adaptor.order.handler;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.TradingVenueFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.is.rex.provision.RexProvisionCache;
import com.integral.is.rex.provision.V4EmsRexPovisionCache;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Map;

public class BrokerOrganizationModificationObserver implements MessageHandler
{
    private Log log = LogFactory.getLog( BrokerOrganizationModificationObserver.class );

	public Message handle( Message message ) throws IdcException
	{
		Map props = message.getMap();

		String notificationType = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

		if ( !OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_UPDATEBROKERORG.equals(notificationType) )
		{
			return message;
		}

		String organizationName = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_SHORTNAME);
		Organization org = ISUtilImpl.getInstance().getOrg(organizationName);
		Organization brokerOrg = org.getBrokerOrganization();
		Collection<String> mainList = OrderConfiguration.getInstance().getOrderProvidersList();
		Collection<Organization> extendedList = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();

        //BrokerCustomer's venue access changes
        Collection<Organization> provisionedOrgs = ISUtilImpl.getInstance().getProvisionedOrgs();
        if(provisionedOrgs.contains( org )) {
            Organization brokerForCustomer = org.getBrokerOrganization();
            if ( null != brokerForCustomer ) {
                Collection<TradingVenueRelationShip> brokersTradingVenueRelations = brokerForCustomer.getTradingVenueRelations();
                for ( TradingVenueRelationShip tradingVenueRelationship : brokersTradingVenueRelations ) {
                    Organization tvOrg = tradingVenueRelationship.getTvOrg();
                    if ( tvOrg == null ) {
                        log.warn( "BrokerOrganizationModificationObserver: tvOrg not for brokerForCustomer=" + brokerForCustomer.getShortName() );
                        continue;
                    }
                    TradingVenueEntity tradingVenue = tvOrg.getTradingVenueOrgFunction().getTradingVenue();
                    if ( !tvOrg.isActive() || !tradingVenue.isActive() ) {
                        log.info( "BrokerOrganizationModificationObserver: not loading inactive tv=" + tvOrg.getShortName() );
                        continue;
                    }
                    Collection<CurrencyPair> currencyPairs = tradingVenue.getSupportedCurrencyPairs().getCurrencyPairs();
                    for ( CurrencyPair currencyPair : currencyPairs ) {
                        TradingVenueFactory.getInstance().loadTradingVenue( tvOrg, currencyPair, tradingVenue.getVenueType() );
                    }
                }
            }
            RexProvisionCache.updateForBrokerForCustomerChange( org );
			V4EmsRexPovisionCache.updateForBrokerForCustomerChange( org );
        }

		//Part of main list - ignore this notification
		if ( mainList.contains(org.getShortName()) )
		{
			return message;
		}

		//Broker set on organization.
		if ( !extendedList.contains(org) )
		{
			if ( brokerOrg != null && mainList.contains(brokerOrg.getShortName()) )
			{
				//Refresh extended list for new addition of customer.
				OrderConfiguration.getInstance().resetOrderProviderOrgsList(true);
			}
			return message;
		}
		else
		{
			if ( brokerOrg == null )
			{
				//Org's broker is set to 'none' or null. Org is not in mainlist but in extended list because of broker
				//previously set on it. Now this is no longer associated with broker so remove it from list.
				OrderConfiguration.getInstance().resetOrderProviderOrgsList(true);
			}
		}

		return message;
	}

}
