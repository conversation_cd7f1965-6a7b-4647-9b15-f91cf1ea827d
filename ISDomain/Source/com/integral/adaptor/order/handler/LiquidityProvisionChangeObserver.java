package com.integral.adaptor.order.handler;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.liquidityProvision.LiquidityProvisionChangeHandler;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.oms.*;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.oms.spaces.fx.esp.descriptor.OrderRequestEntityDescriptor;
import com.integral.oms.spaces.fx.esp.service.FXESPOrderRequestService;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

public class LiquidityProvisionChangeObserver implements LiquidityProvisionChangeHandler
{

   public LiquidityProvisionChangeObserver()
   {
   }

   @Override
   public void handleChange(String orgName, Map props)
   {
      if( ISFactory.getInstance().getISMBean().isLiquidityProvisioningEnabled(orgName) )
      {
         List<OrderBook> orderBooks = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(orgName);
         for (OrderBook i : orderBooks)
         {
            updateOrders(i.getSortedBidOrders());
            updateOrders(i.getSortedOfferOrders());
         }

         // possible SD org, so get update all orders of its FIs.
         updateSDcustomerOrders(orgName);

         //TODO: Implement this: If org is LG, then send notification to ME
      }
   }

   private static void updateSDcustomerOrders(String orgName)
   {
      Organization org = ReferenceDataCacheC.getInstance().getOrganization(orgName);
      Collection<Organization> orgs = org.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
      for (Organization i : orgs)
      {
         String sdOrgName = i.getShortName();
         if (!sdOrgName.equalsIgnoreCase(orgName))
         {
            List<OrderBook> orderBooks = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(sdOrgName);
            for (OrderBook j : orderBooks)
            {
               updateSDOrders(org, j.getSortedBidOrders());
               updateSDOrders(org, j.getSortedOfferOrders());
            }
         }
      }
   }

   private static void updateOrders(Collection<Order> orders)
   {
      for (Order i : orders)
      {
         Object req = i.getEntityDescriptor().getEntity();
         if (req instanceof SingleLegOrder)
         {
            SingleLegOrder orderRequest = (SingleLegOrder)req;
            Organization org = orderRequest.getOrganization();
            CurrencyPair ccyPair = orderRequest.getCurrencyPair();
            LiquidityProvision liquidityProvision = ISUtilImpl.getLiquidityProvision(org, ccyPair);
            OrderRequestEntityDescriptor entDesc = (OrderRequestEntityDescriptor)i.getEntityDescriptor();
            entDesc.updateLiquidityProvisionExceptMinFillSize(liquidityProvision);

            if(liquidityProvision.getHierarchicalMinFillSize() != null && (entDesc.getMinimumFillAmount() != liquidityProvision.getHierarchicalMinFillSize()))
            {
               //lock order before updating min fill amount
               int state = i.checkInitialAndLockOrder();
               if( state == Order.ORDER_STATE_INITIAL )
               {
                  FXESPOrderRequestService ors = (FXESPOrderRequestService) ServiceFactory.getOrderRequestService();
                  ors.updateMinFillFromLiquidyProvision( orderRequest, liquidityProvision );
                  entDesc.updateMinFillAmount( orderRequest );
               }
               //Unlock order based on order type
               OMSUtilC.unlockOrder(i);
            }
            //Trigger Match
            OMSUtilC.triggerMatch(i);
         }
      }
   }

   private static void updateSDOrders(Organization sdOrg, Collection<Order> orders)
   {
      for (Order i : orders)
      {
         Object req = i.getEntityDescriptor().getEntity();
         if (req instanceof SingleLegOrder)
         {
            OrderRequestEntityDescriptor entDesc = (OrderRequestEntityDescriptor)i.getEntityDescriptor();
            if (entDesc.getPlacedByOrg().getShortName().equalsIgnoreCase(sdOrg.getShortName()))
            {
               SingleLegOrder orderRequest = (SingleLegOrder)req;
               CurrencyPair ccyPair = orderRequest.getCurrencyPair();
               LiquidityProvision sdLiqProv = ISUtilImpl.getLiquidityProvision(null, sdOrg, ccyPair);

               if (sdLiqProv != null)
               {
                  orderRequest.setSDLiquidityProvision(sdLiqProv);
                  entDesc.updateSDLiquidityProvision(sdLiqProv);
               }

               //Trigger Match
               OMSUtilC.triggerMatch(i);
            }
         }
      }
   }
}
