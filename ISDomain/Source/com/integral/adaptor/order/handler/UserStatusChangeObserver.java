/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.handler;

import static com.integral.is.functor.UserRemoteTransactionFunctor.FUNCTOR_EVENT_USER_INACTIVE;
import static com.integral.is.functor.UserRemoteTransactionFunctor.KEY_EVENT;
import static com.integral.is.functor.UserRemoteTransactionFunctor.KEY_ORG;
import static com.integral.is.functor.UserRemoteTransactionFunctor.KEY_USER;

import java.util.Collection;
import java.util.Map;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.exception.IdcException;
import com.integral.finance.dealing.CancellationCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.OrderRequestServiceC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * <AUTHOR>
 *
 */
public class UserStatusChangeObserver implements MessageHandler
{

	private Log log = LogFactory.getLog(this.getClass());

	/* (non-Javadoc)
	 * @see com.integral.message.MessageHandler#handle(com.integral.message.Message)
	 */
	public Message handle( Message message ) throws IdcException
	{
		Map props = message.getMap();
		String event = (String) props.get(KEY_EVENT);
		if ( event.equals(FUNCTOR_EVENT_USER_INACTIVE) )
		{
			String organizationName = (String) props.get(KEY_ORG);
			String userShortName = (String) props.get(KEY_USER);
			Organization org = ISUtilImpl.getInstance().getOrg(organizationName);
			if ( org != null )
			{
				Collection<Organization> orderProviderList = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
				if ( orderProviderList.contains(org) )
				{
					if ( org.isCancelOrdersOnUserInactivation() )
					{
						log.info("UserStatusChangeObserver.handle: Invoking OrderRequestServiceC for org=" + organizationName + ",LE=" + userShortName);
						User user = (User) ReferenceDataCacheC.getInstance().getEntityByShortName(userShortName, User.class, org.getNamespace(), Entity.PASSIVE_STATUS);
						OrderRequestServiceC ors = (OrderRequestServiceC) ServiceFactory.getOrderRequestService();
						final WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
						wfm.setEvent(ISConstantsC.MSG_EVT_WITHDRAWALL);
						wfm.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
						wfm.setObject(user);
						wfm.setParameterValue(OrderConstants.CXL_CODE, CancellationCodes.USER_INACTIVE);
						WorkflowMessage responseMessage = ors.process(wfm);
						Message out = responseMessage.getReplyMessage();
						if ( out == null )
						{
							out = responseMessage;
						}
					}
					else
					{
						log.info("UserStatusChangeObserver.handle : Org skipped. CancelOrdersOnInactivation is false for org. Org=" + organizationName + ",user=" + userShortName);
					}
				}
				else
				{
					log.info("UserStatusChangeObserver.handle : Org skipped. Not in orders provider list (extended). Org=" + organizationName + ",user=" + userShortName);
				}
			}
			else
			{
				log.info("UserStatusChangeObserver.handle : Org skipped. OrgName=" + organizationName + ",Org=" + org + ",user=" + userShortName);
			}
		}
		return null;
	}

}
