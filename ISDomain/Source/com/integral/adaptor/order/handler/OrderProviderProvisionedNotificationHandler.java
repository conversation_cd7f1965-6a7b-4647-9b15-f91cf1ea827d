package com.integral.adaptor.order.handler;

import java.util.HashMap;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.transaction.remote.GenericRemoteNotificationFunctor;
import com.integral.transaction.remote.RemoteNotificationMessageHandlerC;
import com.integral.user.Organization;

public class OrderProviderProvisionedNotificationHandler extends RemoteNotificationMessageHandlerC
{
	private Log log = LogFactory.getLog(this.getClass());

	public void handle( HashMap properties )
	{
		log.warn("OrderProviderUpdatedNotificationHandler.handle: ProvisionProvider - Properties - " + properties);
		String vsName = (String) properties.get(GenericRemoteNotificationFunctor.VIRTUAL_SERVER_NAME);
		if ( vsName != null && ConfigurationFactory.getServerMBean().getVirtualServerName().equals(vsName) )
		{
			if ( OrderServiceMBeanC.getInstance().isOrderProvidersListEnabled() )
			{
				OrderConfiguration.getInstance().resetOrderProviderOrgsList(true);
			}
			else
			{
				Organization org = ISUtilImpl.getInstance().getOrg((String) properties.get(GenericRemoteNotificationFunctor.PROVIDER_NAME));
				OrderConfiguration.getInstance().UpdateAdaptorCommonCache(org);
			}
		}
	}

}
