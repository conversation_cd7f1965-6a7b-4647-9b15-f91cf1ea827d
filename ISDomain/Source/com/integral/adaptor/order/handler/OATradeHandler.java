package com.integral.adaptor.order.handler;

import com.integral.exception.IdcException;

import com.integral.is.common.client.handler.ISRateHandler;
import com.integral.is.oms.OrderAdaptorFactory;
import com.integral.message.WorkflowMessage;
import com.integral.xems.XEmsFactory;

public class OATradeHandler extends ISRateHandler
{

	public com.integral.message.Message handle( com.integral.message.Message message ) throws IdcException
	{
		try
		{
			WorkflowMessage wfMsg = (WorkflowMessage) message;
			wfMsg.setParameterValue("LpCrossing", Boolean.TRUE);
			wfMsg.setParameterValue("ResponseReceivedTime", System.currentTimeMillis());
			return XEmsFactory.getApi().processOrderExecutionResponse(wfMsg);
		}
		catch ( Exception e )
		{
			log.warn("OATradeHandler: Exception while handling the message", e);
		}
		return message;
	}

}