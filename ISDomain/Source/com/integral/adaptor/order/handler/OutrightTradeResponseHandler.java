package com.integral.adaptor.order.handler;

import java.util.Date;

import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.RequestAttributes;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.ecn.ECNWorkflowFunctorC;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.Timing;
import com.integral.is.message.rfs.RFSFXRate;
import com.integral.is.message.rfs.RFSFXRateC;
import com.integral.is.message.rfs.RFSTradeReject;
import com.integral.is.message.rfs.RFSTradeResponse;
import com.integral.is.message.rfs.RFSTradeVerify;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.log.OMSLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.model.oms.OrderLiftRequest;

public class OutrightTradeResponseHandler  implements MessageHandler
{

	private static Log log = LogFactory.getLog(OutrightTradeResponseHandler.class);
	private String referenceId;
	private long recievedTime;
	OrderConfigurationMBean configMBean = OrderConfigurationFactory.getOrderConfigurationMBean();
	private RFSTradeVerify tradeResponse ;

	public OutrightTradeResponseHandler( long receivedTime )
	{
		this.recievedTime = receivedTime;
	}

	public Message handle( Message message ) throws IdcException
	{
		try
		{
			if ( ISCommonUtilC.isWarmUpObject(message) )
			{
				return null;
			}
			if ( message instanceof WorkflowMessage )
			{
				WorkflowMessage wf = (WorkflowMessage) message;
				MessageEvent event = wf.getEvent();
				Object refObj = wf.getObject();
				//if the WorkflowMessgae object is trade
				if ( ISConstantsC.MSG_TOPIC_ORDER.equals(wf.getTopic()) )
				{
					
					if ( ISConstantsC.EVT_COMPLETED.equals(event) )
					{
						
						double amount = 0.0;
						if (refObj instanceof Request) {
							Request request = (Request) refObj;
							RequestAttributes requestAttr = request.getRequestAttributes();
							amount = requestAttr.getOrderFilledAmt();
							logOrderMatchingEnds(request, amount, 0.0 ,tradeResponse.getProviderTradeId());
						} else if (refObj instanceof OrderLiftRequest) {
							OrderLiftRequest orderLiftRequest = (OrderLiftRequest)refObj;
							amount = orderLiftRequest.getFilledAmount();
							logOrderMatchingEnds(orderLiftRequest, amount, 0.0 ,tradeResponse.getProviderTradeId());
						}
						AdaptorConfigurationFactory.getResponseHandlerInstance().rfsTradeRecieved(tradeResponse, false);
					}
					else if ( ISConstantsC.EVT_PARTIAL.equals(event) )
					{
						log.warn("OutrightTradeResponseHandler.Error: Received PARTIAL Fill for Outright order TOPIC[" + wf.getTopic() + "] EVENT[" + event + "] OBJECT[" + wf.getObject() + "]");
					}
					else if ( ISConstantsC.EVT_EXPIRED.equals(event) )
					{
						RFSTradeReject rfsTradeReject = null;
						if (refObj instanceof Request) {
							Request request = (Request) refObj;
							rfsTradeReject = getRFSTradeReject(wf, request);
						} else if (refObj instanceof OrderLiftRequest) {
							OrderLiftRequest orderLiftRequest = (OrderLiftRequest)refObj;
							rfsTradeReject = getRFSTradeReject(wf, orderLiftRequest);
						}
						
						AdaptorConfigurationFactory.getResponseHandlerInstance().rfsTradeRecieved(rfsTradeReject, false);
					}
				}
				else if ( ISConstantsC.MSG_TOPIC_TRADE.equals(wf.getTopic()) )
				{
					if ( ISConstantsC.MSG_EVENT_CREATE.equals(event.getName()) )
					{
						
						if (refObj instanceof Request) {
							tradeResponse = getRFSTradeVerify(wf, (Request) refObj);
						} else if (refObj instanceof OrderLiftRequest) {
							tradeResponse = getRFSTradeVerify(wf, (OrderLiftRequest) refObj);
						}
					}
				}
				else
				{
					log.warn("OutrightTradeResponseHandler.handle called - TOPIC[" + wf.getTopic() + "] EVENT[" + event + "] OBJECT[" + wf.getObject() + "]");
				}
			}
		}
		catch ( Exception e )
		{
			log.error("OutrightTradeResponseHandler.handle - Failed to send trade message to IS", e);
		}
		return null;
	}

	private RFSTradeVerify getRFSTradeVerify( WorkflowMessage wf, Request request )
	{
		Order order = (Order) request.getRequestAttributes().getOrder();
		RFSTradeVerify rfsTradeVerify = MessageFactory.newRFSTradeVerify();
		FXLegDealingPrice qdealingPrice = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
		rfsTradeVerify.setTradeId(request.getExternalRequestId());
		rfsTradeVerify.setBaseCurrency(order.getEntityDescriptor().getBaseCurrency());
		rfsTradeVerify.setVariableCurrency(order.getEntityDescriptor().getVariableCurrency());
		if ( qdealingPrice.getFixingDate() != null )
		{
			rfsTradeVerify.setFixingDate(qdealingPrice.getFixingDate().asJdkDate());
		}
		if ( qdealingPrice.getFixingTenor() != null )
		{
			rfsTradeVerify.setFixingTenor(qdealingPrice.getFixingTenor().toString());
		}
		if ( qdealingPrice.getValueDate() != null )
		{
			rfsTradeVerify.setAcceptedNearLegValueDate(qdealingPrice.getValueDate().asJdkDate());
		}
		RFSFXRate acceptedNearLegRate = new RFSFXRateC();
		acceptedNearLegRate.setRate(qdealingPrice.getRate());
		acceptedNearLegRate.setSpotRate(qdealingPrice.getSpotRate());
		acceptedNearLegRate.setForwardPoints(qdealingPrice.getForwardPoints());
		acceptedNearLegRate.setDealtAmount(qdealingPrice.getAcceptedDealtCurrencyAmount());
		rfsTradeVerify.setAcceptedNearLegRate(acceptedNearLegRate);
		rfsTradeVerify.setLegalEntity(request.getCounterparty().getShortName());
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_USER_OBJ_ID, (Long) wf.getParameterValue(ISConstantsC.MAKER_USER_OBJ_ID));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_ORDER_ID, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_ID));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_ORDER_CHANNEL, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_CHANNEL));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_ORDER_DEALT_CCY, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_DEALT_CCY));
		rfsTradeVerify.setProperty(ISConstantsC.TRADE_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.TRADE_TRANSACTIONID));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_REQUEST_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.MAKER_REQUEST_TRANSACTIONID));
		rfsTradeVerify.setProperty(ECNWorkflowFunctorC.LEGAL_ENTITY, (String) wf.getParameterValue(ECNWorkflowFunctorC.LEGAL_ENTITY));
		rfsTradeVerify.setProperty(ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT, (String) wf.getParameterValue(ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_TRADE_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.MAKER_TRADE_TRANSACTIONID));
		rfsTradeVerify.setProperty(ISCommonConstants.TRADE_EXECUTION_FLAGS, (Integer) wf.getParameterValue(ISCommonConstants.TRADE_EXECUTION_FLAGS));
		rfsTradeVerify.setProperty(ISCommonConstants.MAKER_SD_CPTYID, (Long) wf.getParameterValue(ISCommonConstants.MAKER_SD_CPTYID));
		rfsTradeVerify.setProperty(ISCommonConstants.MAKER_SD_USERID, (Long) wf.getParameterValue(ISCommonConstants.MAKER_SD_USERID));
		rfsTradeVerify.setProperty(ISCommonConstants.ORDER_UNFILLED_AMT, (Double) wf.getParameterValue(ISCommonConstants.ORDER_UNFILLED_AMT));
		rfsTradeVerify.setProviderTradeId((String) wf.getParameterValue(ISConstantsC.TRADE_TRANSACTIONID));
		rfsTradeVerify.setProvider(request.getToOrganization().getShortName().toString());		
		setTimingInfo(rfsTradeVerify,  (Long) wf.getParameterValue(OrderConstants.TRADE_VERIFIED_TIME));
		return rfsTradeVerify;
	}
	
	private RFSTradeReject getRFSTradeReject( WorkflowMessage wf, Request request )
	{
		Order order = (Order) request.getRequestAttributes().getOrder();
		RFSTradeReject rfsTradeReject = MessageFactory.newRFSTradeReject();
		rfsTradeReject.setTradeId(request.getExternalRequestId());
		rfsTradeReject.setBaseCurrency(order.getEntityDescriptor().getBaseCurrency());
		rfsTradeReject.setVariableCurrency(order.getEntityDescriptor().getVariableCurrency());
		rfsTradeReject.setLegalEntity(request.getCounterparty().getShortName());
		rfsTradeReject.setProvider(request.getToOrganization().getShortName().toString());
		rfsTradeReject.setFailureReason(configMBean.getTradeRejectionReason());
		setTimingInfo(rfsTradeReject,  System.currentTimeMillis());
	
		return rfsTradeReject;
	}
	
	private RFSTradeVerify getRFSTradeVerify( WorkflowMessage wf, OrderLiftRequest request )
	{
		//Order order = (Order) request.geteOrder();
		RFSTradeVerify rfsTradeVerify = MessageFactory.newRFSTradeVerify();
		//FXLegDealingPrice qdealingPrice = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
		rfsTradeVerify.setTradeId(request.getClientReferenceId());
		CurrencyPair currencyPair = request.getCurrencyPair();
		rfsTradeVerify.setBaseCurrency(currencyPair.getBaseCurrency().getShortName());
		rfsTradeVerify.setVariableCurrency(currencyPair.getVariableCurrency().getShortName());
		long fixingDate = request.getFixingDate();
		if ( fixingDate > 0L )
		{
			rfsTradeVerify.setFixingDate(new Date(fixingDate));
		}
		String tenor = request.getTenor();
		if ( tenor != null )
		{
			rfsTradeVerify.setFixingTenor(tenor);
		}
		long valueDate = request.getValueDate();
		if ( valueDate > 0 )
		{
			rfsTradeVerify.setAcceptedNearLegValueDate(new Date(valueDate));
		}
		RFSFXRate acceptedNearLegRate = new RFSFXRateC();
		acceptedNearLegRate.setRate(request.getRate());
		acceptedNearLegRate.setSpotRate(request.getSpotRate());
		acceptedNearLegRate.setForwardPoints(request.getForwardPoint());
		acceptedNearLegRate.setDealtAmount(request.getDealtAmount());
		rfsTradeVerify.setAcceptedNearLegRate(acceptedNearLegRate);
		rfsTradeVerify.setLegalEntity(request.getCptyA());
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_USER_OBJ_ID, (Long) wf.getParameterValue(ISConstantsC.MAKER_USER_OBJ_ID));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_ORDER_ID, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_ID));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_ORDER_CHANNEL, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_CHANNEL));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_ORDER_DEALT_CCY, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_DEALT_CCY));
		rfsTradeVerify.setProperty(ISConstantsC.TRADE_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.TRADE_TRANSACTIONID));
		rfsTradeVerify.setProperty(ISConstantsC.MAKER_REQUEST_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.MAKER_REQUEST_TRANSACTIONID));
		rfsTradeVerify.setProperty(ECNWorkflowFunctorC.LEGAL_ENTITY, (String) wf.getParameterValue(ECNWorkflowFunctorC.LEGAL_ENTITY));
		rfsTradeVerify.setProperty(ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT, (String) wf.getParameterValue(ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT));
		rfsTradeVerify.setProperty(ISCommonConstants.TRADE_EXECUTION_FLAGS, (Integer) wf.getParameterValue(ISCommonConstants.TRADE_EXECUTION_FLAGS));
		rfsTradeVerify.setProperty(ISCommonConstants.MAKER_SD_CPTYID, (Long) wf.getParameterValue(ISCommonConstants.MAKER_SD_CPTYID));
		rfsTradeVerify.setProperty(ISCommonConstants.MAKER_SD_USERID, (Long) wf.getParameterValue(ISCommonConstants.MAKER_SD_USERID));
		rfsTradeVerify.setProperty(ISCommonConstants.ORDER_UNFILLED_AMT, (Double) wf.getParameterValue(ISCommonConstants.ORDER_UNFILLED_AMT));
		rfsTradeVerify.setProviderTradeId((String) wf.getParameterValue(ISConstantsC.TRADE_TRANSACTIONID));
		rfsTradeVerify.setProvider(request.getToOrganization().getShortName().toString());		
		setTimingInfo(rfsTradeVerify,  (Long) wf.getParameterValue(OrderConstants.TRADE_VERIFIED_TIME));
		return rfsTradeVerify;
	}
	
	private RFSTradeReject getRFSTradeReject( WorkflowMessage wf, OrderLiftRequest request )
	{
		//Order order = (Order) request.getRequestAttributes().getOrder();
		RFSTradeReject rfsTradeReject = MessageFactory.newRFSTradeReject();
		rfsTradeReject.setTradeId(request.getClientReferenceId());
		rfsTradeReject.setBaseCurrency(request.getCurrencyPair().getBaseCurrency().getShortName());
		rfsTradeReject.setVariableCurrency(request.getCurrencyPair().getVariableCurrency().getShortName());
		rfsTradeReject.setLegalEntity(request.getCptyA());
		rfsTradeReject.setProvider(request.getToOrganization().getShortName().toString());
		rfsTradeReject.setFailureReason(configMBean.getTradeRejectionReason());
		setTimingInfo(rfsTradeReject,  System.currentTimeMillis());
	
		return rfsTradeReject;
	}
	/**
	 * Get referenceId
	 *
	 * @return reference id
	 */
	public String getReferenceId()
	{
		return referenceId;
	}

	/**
	 * Set referenceId
	 *
	 * @param referenceId reference id
	 */
	public void setReferenceId( String referenceId )
	{
		this.referenceId = referenceId;
	}

	private void logOrderMatchingEnds( Request request, double filledAmt, double unFilledAmt ,String providerTradeId)
	{
		StringBuilder sb = new StringBuilder(150);
		sb.append(OMSLogger.ORDER_MATCHING_ENDS).append(getReferenceId()).append(OMSLogger.whitespace).append(request.getExternalRequestId()).append(OMSLogger.whitespace).append(providerTradeId).append(OMSLogger.whitespace).append(filledAmt).append(OMSLogger.whitespace).append(unFilledAmt);
		OMSLogger.getInstance().logInfo(sb.toString());
	}
	
	private void logOrderMatchingEnds( OrderLiftRequest request, double filledAmt, double unFilledAmt ,String providerTradeId)
	{
		StringBuilder sb = new StringBuilder(150);
		sb.append(OMSLogger.ORDER_MATCHING_ENDS).append(getReferenceId()).append(OMSLogger.whitespace).append(request.getClientReferenceId()).append(OMSLogger.whitespace).append(providerTradeId).append(OMSLogger.whitespace).append(filledAmt).append(OMSLogger.whitespace).append(unFilledAmt);
		OMSLogger.getInstance().logInfo(sb.toString());
	}
	

	
	private void setTimingInfo( RFSTradeResponse response, long executionReceivedTime )
	{
		Timing timing = response.getTiming();
		if ( response instanceof RFSTradeReject )
		{
			timing.setTime(ISConstantsC.EVENT_TIME_DISP_REC_REJ_FROM_PROVIDER, executionReceivedTime);
		}
		else
		{
			timing.setTime(ISConstantsC.EVENT_TIME_DISP_REC_VERIFY_FROM_PROVIDER, executionReceivedTime);
		}
		response.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_ADAPTER_REC_ACC, recievedTime);
		response.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_ACC_SENT_TO_PROVIDER, recievedTime);
	}

	

}
