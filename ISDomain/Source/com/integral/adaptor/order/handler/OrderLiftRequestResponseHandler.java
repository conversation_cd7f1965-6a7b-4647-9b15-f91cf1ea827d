package com.integral.adaptor.order.handler;

import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.adaptor.response.ResponseHandlerC;
import com.integral.exception.IdcException;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.ecn.ECNWorkflowFunctorC;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.TradeReject;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeResponses;
import com.integral.is.message.TradeStatusResponse;
import com.integral.is.message.TradeVerify;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.log.OMSLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.model.oms.OrderLiftRequest;

import java.util.ArrayList;
import java.util.Collection;

public class OrderLiftRequestResponseHandler implements MessageHandler
{

	private static Log log = LogFactory.getLog(OrderLiftRequestResponseHandler.class);
	private String providerTradeIds = "";
	private final Object monitor = new Object();
	private double totalPrice = 0.0;
	private long recievedTime;
	private Collection<TradeResponse> tradeResponses = new ArrayList<TradeResponse>();
	OrderConfigurationMBean configMBean = OrderConfigurationFactory.getOrderConfigurationMBean();

	public OrderLiftRequestResponseHandler( long receivedTime )
	{
		this.recievedTime = receivedTime;
	}

	public Message handle( Message message ) throws IdcException
	{
		try
		{
            ResponseHandlerC responseHandlerInstance = AdaptorConfigurationFactory.getResponseHandlerInstance();
			if ( message instanceof WorkflowMessage )
			{
				WorkflowMessage wf = (WorkflowMessage) message;
				MessageEvent event = wf.getEvent();

				//if the WorkflowMessgae object is trade
				if ( ISConstantsC.MSG_TOPIC_ORDER.equals(wf.getTopic()) )
				{
					TradeResponses responses = MessageFactory.newTradeResponses();
					OrderLiftRequest request = (OrderLiftRequest) wf.getObject();
					if ( ISConstantsC.EVT_COMPLETED.equals(event) )
					{
						double orderAmt = request.getDealtAmount();
						double amount = request.getFilledAmount();
						TradeStatusResponse tradeStatusResponse = MessageFactory.newTradeStatusResponse();
						tradeStatusResponse.setTradeId(request.getClientReferenceId());
						tradeStatusResponse.setVerifiedAmount(amount);
						tradeStatusResponse.setCancelledAmount(0);
						tradeStatusResponse.setTradeAmount(orderAmt);
						logOrderMatchingEnds(request, amount, 0.0);
						tradeResponses.add(tradeStatusResponse);
						responses.setTradeResponses(tradeResponses);
					}
					else if ( ISConstantsC.EVT_PARTIAL.equals(event) )
					{
						double orderAmt = request.getDealtAmount();
						double amount = request.getFilledAmount();
						double unfilledAmt = request.getUnfilledAmount();
						TradeStatusResponse tradeStatusResponse = MessageFactory.newTradeStatusResponse();
						tradeStatusResponse.setTradeId(request.getClientReferenceId());
						tradeStatusResponse.setVerifiedAmount(amount);
						tradeStatusResponse.setCancelledAmount(unfilledAmt);
						tradeStatusResponse.setTradeAmount(orderAmt);
						logOrderMatchingEnds(request, amount, unfilledAmt);
						tradeResponses.add(tradeStatusResponse);
						responses.setTradeResponses(tradeResponses);
					}
					else if ( ISConstantsC.EVT_EXPIRED.equals(event) )
					{
						double orderAmt = request.getDealtAmount();
						double unfilledAmt = request.getUnfilledAmount();
						TradeReject trdReject = MessageFactory.newTradeReject();
                        trdReject.setTradeId(request.getClientReferenceId());
						trdReject.setReasonDescription(configMBean.getTradeRejectionReason());
						trdReject.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_REC_REJ_FROM_PROVIDER, System.currentTimeMillis());
						setTimings(trdReject);
						tradeResponses.add(trdReject);
						TradeStatusResponse tradeStatusResponse = MessageFactory.newTradeStatusResponse();
						tradeStatusResponse.setTradeId(request.getClientReferenceId());
						tradeStatusResponse.setVerifiedAmount(0);
						tradeStatusResponse.setCancelledAmount(unfilledAmt);
						tradeStatusResponse.setTradeAmount(orderAmt);
						logOrderMatchingEnds(request, 0, unfilledAmt);
						tradeResponses.add(tradeStatusResponse);
						responses.setTradeResponses(tradeResponses);
					}
                    if (ISCommonUtilC.isWarmUpObject(message)) {
                        ISCommonUtilC.setAsWarmUpObject(responses);
                    }
                    if( request.getToOrganization() != null ) {
                        responses.setProperty(ISCommonConstants.ProviderName, request.getToOrganization().getShortName());
                    }
                    responseHandlerInstance.tradeRecieved(responses);
				}
				else if ( ISConstantsC.MSG_TOPIC_TRADE.equals(wf.getTopic()) )
				{
					OrderLiftRequest request = (OrderLiftRequest) wf.getObject();
					if ( ISConstantsC.MSG_EVENT_CREATE.equals(event.getName()) )
					{
						synchronized ( monitor )
						{
							if ( wf.getParameterValue(ISConstantsC.CPTYB_TRD_ID) != null )
							{
								providerTradeIds = providerTradeIds + wf.getParameterValue(ISConstantsC.CPTYB_TRD_ID) + ",";
							}

							if ( wf.getParameterValue(ISConstantsC.VERIFIED_PRICE) != null )
							{
								totalPrice = totalPrice + ((Double) wf.getParameterValue(ISConstantsC.VERIFIED_PRICE)).doubleValue();
							}
							TradeVerify trdVerify = MessageFactory.newTradeVerify();
							trdVerify.setTradeId(request.getClientReferenceId());
							trdVerify.setProperty(ISConstantsC.MAKER_USER_OBJ_ID, (Long) wf.getParameterValue(ISConstantsC.MAKER_USER_OBJ_ID));
							trdVerify.setProperty(ISConstantsC.MAKER_ORDER_ID, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_ID));
							trdVerify.setProperty(ISConstantsC.MAKER_ORDER_CHANNEL, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_CHANNEL));
                            String makerBookName = (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_BOOKNAME);
                            //For Yield Manager.
                            if( makerBookName != null ){
                                trdVerify.setProperty(ISConstantsC.MAKER_ORDER_BOOKNAME,makerBookName);
                            }
							trdVerify.setProperty(ISConstantsC.MAKER_ORDER_DEALT_CCY, (String) wf.getParameterValue(ISConstantsC.MAKER_ORDER_DEALT_CCY));
							trdVerify.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_REC_VERIFY_FROM_PROVIDER, (Long) wf.getParameterValue(OrderConstants.TRADE_VERIFIED_TIME));
							setTimings(trdVerify);
							double amount = (Double) wf.getParameterValue(ISConstantsC.VERIFIED_AMOUNT);
							trdVerify.setAcceptedAmount(amount);
							trdVerify.setProviderTradeId((String) wf.getParameterValue(ISConstantsC.CPTYB_TRD_ID));
							trdVerify.setProperty(ISConstantsC.TRADE_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.TRADE_TRANSACTIONID));
							trdVerify.setProperty(ISConstantsC.MAKER_REQUEST_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.MAKER_REQUEST_TRANSACTIONID));
							trdVerify.setProperty(ISConstantsC.MAKER_TRADE_TRANSACTIONID, (String) wf.getParameterValue(ISConstantsC.MAKER_TRADE_TRANSACTIONID));
							trdVerify.setProperty(ECNWorkflowFunctorC.LEGAL_ENTITY, (String) wf.getParameterValue(ECNWorkflowFunctorC.LEGAL_ENTITY));
							trdVerify.setProperty(ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT, (String) wf.getParameterValue(ISCommonConstants.MAKER_ORDER_MARKET_SNAPSHOT));
							trdVerify.setProperty(ISCommonConstants.TRADE_EXECUTION_FLAGS, (Integer) wf.getParameterValue(ISCommonConstants.TRADE_EXECUTION_FLAGS));
							trdVerify.setProperty(ISCommonConstants.MAKER_SD_CPTYID, (Long) wf.getParameterValue(ISCommonConstants.MAKER_SD_CPTYID));
							trdVerify.setProperty(ISCommonConstants.MAKER_SD_USERID, (Long) wf.getParameterValue(ISCommonConstants.MAKER_SD_USERID));
							logOrderMatchingEnds(request, amount, 0.0);
							tradeResponses.add(trdVerify);
						}
					}
				}
				else
				{
					log.warn("TradeResponseHandler.handle called - TOPIC[" + wf.getTopic() + "] EVENT[" + event + "] OBJECT[" + wf.getObject() + "]");
				}
			}
		}
		catch ( Exception e )
		{
			log.error("TradeResponseHandler.handle - Failed to send trade message to IS", e);
		}
		return null;
	}

	private void logOrderMatchingEnds( OrderLiftRequest request, double filledAmt, double unFilledAmt )
	{
		StringBuilder sb = new StringBuilder(150);
		sb.append(OMSLogger.ORDER_MATCHING_ENDS).append(request.getClientReferenceId())
                .append(OMSLogger.whitespace).append(request.getClientReferenceId())
                .append(OMSLogger.whitespace).append(providerTradeIds)
                .append(OMSLogger.whitespace).append(filledAmt)
                .append(OMSLogger.whitespace).append(unFilledAmt);
		OMSLogger.getInstance().logInfo(sb.toString());
	}

	private void setTimings( TradeResponse tradeResponse )
	{
		tradeResponse.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_ADAPTER_REC_ACC, recievedTime);
		tradeResponse.getTiming().setTime(ISConstantsC.EVENT_TIME_DISP_ACC_SENT_TO_PROVIDER, recievedTime);

	}

}
