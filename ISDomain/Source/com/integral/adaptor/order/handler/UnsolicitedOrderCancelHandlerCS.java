package com.integral.adaptor.order.handler;

import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.oms.spaces.fx.esp.FXOrder;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.staging.oms.passorder.OrderCancelledBy;

public class UnsolicitedOrderCancelHandlerCS extends UnsolicitedOrderCancelHandlerC
{
	private Log log = LogFactory.getLog(this.getClass());

	public boolean cancelOrder( String orderId , OrderCancelledBy cancelledBy)
	{
		try
		{
			FXOrder fxo = FXOrderCache.get(orderId);
            if( fxo == null )
            {
                log.warn("UnsolicitedOrderCancelHandlerCS.cancelOrder : Order not found " + orderId);
                return false;

            }

			SingleLegOrder slo = (SingleLegOrder) fxo.getObject();
			if ( slo == null )
			{
				log.warn("UnsolicitedOrderCancelHandlerCS.cancelOrder : Could not cancel the order for orderId " + orderId + " since orderRequest is null");
				return false;
			}
			IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext();
			if ( ctx == null )
			{
				ctx = IdcSessionManager.getInstance().getSessionContext(slo.getUser());
				IdcSessionManager.getInstance().setSessionContext(ctx);
			}
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(slo, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
			wfMsg.setObject(slo);
			// note: originally used this, but per Jatin advice and GM hitting 'host server' of order can use COWC
			//            wfMsg.setParameterValue( ISCommonConstants.UNSOLICITED_CANCEL_IND, Boolean.TRUE );
			//            FXISRequestService isRequestService = ( FXISRequestService ) ServiceFactory.getISRequestService();
			//            isRequestService.process(wfMsg);
			// note:            wfMsg.setSender( wfMsg.getSender() ); not required as COWC sets 'default' used for unsolicited cancel
			slo.setUnsolicitedCancel(true);
			if(cancelledBy.getValue().equals(OrderCancelledBy.DESK.getValue())){
				wfMsg.setParameterValue("CANCELLED_BY_DESK", true);
			}
			log.info("UnsolicitedOrderCancelHandlerCS.cancelOrder : txnid=" + slo.getTransactionId() + ",orderid=" + slo.get_id() + ",clsf=" + slo.getType());
			//OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfMsg);
            WorkflowMessage response = OrderCalculatorFactory.getInstance().getOrderRouter().cancel(wfMsg);
            if ( response == null || response.getStatus() == MessageStatus.FAILURE || response.getErrors() != null ) {
                StringBuilder sb = new StringBuilder(256).append("UnsolicitedOrderCancelHandlerCS.cancelOrder - Failed to cancel order. Order Id ").append(slo.get_id()).append( "tId:" )
                        .append( slo.getTransactionId() ).append(". Response - ").append(response);
                log.warn(sb.toString());
            }
			WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();
			return replyMessage == null || "SUCCESS".equals(replyMessage.getStatusName());
		}
		catch ( Exception ex )
		{
			log.error("UnsolicitedOrderCancelHandlerCS.cancelOrder : Error in cancelling order " + ex);
			return false;
		}
	}

	public boolean cancelOrder( String orderId )
	{
		try
		{
			FXOrder fxo = FXOrderCache.get(orderId);
			if( fxo == null )
			{
				log.warn("UnsolicitedOrderCancelHandlerCS.cancelOrder : Order not found " + orderId);
				return false;

			}

			SingleLegOrder slo = (SingleLegOrder) fxo.getObject();
			if ( slo == null )
			{
				log.warn("UnsolicitedOrderCancelHandlerCS.cancelOrder : Could not cancel the order for orderId " + orderId + " since orderRequest is null");
				return false;
			}
			IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext();
			if ( ctx == null )
			{
				ctx = IdcSessionManager.getInstance().getSessionContext(slo.getUser());
				IdcSessionManager.getInstance().setSessionContext(ctx);
			}
			WorkflowMessage wfMsg = ISUtilImpl.getInstance().createWorkflowMessage(slo, ISConstantsC.MSG_EVT_WITHDRAW, ISConstantsC.MSG_TOPIC_REQUEST);
			wfMsg.setObject(slo);
			// note: originally used this, but per Jatin advice and GM hitting 'host server' of order can use COWC
			//            wfMsg.setParameterValue( ISCommonConstants.UNSOLICITED_CANCEL_IND, Boolean.TRUE );
			//            FXISRequestService isRequestService = ( FXISRequestService ) ServiceFactory.getISRequestService();
			//            isRequestService.process(wfMsg);
			// note:            wfMsg.setSender( wfMsg.getSender() ); not required as COWC sets 'default' used for unsolicited cancel
			slo.setUnsolicitedCancel(true);
			log.info("UnsolicitedOrderCancelHandlerCS.cancelOrder : txnid=" + slo.getTransactionId() + ",orderid=" + slo.get_id() + ",clsf=" + slo.getType());
			//OrderCalculatorFactory.getInstance().getCancelOrderWorkflowCalculator().processWithdrawRequest(wfMsg);
			WorkflowMessage response = OrderCalculatorFactory.getInstance().getOrderRouter().cancel(wfMsg);
			if ( response == null || response.getStatus() == MessageStatus.FAILURE || response.getErrors() != null ) {
				StringBuilder sb = new StringBuilder(256).append("UnsolicitedOrderCancelHandlerCS.cancelOrder - Failed to cancel order. Order Id ").append(slo.get_id()).append( "tId:" )
						.append( slo.getTransactionId() ).append(". Response - ").append(response);
				log.warn(sb.toString());
			}
			WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();
			return replyMessage == null || "SUCCESS".equals(replyMessage.getStatusName());
		}
		catch ( Exception ex )
		{
			log.error("UnsolicitedOrderCancelHandlerCS.cancelOrder : Error in cancelling order " + ex);
			return false;
		}
	}
}
