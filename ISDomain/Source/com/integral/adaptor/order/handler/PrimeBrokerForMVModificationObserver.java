package com.integral.adaptor.order.handler;

import java.util.Collection;
import java.util.Map;

import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.TradingVenueFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.is.rex.provision.RexProvisionCache;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;

public class PrimeBrokerForMVModificationObserver implements MessageHandler {
	private Log log = LogFactory
			.getLog(PrimeBrokerForMVModificationObserver.class);

	public Message handle(Message message) throws IdcException {
		Map props = message.getMap();

		String notificationType = (String) props
				.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

		if (!OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_UPDATEBPRIMEROKERFORMV
				.equals(notificationType)) {
			return message;
		}

		String organizationName = (String) props
				.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_SHORTNAME);
		Organization org = ISUtilImpl.getInstance().getOrg(organizationName);

		// PrimeBrokerCustomer's venue access changes
		Collection<Organization> provisionedOrgs = ISUtilImpl.getInstance()
				.getProvisionedOrgs();
		if (provisionedOrgs.contains(org)) {
			Organization pbOrgForMV = org.getPrimeBrokerOrganizationForMV();
			if (null != pbOrgForMV) {
				Collection<TradingVenueRelationShip> primeBrokersTradingVenueRelations = pbOrgForMV
						.getTradingVenueRelations();
				for (TradingVenueRelationShip tradingVenueRelationship : primeBrokersTradingVenueRelations) {
					Organization tvOrg = tradingVenueRelationship.getTvOrg();
					if (tvOrg == null) {
						log.warn("PrimeBrokerForMVModificationObserver: tvOrg not set for primeBrokerForMV="
								+ pbOrgForMV.getShortName());
						continue;
					}
					TradingVenueEntity tradingVenue = tvOrg
							.getTradingVenueOrgFunction().getTradingVenue();
					if (!tvOrg.isActive() || !tradingVenue.isActive()) {
						log.info("PrimeBrokerForMVModificationObserver: not loading inactive tv="
								+ tvOrg.getShortName());
						continue;
					}
					Collection<CurrencyPair> currencyPairs = tradingVenue
							.getSupportedCurrencyPairs().getCurrencyPairs();
					for (CurrencyPair currencyPair : currencyPairs) {
						TradingVenueFactory.getInstance().loadTradingVenue(
								tvOrg,
								currencyPair,
								tradingVenue.getVenueType());
					}
				}
			}
			RexProvisionCache.updateForBrokerForCustomerChange(org);
		}
		return message;
	}
}
