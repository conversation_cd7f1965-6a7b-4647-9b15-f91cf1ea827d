/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.handler;

import com.integral.finance.currency.CurrencyPair;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.lp.QuoteDistributionInfoObserver;
import com.integral.user.Organization;

/**
 * <AUTHOR>
 *
 */
public class OMSQuoteDistributionInfoObserver implements QuoteDistributionInfoObserver
{

	/* (non-Javadoc)
	 * @see com.integral.lp.QuoteDistributionInfoObserver#destroyed(com.integral.user.Organization, com.integral.user.Organization, com.integral.finance.currency.CurrencyPair)
	 */
	public void destroyed( Organization lp, Organization fi, CurrencyPair ccyp )
	{
		OrderBookCacheC.getInstance().cancelAll(lp, fi, ccyp);
	}

}
