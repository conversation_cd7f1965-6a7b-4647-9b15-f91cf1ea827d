/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.handler;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Pattern;

import com.integral.adaptor.order.OrderBroadcaster;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.exception.IdcException;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.message.MessageFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.system.notification.NotificationEvent;
import com.integral.system.notification.NotificationEventFilter;
import com.integral.system.notification.NotificationFactory;
import com.integral.system.notification.NotificationSenderC;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * <AUTHOR>
 *
 */
public class OrganizationMasterControlChangeObserver implements MessageHandler
{

	private Log log = LogFactory.getLog(this.getClass());

	/* (non-Javadoc)
	 * @see com.integral.message.MessageHandler#handle(com.integral.message.Message)
	 */
	public Message handle( Message message ) throws IdcException
	{
		Map props = message.getMap();		
		String organizationName = (String) props.get(ISCommonConstants.ORGANIZATION_KEY);
		String event = (String) props.get(ISCommonConstants.ACTION_KEY);
		Organization org = ISUtilImpl.getInstance().getOrg(organizationName);
		if(!RuntimeFactory.getServerRuntimeMBean().isTakerOrgControlQueryFromMongoEnabled(organizationName)){
			if ( event.equals(ISCommonConstants.ORDER_EXECUTION_KEY) )
			{
				if ( org != null )
				{
					sendAlert(org);
					Collection<Organization> orderProviderList = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
					if ( orderProviderList.contains(org) )
					{
						final boolean isOrderExecutionEnbled = org.isOrderExecutionEnabled();
						log.info("OMCCO.handle - Order execution updated for org=" + org + ",value=" + org.isOrderExecutionEnabled());
						OrderBroadcaster.republishAll(organizationName, isOrderExecutionEnbled);
					}
					else
					{
						log.info("OMCCO.handle : Org skipped. Not in order provider's list (extended). Org=" + organizationName + ",List=" + orderProviderList);
					}
				}
				else
				{
					log.info("OMCCO.handle : Org is null. Skipped. OrgName=" + organizationName + ",Org=" + org);
				}
			}
		}
		return null;
	}

	/**
	 * @param org
	 */
	private void sendAlert( Organization org )
	{
		String message = org_pattern.matcher(org.isOrderExecutionEnabled() ? ORG_ENABLED_NOTIFICATION_PATTERN : ORG_DISABLED_NOTIFICATION_PATTERN).replaceFirst(org.getShortName());
		ArrayList<User> users = ISUtilImpl.getInstance().getLoggedInUserObjects(ISCommonConstants.CLIENT_NAME_FXINSIDE);
		Iterator<User> us = users.listIterator();
		HashSet<Organization> orgs = new HashSet<Organization>();
		while ( us.hasNext() )
		{
			User user = us.next();
			if ( user.getOrganization().getObjectId() == org.getObjectId() )
			{
				orgs.add(user.getOrganization());
				continue;
			}
			Collection<Organization> o = user.getOrganization().getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
			if ( o.contains(org) )
			{
				orgs.add(user.getOrganization());
				continue;
			}
			us.remove();
		}
		for ( Organization organization : orgs )
		{
			WorkflowMessage msg = MessageFactory.newWorkflowMessage();
			msg.setParameterValue("MessageContent", message);
			msg.setParameterValue("MessageProps", null);

			NotificationEvent ne = NotificationFactory.newNotificationEvent();
			ne.setMessage(msg);
			NotificationEventFilter nf = NotificationFactory.newOrganizationUsersNotificationEventFilter(organization);
			NotificationSenderC.getInstance().sendMessage(nf, ne, 1);

		}
	}

	static String ORG_DISABLED_NOTIFICATION_PATTERN = "<workflowMessage><event>EXECUTION_DISABLED</event><topic>ALERT</topic><parameter key=\"org\" class=\"java.lang.String\">_ORGNAME_</parameter></workflowMessage>";
	static String ORG_ENABLED_NOTIFICATION_PATTERN = "<workflowMessage><event>EXECUTION_ENABLED</event><topic>ALERT</topic><parameter key=\"org\" class=\"java.lang.String\">_ORGNAME_</parameter></workflowMessage>";
	static Pattern org_pattern = Pattern.compile("_ORGNAME_");
}
