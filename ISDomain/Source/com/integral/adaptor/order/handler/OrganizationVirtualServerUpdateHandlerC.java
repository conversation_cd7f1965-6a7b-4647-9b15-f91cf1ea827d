package com.integral.adaptor.order.handler;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.admin.ha.PrimaryAdminIdentifier;
import com.integral.exception.IdcException;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.is.alerttest.ISAlertMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.is.log.MessageLogger;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.order.migration.OrderMigrationService;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.server.VirtualServerType;
import com.integral.user.Organization;

import java.util.List;
import java.util.Map;

public class OrganizationVirtualServerUpdateHandlerC implements MessageHandler
{
	private final Log log = LogFactory.getLog(this.getClass());

	public Message handle( Message message ) throws IdcException
	{
		Map props = message.getMap();

		String notificationType = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

		if ( !OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_VIRTUALSERVER_UPDATE.equals(notificationType) )
		{
			return message;
		}

		String orgName = (String) props.get(OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_SHORTNAME);
		Organization org = ISUtilImpl.getInstance().getOrg(orgName);
		String userName = (String) props.get( "userName" );
		String oldVsName = (String) props.get( "OLDVIRTUALSERVER" );
		String newVsName = org.getVirtualServer() != null ? org.getVirtualServer().getShortName() : null;
		ServerMBean currentServerMBean = ConfigurationFactory.getServerMBean();
		String currentVsName = currentServerMBean.getVirtualServerName();

		// order provider list needs to be updated.
		if (!OrderServiceMBeanC.getInstance().isOrderProvidersListEnabled()) {
			if (currentVsName.equals(oldVsName) || currentVsName.equals(newVsName)) {
				OrderConfiguration.getInstance().resetOrderProviderOrgsList(false);
			}
		}

		if (OrderServiceMBeanC.getInstance().isOrderMigrationOnVirtualServerSwitchEnabled(orgName)) {
			OrderMigrationService orderMigrationService = OrderMigrationService.getInstance();
			boolean isOldVsDown = !orderMigrationService.isServerUp(oldVsName);
			boolean isNewVsDown = !orderMigrationService.isServerUp(newVsName);
			if (isOldVsDown) {
				boolean isCurrentServerAdmin = currentServerMBean.getVirtualServerType().equals(VirtualServerType.AdminServer);
				log.debug("Current Server is Admin=" + isCurrentServerAdmin);
				if (isCurrentServerAdmin) {
					boolean isCurrentServerAdminMaster = PrimaryAdminIdentifier.getInstance().isPrimary();
					log.debug("Current Server is Admin Master=" + isCurrentServerAdminMaster);
					if (isCurrentServerAdminMaster) {
						if (isNewVsDown) {
							// Raise alert that both the VS are down
							List<String> activeOrders = orderMigrationService.getActiveOrderList(orgName, oldVsName);
							if (activeOrders != null && !activeOrders.isEmpty()) {
								StringBuilder error = new StringBuilder("VirtualServer migration by user " + userName + " of " + orgName
										+ " Failed as both the oldVs=" + oldVsName
										+ " & newVs=" + newVsName + " are down. ActiveOrders=" + activeOrders);
								log.warn(error.toString());
								MessageLogger.getInstance().log(ISAlertMBean.ALERT_VS_SWITCH_ORDER_MIGRATION, "", error.toString(), "Both.VS.Down");
							}
						} else {
							List<String> activeOrders = orderMigrationService.getActiveOrderList(orgName, oldVsName);
							if (activeOrders != null && !activeOrders.isEmpty()) {
								OrderServiceMBeanC.OrderMigrationMode omm = OrderServiceMBeanC.getInstance().getOrderMigrationMode(orgName);
								switch (omm) {
									case MANUAL:
										// Raise alert that both the VS are down
										StringBuilder error = new StringBuilder("VirtualServer migration by user " + userName
												+ " is set as Manual & oldVs=" + oldVsName
												+ " is down. Hence for " + orgName
												+ " having oldVs=" + oldVsName + ", newVs=" + newVsName
												+ " perform the order migration manually for the ActiveOrders=" + activeOrders);
										log.warn(error.toString());
										MessageLogger.getInstance().log(ISAlertMBean.ALERT_VS_SWITCH_ORDER_MIGRATION, "", error.toString(), "New.VS.Down");
										break;
									case AUTO:
										// Perform order migration on Admin
										StringBuilder msg = new StringBuilder("VirtualServer migration by user " + userName
												+ " is set as Auto & hence triggering the migration from " + currentVsName
												+ ". Please ensure that for " + orgName
												+ " having oldVs=" + oldVsName + ", newVs=" + newVsName
												+ " the order migration is done correctly for the ActiveOrders=" + activeOrders);
										log.warn(msg.toString());
										MessageLogger.getInstance().log(ISAlertMBean.ALERT_VS_SWITCH_ORDER_MIGRATION, "", msg.toString(), "New.VS.Down");
										orderMigrationService.doOrderMigrationOnOldVirtualServer(org, oldVsName, newVsName);
										break;
									default:
										log.warn("Unsupported VirtualServer Order Migration mode");
								}
							}
						}
					}
				}
			} else if (currentVsName.equals(oldVsName)) {
				if (isNewVsDown) {
					// Raise alert that the new VS is down
					List<String> activeOrders = orderMigrationService.getActiveOrderList(orgName, oldVsName);
					StringBuilder error = new StringBuilder("VirtualServer migration by user " + userName + " of " + orgName
							+ " from oldVs=" + oldVsName
							+ " Failed as the newVs=" + newVsName + " is down. ActiveOrders=" + activeOrders);
					log.warn(error.toString());
					MessageLogger.getInstance().log(ISAlertMBean.ALERT_VS_SWITCH_ORDER_MIGRATION, "", error.toString(), "New.VS.Down");
				} else {
					orderMigrationService.doOrderMigrationOnOldVirtualServer(org, oldVsName, newVsName);
				}
			}
		}

		if (currentVsName.equals(newVsName)) {
			// ACTION is either CHANGED or ADDED
			log.warn("Updating adaptor common cache");
			OrderConfiguration.getInstance().UpdateAdaptorCommonCache(org);
		}
		return message;
	}
}
