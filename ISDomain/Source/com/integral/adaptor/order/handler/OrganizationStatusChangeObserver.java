/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.handler;

import java.util.Collection;
import java.util.Map;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.admin.AdminServiceC;
import com.integral.exception.IdcException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.dealing.CancellationCodes;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.OrderRequestServiceC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.Entity;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.user.Organization;

import static com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MOFIFICATION_NOTIFICATION_TYPE_STATUS_CHANGE;
import static com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_STATUS;
import static com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor.ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_STATUS_ACTIVE;

/**
 * <AUTHOR>
 *
 */
public class OrganizationStatusChangeObserver implements MessageHandler
{

	private Log log = LogFactory.getLog(this.getClass());

	/* (non-Javadoc)
	 * @see com.integral.message.MessageHandler#handle(com.integral.message.Message)
	 */
	public Message handle( Message message ) throws IdcException
	{
		Map props = message.getMap();
		String organizationName = (String) props.get(NamedEntity.ShortName);
		String notificationType = (String) props.get(AdminServiceC.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);
		if ( notificationType.equals(ORGANIZATION_MOFIFICATION_NOTIFICATION_TYPE_STATUS_CHANGE) )
		{
			String status = (String) props.get(ORGANIZATION_MODIFICATION_FUNCTOR_KEY_STATUS);

			if ( ORGANIZATION_MODIFICATION_FUNCTOR_VALUE_STATUS_ACTIVE.equals(status) )
			{
				return null;
			}
			Organization org = (Organization) ReferenceDataCacheC.getInstance().getEntityByShortName(organizationName, Organization.class, null, Entity.PASSIVE_STATUS);

			if ( org != null && status != null )
			{
				Collection<Organization> orderProviderList = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
				if ( orderProviderList.contains(org) )
				{
					log.info("OrganizationStatusChangeObserver.handle Invoking OrderRequestServiceC for org " + organizationName);
					OrderRequestServiceC ors = (OrderRequestServiceC) ServiceFactory.getOrderRequestService();
					final WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
					wfm.setEvent(ISConstantsC.MSG_EVT_WITHDRAWALL);
					wfm.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
					wfm.setObject(org);
					wfm.setParameterValue(OrderConstants.CXL_CODE, CancellationCodes.ORGANIZATION_INACTIVE);

					WorkflowMessage responseMessage = ors.process(wfm);
					Message out = responseMessage.getReplyMessage();
					if ( out == null )
					{
						out = responseMessage;
					}
					return out;
				}
				else
				{
					log.info("OrganizationStatusChangeObserver.handle : Org skipped. Not in orders provider list (extended). Org=" + organizationName);
				}
			}
			else
			{
				log.info("OrganizationStatusChangeObserver.handle : Org skipped. OrgName=" + organizationName + ",Org=" + org + ",status=" + status);
			}
		}
		return null;
	}

}
