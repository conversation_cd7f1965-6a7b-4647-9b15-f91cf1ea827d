package com.integral.adaptor.order.handler;

import java.util.Collection;

import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.adaptor.request.SubscriptionHandler;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.MarketRateResponseC;
import com.integral.is.message.MarketRateSubscribe;
import com.integral.is.message.MarketRateUnsubscribe;
import com.integral.is.message.ResponseMessage;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;

public class SubscriptionHandlerC implements SubscriptionHandler
{
	private OrderConfigurationMBean mbean;

	private Log log = LogFactory.getLog(this.getClass());

	public SubscriptionHandlerC()
	{
		mbean = OrderConfigurationFactory.getOrderConfigurationMBean();
	}

	public ResponseMessage startSubscription( MarketRateSubscribe message )
	{
		ResponseMessage response = new MarketRateResponseC();
		response.setStatus(ResponseMessage.SUCCESS);
		response.setServerId(message.getServerId());
		Collection<Organization> orgList = mbean.getExtendedOrderProviderOrgsList();
		String toOrgName = message.getProviderShortName();
		if ( isContains(orgList, toOrgName) )
		{
			log.warn("SubscriptionHandler.startSubscription:toOrg:" + toOrgName + " is one of the providers deployed as OA.Hence returning.");
			warmupCustomerOrgForLift(message);
			return response;
		}
		return response;
	}

	/**
	 * @param orgList
	 * @param toOrgName
	 * @return
	 */
	private boolean isContains( Collection<Organization> orgList, String toOrgName )
	{
		for ( Organization organization : orgList )
		{
			if ( organization.getShortName().equals(toOrgName) )
			{
				return true;
			}
		}
		return false;
	}

	/**
	 * @param message 
	 * 
	 */
	private void warmupCustomerOrgForLift( MarketRateSubscribe message )
	{
		try
		{
			String org = message.getOrgShortName();
			Organization o = ISUtilImpl.getInstance().getOrg(org);
			o.getOrganizationRelationships();
			o.getLegalEntities();
			QuoteConventionUtilC.getInstance().getFXRateConvention(o);
			Organization makerOrg = ISUtilImpl.getInstance().getOrg(message.getProviderShortName());
			User user = UserFactory.getUser(message.getUserShortName() + "@" + org);
			LegalEntity legalEntity = OrganizationUtil.getLegalEntity(o, message.getLeShortName());
			CurrencyPair currencyPair = CurrencyFactory.getCurrencyPair(message.getBaseCcy(), message.getVariableCcy());
			WarmUpTradeUtilC.getInstance().performReferenceDataWarmupOnSubscription(o, makerOrg, user, true, currencyPair, legalEntity);
		}
		catch ( Exception e )
		{

		}
	}

	public ResponseMessage stopSubscription( MarketRateUnsubscribe message )
	{
		ResponseMessage response = new MarketRateResponseC();
		response.setStatus(ResponseMessage.SUCCESS);
		response.setServerId(message.getServerId());
		return response;
	}

}
