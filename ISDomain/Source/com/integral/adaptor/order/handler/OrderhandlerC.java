package com.integral.adaptor.order.handler;


import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.request.OrderHandler;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.DealingPriceElement;
import com.integral.finance.dealing.ExecutionType;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXDealingPriceElementC;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceC;
import com.integral.finance.trade.Tenor;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeReject;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.rfs.RFSMessage;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderConstants;
import com.integral.is.oms.OrderLiftRequestC;
import com.integral.is.order.OAWorkflowFunctorC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.ProductType;
import com.integral.model.oms.OrderLiftRequest;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.time.IdcDateC;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserFactory;
import com.integral.util.IdcUtilC;

@SuppressWarnings("unchecked")
public class OrderhandlerC implements OrderHandler
{
	protected final static Log log = LogFactory.getLog(OrderhandlerC.class);
	

	/**
	 * 
	 * @param tradeRequest
	 * @return false is rejected
	 */
	protected boolean handleTradeRequest( TradeRequest tradeRequest )
	{
		try
		{
            log.info("handleTradeRequest: Received Lift order Request:" + tradeRequest.getTradeId() + " For order id" + tradeRequest.getProviderQuoteId());
            Boolean fromSpaces = ( Boolean ) tradeRequest.getProperty( ISCommonConstants.SPACES_ENABLED );
            boolean fromSpacesEnabled = fromSpaces == null ? false : fromSpaces;
            boolean spacesEnabled = ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled();
            boolean spaceDOEnabled = ISFactory.getInstance().getISMBean().isSpacesDOEnabled();
            if( fromSpacesEnabled && spacesEnabled && !spaceDOEnabled ){
                log.warn( "handleTradeRequest : Spaces Enabled OA received TradeRequest from Spaces Enabled Server. This is not supported" );
                sendTradeReject(tradeRequest, "SpacesToSpacesLiftNotSupported", "Spaces to Spaces Lift not supported");
                return false;
            }
            if( fromSpacesEnabled ){
                if( spacesEnabled && !spaceDOEnabled ){
                    log.warn( "handleTradeRequest : Spaces Enabled OA received TradeRequest from Spaces Enabled Server. This is not supported" );
                    sendTradeReject(tradeRequest, "SpacesToSpacesLiftNotSupported", "Spaces to Spaces Lift not supported");
                    return false;
                }
            }
            else{
                if( spacesEnabled ){
                    log.warn( "handleTradeRequest : Spaces Enabled OA received TradeRequest from Non Spaces Server. This is not supported" );
                    sendTradeReject(tradeRequest, "NonSpacesToSpacesLiftNotSupported", "Non-Spaces to Spaces Lift not supported");
                    return false;
                }
            }
			long receivedTime = System.currentTimeMillis();
			long receivedTimeNano = System.nanoTime();
			if ( tradeRequest.getProviderShortName().equalsIgnoreCase(tradeRequest.getOrgShortName()) )
			{
				if ( !OrderConfigurationFactory.getOrderConfigurationMBean().isIntrafloorMatchingEnabled() )
				{
					sendTradeReject(tradeRequest, "IntraFloorMatchingNotSupported", "Intrafloor matching not supported");
					return false;
				}
			}
            if( spacesEnabled ){
                WorkflowMessage wfMsg = createLiftOrderRequest();
                String errorCode = populateLiftRequestOnSpaces( wfMsg, tradeRequest );
                if ( errorCode != null )
                {
                    log.warn("OrderhandlerC.handleTradeRequest: Recieved Failure message ::" + errorCode + " Hence returning reject");
                    sendTradeReject(tradeRequest, errorCode, errorCode);
                    return false;
                }
                processTradeRequestOnSpaces( tradeRequest, receivedTime, receivedTimeNano, wfMsg );
            }
            else{
                WorkflowMessage wfMsg = createTakerOrderRequest();
                String errorCode = populateLiftRequest(wfMsg, tradeRequest);
                if ( errorCode != null )
                {
                    log.warn("OrderhandlerC.handleTradeRequest: Recieved Failure message ::" + errorCode + " Hence returning reject");
                    sendTradeReject(tradeRequest, errorCode, errorCode);
                    return false;
                }
                processTradeRequest( tradeRequest, receivedTime, receivedTimeNano, wfMsg );
            }
		}
		catch ( Exception e )
		{
			log.error("OrderhandlerC.handleTradeRequest - Rejecting trade. Exception while processing trade request - " + tradeRequest, e);
			sendTradeReject(tradeRequest, ISConstantsC.INTERNAL_SERVER_ERROR, ISConstantsC.INTERNAL_SERVER_ERROR);
			return false;
		}
		return true;
	}

    private void processTradeRequest( TradeRequest tradeRequest, long receivedTime, long rt, WorkflowMessage wfMsg ) {
        long nt2 = System.nanoTime();
        String bookName = getDestinationOrderBookName(tradeRequest);
        Request liftRequest = ( Request ) wfMsg.getObject();
        boolean isWarmup = ISCommonUtilC.isWarmUpObject( tradeRequest );
        if ( isWarmup )
        {
            ISCommonUtilC.setAsWarmUpObject(wfMsg);
            ISCommonUtilC.setAsWarmUpObject(liftRequest);
        }
        String tradeTransactionId = (String) tradeRequest.getProperty( ISConstantsC.TRADE_TRANSACTIONID);
        String takerOrderId = (String) tradeRequest.getProperty( OAWorkflowFunctorC.TAKER_ORDERID);
        long effTime = (Long) tradeRequest.getProperty(OAWorkflowFunctorC.TAKER_ORDER_EFFECTIVETIME);
        String isPBTrade = (String) tradeRequest.getProperty( ISCommonConstants.isPrimeBrokerCoverTradeEnabled);
        //There may be multiple lift requests from same order id, so merge it with trade request's txn id to make it unique.
        String liftRequestOrderId = takerOrderId + '-' + tradeRequest.getTradeId();
        liftRequest.setOrderId(liftRequestOrderId);
        liftRequest.getRequestAttributes().setTradeTransactionID(tradeTransactionId);
        liftRequest.setProperty(ISConstantsC.OA_BOOK_NAME, bookName);
        liftRequest.getRequestAttributes().setLiftOrderRequest();
        liftRequest.setProperty( OrderConstants.TAKER_ORDER_EFFECTIVE_TIME, effTime);
        liftRequest.setProperty(OrderConstants.TAKER_ORDER_RECEIVED_TIME, receivedTime);
        if ( isPBTrade != null && isPBTrade.equalsIgnoreCase("true") )
        {
            liftRequest.setProperty(ISConstantsC.isPrimeBrokerCoverTradeEnabled, Boolean.TRUE);
        }

        wfMsg.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
        wfMsg.setEvent(ISConstantsC.MSG_EVT_MATCH);
        wfMsg.setStatus( MessageStatus.SUCCESS);


        if ( tradeRequest.getProperty( OARFSHandlerC.IS_OUTRIGHT) != null && ((Boolean) tradeRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT)).booleanValue() == true )
        {
            OutrightTradeResponseHandler trh = new OutrightTradeResponseHandler(receivedTime);
            trh.setReferenceId(liftRequest.getTransactionID());
            wfMsg.setParameterValue("messageHandler", trh);
        }
        else
        {
            TradeResponseHandler trh = new TradeResponseHandler(receivedTime);
            trh.setReferenceId(liftRequest.getTransactionID());
            wfMsg.setParameterValue("messageHandler", trh);
        }


        WorkflowMessage respMsg = (WorkflowMessage) ServiceFactory.getOrderRequestService().process(wfMsg).getReplyMessage();

        liftRequest.getRequestAttributes().addOrderWFProcessingTime("OH.1", (nt2 - rt));
        liftRequest.getRequestAttributes().addOrderWFProcessingTime("OH.2", (System.nanoTime() - nt2));

        if ( MessageStatus.SUCCESS != respMsg.getStatus() )
        {
            String error = ISConstantsC.INTERNAL_SERVER_ERROR;
            if ( respMsg.getErrors() != null )
            {
                error = ISUtilImpl.getInstance().getErrorMessage(respMsg.getErrors()).toString();
            }
            log.warn("OrderhandlerC.handleTradeRequest: Recieved Failure message ::" + error + " Hence returning reject");
            sendTradeReject(tradeRequest, error, error);
        }
        if ( !isWarmup )
        {
            IdcUtilC.logProcesingTimes( liftRequest );
        }
    }

    private void processTradeRequestOnSpaces( TradeRequest tradeRequest, long receivedTime, long receivedTimeNano, WorkflowMessage wfMsg ) {
        long nt2 = System.nanoTime();

        String bookName = getDestinationOrderBookName(tradeRequest);
        OrderLiftRequest orderLiftRequest = ( OrderLiftRequest ) wfMsg.getObject();
        boolean isWarmup = ISCommonUtilC.isWarmUpObject( tradeRequest );
        if ( isWarmup )
        {
            ISCommonUtilC.setAsWarmUpObject(wfMsg);
            orderLiftRequest.setWarmUp(true);
        }
        String tradeTransactionId = (String) tradeRequest.getProperty( ISConstantsC.TRADE_TRANSACTIONID);
        String takerOrderId = (String) tradeRequest.getProperty( OAWorkflowFunctorC.TAKER_ORDERID);
        long effTime = (Long) tradeRequest.getProperty(OAWorkflowFunctorC.TAKER_ORDER_EFFECTIVETIME);
        String isPBTrade = (String) tradeRequest.getProperty( ISCommonConstants.isPrimeBrokerCoverTradeEnabled);
        //There may be multiple lift requests from same order id, so merge it with trade request's txn id to make it unique.
        String liftRequestOrderId = takerOrderId + '-' + tradeRequest.getTradeId();
        orderLiftRequest.setOrderId(liftRequestOrderId);
        orderLiftRequest.setTradeTransactionId( tradeTransactionId);
        orderLiftRequest.setBookName(bookName);
        orderLiftRequest.setEffectiveTime( effTime );
        orderLiftRequest.setReceivedTime( receivedTime );
        orderLiftRequest.setReceivedTimeNano( receivedTimeNano );
        if ( isPBTrade != null && isPBTrade.equalsIgnoreCase("true") )
        {
            orderLiftRequest.setPrimeBrokerCoverTradeEnabled(true);
        }

        wfMsg.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
        wfMsg.setEvent(ISConstantsC.MSG_EVT_MATCH);
        wfMsg.setStatus( MessageStatus.SUCCESS);
        
        if ( tradeRequest.getProperty( OARFSHandlerC.IS_OUTRIGHT) != null && ((Boolean) tradeRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT)).booleanValue() == true )
        {
            OutrightTradeResponseHandler trh = new OutrightTradeResponseHandler(receivedTime);
           orderLiftRequest.setHandler(trh);
        }
        else
        {
            OrderLiftRequestResponseHandler trh = new OrderLiftRequestResponseHandler( receivedTime );
            orderLiftRequest.setHandler( trh );
        }
        
        WorkflowMessage respMsg = (WorkflowMessage) ServiceFactory.getOrderRequestService().process(wfMsg).getReplyMessage();
        orderLiftRequest.addProcessingTime( "OH.1", (nt2 - receivedTimeNano) );
        orderLiftRequest.addProcessingTime( "OH.2", (System.nanoTime() - nt2));
        if ( MessageStatus.SUCCESS != respMsg.getStatus() )
        {
            String error = ISConstantsC.INTERNAL_SERVER_ERROR;
            if ( respMsg.getErrors() != null )
            {
                error = ISUtilImpl.getInstance().getErrorMessage(respMsg.getErrors()).toString();
            }
            log.warn("processTradeRequestOnSpaces: Recieved Failure message ::" + error + " Hence returning reject");
            sendTradeReject(tradeRequest, error, error);
        }
        if ( !isWarmup )
        {
            IdcUtilC.logProcesingTimes( orderLiftRequest );
        }
    }

    public String getDestinationOrderBookName( TradeRequest request )
	{
		String name = null;
		String pqid = request.getProviderQuoteId();
		String bookIndex = pqid.split("#")[6];
		OrderBook book = OrderBookCacheC.getInstance().getOrderBookByIndex(Integer.parseInt(bookIndex));
		if ( book != null )
		{
			name = book.getBookName();
		}
		return name;
	}

	/*
	 * <workflowMessage>
	<request>
	    <requestClassification>LIMIT</requestClassification>
	    <user/>
	    <fxLegPrice>
	        <name>singleLeg</name>
	        <fxPriceElement>
	            <fxPrice/>
	        </fxPriceElement>
	        <bidOffer>TWO_WAY</bidOffer>
	        <tenor>SPOT</tenor>
	        <dealtCurrencyProperty/>
	        <dealtCurrency/>
	        <settledCurrency/>
	        <dealtAmount/>
	    </fxLegPrice>
	    <customField key="OrderCount" type="java.lang.String">0</customField>
	</request>
	<parameter key='OrderCount'>0</parameter>
	</workflowMessage>
	 */
	private WorkflowMessage createTakerOrderRequest()
	{
		WorkflowMessage message = com.integral.message.MessageFactory.newWorkflowMessage();
		Request request = DealingFactory.newRequest();
		request.setRequestClassification(ISUtilImpl.getInstance().getRequestClassification(ISCommonConstants.MAKEPRICE_CREATE_TYPE));
		FXLegDealingPrice dp = new FXLegDealingPriceC();
		dp.setName(ISConstantsC.SINGLE_LEG);
		FXDealingPriceElement dpe = new FXDealingPriceElementC();
		dp.setPriceElement(dpe);
		request.setRequestPrice(ISConstantsC.SINGLE_LEG, dp);
		message.setParameterValue(ISCommonConstants.CLIENT_ORDER_COUNT, "0");
        message.setObject( request );
        return message;
	}

    private WorkflowMessage createLiftOrderRequest()
    {
        WorkflowMessage message = com.integral.message.MessageFactory.newWorkflowMessage();
        OrderLiftRequest liftRequest = new OrderLiftRequestC();
        liftRequest.setClassification(ISCommonConstants.MAKEPRICE_CREATE_TYPE);
        message.setObject(liftRequest);
        return message;
    }

	/**
	 * Sends tradeRejectMessage
	 *
	 * @param isMsg
	 * @param reasonCode
	 * @param reasonDesc
	 */
	private void sendTradeReject( TradeRequest isMsg, String reasonCode, String reasonDesc )
	{
		try
		{
			if ( ISCommonUtilC.isWarmUpObject(isMsg) )
			{
				return;
			}
			TradeReject tradeReject = MessageFactory.newTradeReject();
			tradeReject.setRejectedAmount(isMsg.getAmount());
			tradeReject.setRequestReferenceId(isMsg.getRequestId());
			tradeReject.setReasonCode(reasonCode);
			tradeReject.setReasonDescription(reasonDesc);
			tradeReject.setTradeId(isMsg.getTradeId());
			tradeReject.setRequestReferenceId(isMsg.getRequestId());
			AdaptorConfigurationFactory.getResponseHandlerInstance().tradeRecieved(tradeReject);
		}
		catch ( Exception e )
		{
			log.error("OrderhandlerC.sendTradeReject:Error -", e);
		}
	}

	/**
	 * Populates the Limit Order Request with the given TradeRequest
	 *
	 * @param message
	 * @param trdRequest TradeRequest
	 * @return String error message.
	 */
	private String populateLiftRequest( WorkflowMessage message, TradeRequest trdRequest )
	{
		Request request = ( Request ) message.getObject();

        String tradeId = trdRequest.getTradeId();
		String userName = trdRequest.getUserShortName();
		double rate = trdRequest.getRate();
		double amount = trdRequest.getAmount();
		int buySell = trdRequest.getBuySell();
		String dealtCCY = trdRequest.getDealtCcy();
		String baseCCY = trdRequest.getBaseCcy();
		String varCCY = trdRequest.getVariableCcy();
		String orgShortName = trdRequest.getOrgShortName();
		String providerShortName = trdRequest.getProviderShortName();
		String leShortName = trdRequest.getLeShortName();
		int intBidOffer = DealingPrice.BID;
		if ( buySell == TradeRequest.SELL )
		{
			intBidOffer = DealingPrice.OFFER;
		}
		if ( tradeId != null )
		{
			request.setExternalRequestId( tradeId );
		}
		FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
		FXLegDealingPrice fillPrice = new FXLegDealingPriceC();
		fillPrice.setDealtAmount(0.0);
		dp.setFillDealingPrice( fillPrice );
        dp.setDealtAmount(amount);

        dp.setDealtCurrency( CurrencyFactory.getCurrency( dealtCCY ) );
		dp.setDealtCurrencyProperty( baseCCY.equals( dealtCCY ) ? FXLegDealingPrice.CCY1 : FXLegDealingPrice.CCY2 );
		dp.setSettledCurrency( CurrencyFactory.getCurrency( dealtCCY.equals( baseCCY ) ? varCCY : baseCCY ) );
        dp.setDealtAmount(amount);
		dp.setExecutionType(ExecutionType.LIFT);
		dp.setTenor(new Tenor(Tenor.SPOT));
		dp.setMaxShowAmount(0.0d);
		DealingPriceElement dpe = dp.getPriceElement();
		FXPrice fxprice = new FXPriceC();
		FXRate fxRate = new FXRateC();
		fxRate.setRate(rate);
		fxRate.setSpotRate( rate );
		fxRate.setBaseCurrency(CurrencyFactory.getCurrency(baseCCY));
		fxRate.setVariableCurrency(CurrencyFactory.getCurrency(varCCY));
        if ( intBidOffer == DealingPrice.BID )
		{
			fxprice.setBidFXRate(fxRate);
			dp.setBidOfferMode(DealingPrice.BID);
		}
		else if ( intBidOffer == DealingPrice.OFFER )
		{
			fxprice.setOfferFXRate(fxRate);
			dp.setBidOfferMode(DealingPrice.OFFER);
		}

		dpe.setPrice( fxprice );

        populateOutrightLiftOrderFields( trdRequest, request );

		long st = System.nanoTime();
		User user = UserFactory.getUser(userName + "@" + orgShortName);
		if ( user == null )
		{
			return "User lookup failed.Name=" + userName + "@" + orgShortName;
		}
		request.getRequestAttributes().addOrderWFProcessingTime("OH.ursFac", (System.nanoTime() - st));
		IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
		IdcSessionManager.getInstance().setSessionContext(ctx);
		message.setSender(user);
		request.setUser(user);
		request.setCurrencyPair(CurrencyFactory.getCurrencyPair(baseCCY, varCCY));
		Organization counterOrg = ISUtilImpl.getInstance().getOrg(providerShortName);
		Collection toOrgs = new ArrayList();
		toOrgs.add(counterOrg);
		request.setToOrganizations( toOrgs );
		Organization org = user.getOrganization();
		request.setOrganization(user.getOrganization());
		request.setNamespace(user.getNamespace());
		if ( leShortName != null )
		{
			Iterator itrle = org.getLegalEntities().iterator();
			while ( itrle.hasNext() )
			{
				LegalEntity element = (LegalEntity) itrle.next();
				if ( element.getShortName().equals(leShortName) )
				{
					request.setCounterparty( element );
				}
			}
		}
		st = System.nanoTime();
		fxRate.setFXRateConvention(QuoteConventionUtilC.getInstance().getFXRateConvention(org));
		request.getRequestAttributes().addOrderWFProcessingTime("OH.stCnv", (System.nanoTime() - st));

        setMatchingPreferences(request, counterOrg);

		String channel = "DirectFXTrader";
		request.setChannel(ISUtilImpl.getInstance().getExtSys(channel));
        Integer tif = (Integer) trdRequest.getProperty(OAWorkflowFunctorC.TAKER_ORDER_TIMEINFORCE);
		if ( tif != null )
		{
			//Right now taker order can have only FOK or IOC  
			if ( tif.intValue() == TimeInForce.FOK )
			{
				request.setTimeInForce(TimeInForce.FOK);
				dp.setMinDealtAmount( amount );
			}
			else
			{
				int timeOutPeriod = OrderConfiguration.getInstance().getFlashLiftTimeout(org);
				if ( timeOutPeriod > 0 )
				{
					request.setTimeInForce( TimeInForce.GTD );
					message.setParameterValue(ISCommonConstants.EXP_TIME, new Long(timeOutPeriod));
				}
				else
				{
					request.setTimeInForce(TimeInForce.IOC);
				}
			}
		}
		else
		{
			request.setTimeInForce(TimeInForce.IOC);
		}

		Double minQty = (Double) trdRequest.getProperty(OAWorkflowFunctorC.TAKER_ORDER_MINQTY);
		if ( minQty != null && request.getTimeInForce() != TimeInForce.FOK )
		{
			dp.setMinDealtAmount(minQty);
		}

		request.setOriginatingOrderId(trdRequest.getOriginatingOrderid());

		if(trdRequest.getOriginatingCpty() != null)
		{
			try {
                request.setOriginatingCptyId( Long.parseLong( trdRequest.getOriginatingCpty() ) );
	        } catch ( NumberFormatException e ) {
	            log.error( this.getClass().getName() + ".populateLiftRequest() not a valid originating cpty id " + trdRequest.getOriginatingCpty() );
	        } catch ( ClassCastException ex ){
                log.error( this.getClass().getName() + ".populateLiftRequest() not a valid originating cpty id " + trdRequest.getOriginatingCpty() );
            }
		}

		if(null != trdRequest.getOriginatingUser())
		{
			try{
				long originatingUserId = Long.parseLong(trdRequest.getOriginatingUser());
				request.setOriginatingUserId(originatingUserId);
			}catch ( NumberFormatException e ) {
	            log.error( this.getClass().getName() + ".populateLiftRequest() not a valid originating user id " + trdRequest.getOriginatingUser() );
	        } catch ( ClassCastException ex ){
                log.error( this.getClass().getName() + ".populateLiftRequest() not a valid originating user id " + trdRequest.getOriginatingUser() );
            }
		}
		
		return null;
	}

    private String populateLiftRequestOnSpaces( WorkflowMessage message, TradeRequest trdRequest )
    {
        OrderLiftRequest liftRequest = ( OrderLiftRequest ) message.getObject();
        String tradeId = trdRequest.getTradeId();
        String userName = trdRequest.getUserShortName();
        double rate = trdRequest.getRate();
        double amount = trdRequest.getAmount();
        int buySell = trdRequest.getBuySell();
        String dealtCCY = trdRequest.getDealtCcy();
        String baseCCY = trdRequest.getBaseCcy();
        String varCCY = trdRequest.getVariableCcy();
        String orgShortName = trdRequest.getOrgShortName();
        String providerShortName = trdRequest.getProviderShortName();
        String leShortName = trdRequest.getLeShortName();
        int intBidOffer = DealingPrice.BID;
        if ( buySell == TradeRequest.SELL )
        {
            intBidOffer = DealingPrice.OFFER;
        }
        if ( tradeId != null )
        {
            liftRequest.setClientReferenceId( tradeId );
        }
        liftRequest.setDealtAmount(amount);
        liftRequest.setDealtCurrency( CurrencyFactory.getCurrency( dealtCCY ) );
        liftRequest.setCurrencyPair( CurrencyFactory.getCurrencyPair( baseCCY, varCCY ) );
        liftRequest.setTenor( Tenor.SPOT );
        liftRequest.setSpotRate( rate );
        liftRequest.setRate( rate );
        liftRequest.setValueDate( trdRequest.getValueDate().getTime() );
        OrderLiftRequest.BuySellMode buySellMode = null;
        if ( intBidOffer == DealingPrice.BID )
        {
            buySellMode = OrderLiftRequest.BuySellMode.SELL;
        }
        else if ( intBidOffer == DealingPrice.OFFER )
        {
            buySellMode = OrderLiftRequest.BuySellMode.BUY;
        }
        liftRequest.setBuySell( buySellMode );
        /*
        if ( trdRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT) != null && ((Boolean) trdRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT)) ){
            return "Outright not supported on Spaces";
        }
        */
        long st = System.nanoTime();
        User user = UserFactory.getUser(userName + "@" + orgShortName);
        if ( user == null )
        {
            return "User lookup failed.Name=" + userName + "@" + orgShortName;
        }
        liftRequest.addProcessingTime("OH.ursFac", (System.nanoTime() - st));
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
        IdcSessionManager.getInstance().setSessionContext( ctx );
        message.setSender( user );
        liftRequest.setUser( user );
        Organization counterOrg = ISUtilImpl.getInstance().getOrg(providerShortName);
        liftRequest.setToOrganization( counterOrg );
        Organization org = user.getOrganization();
        liftRequest.setOrganization( user.getOrganization() );
        if ( leShortName != null )
        {
            Iterator itrle = org.getLegalEntities().iterator();
            while ( itrle.hasNext() )
            {
                LegalEntity element = (LegalEntity) itrle.next();
                if ( element.getShortName().equals(leShortName) )
                {
                    liftRequest.setLegalEntity(element);
                }
            }
        }
        st = System.nanoTime();
        FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getFXRateConvention( org );
        liftRequest.setFXRateBasis( fxRateConvention.getFXRateBasis( liftRequest.getCurrencyPair() ) );
        liftRequest.addProcessingTime( "OH.stCnv", ( System.nanoTime() - st ) );

        String channel = "DirectFXTrader";
        liftRequest.setChannel(channel);
        Integer tif = (Integer) trdRequest.getProperty(OAWorkflowFunctorC.TAKER_ORDER_TIMEINFORCE);
        if ( tif != null )
        {
            //Right now taker order can have only FOK or IOC
            if ( tif == TimeInForce.FOK )
            {
                liftRequest.setMinDealtAmount( amount );
                liftRequest.setTimeInForce(OrderLiftRequest.TimeInForce.FOK);
            }
            else
            {
                int timeOutPeriod = OrderConfiguration.getInstance().getFlashLiftTimeout(org);
                if ( timeOutPeriod > 0 )
                {
                    liftRequest.setTimeInForce( OrderLiftRequest.TimeInForce.GTD );
                    liftRequest.setExpiry(timeOutPeriod);
                }
                else
                {
                    liftRequest.setTimeInForce( OrderLiftRequest.TimeInForce.IOC );
                }
            }
        }
        else
        {
            liftRequest.setTimeInForce( OrderLiftRequest.TimeInForce.IOC );
        }

        Double minQty = (Double) trdRequest.getProperty(OAWorkflowFunctorC.TAKER_ORDER_MINQTY);
        if ( minQty != null && liftRequest.getTimeInForce() != OrderLiftRequest.TimeInForce.FOK )
        {
            liftRequest.setMinDealtAmount(minQty);
        }

        liftRequest.setOriginatingOrderId( trdRequest.getOriginatingOrderid());

        if(trdRequest.getOriginatingCpty() != null)
        {
            try {
                st = System.nanoTime();
                long objectId = Long.parseLong(trdRequest.getOriginatingCpty());
                liftRequest.setOriginatingLegalEntity( ( LegalEntity ) ReferenceDataCacheC.getInstance().getEntityByObjectId( objectId,LegalEntity.class ) );
                liftRequest.addProcessingTime( "OH.origLE", ( System.nanoTime() - st ) );
            } catch ( NumberFormatException e ) {
                log.error( this.getClass().getName() + ".populateLiftRequest() not a valid originating cpty id " + trdRequest.getOriginatingCpty() );
            } catch ( ClassCastException ex ){
                log.error( this.getClass().getName() + ".populateLiftRequest() not a valid originating cpty id " + trdRequest.getOriginatingCpty() );
            }
        }   
        /**
         * populate required fields for lifting outright orders
         */
        populateOutrightLiftOrderFields( trdRequest, liftRequest );
        return null;
    }

	private void populateOutrightLiftOrderFields( TradeRequest trdRequest, Request request )
	{
		if ( trdRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT) != null && ((Boolean) trdRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT)) )
		{
			FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
			
			if ( trdRequest.getValueDate() != null )
			{
				dp.setValueDate(IdcDateC.newDate( trdRequest.getValueDate() ));
			}
			if ( trdRequest.getProperty(OARFSHandlerC.TENOR) != null )
			{
				dp.setTenor(new Tenor((String)trdRequest.getProperty(OARFSHandlerC.TENOR)));
			}
			else
			{
				dp.setTenor(null);
			}

			if ( trdRequest.getProperty(OARFSHandlerC.FIXING_TENOR) != null )
			{
				dp.setFixingTenor(new Tenor((String) trdRequest.getProperty(OARFSHandlerC.FIXING_TENOR)));
			}
			if ( trdRequest.getProperty(OARFSHandlerC.FIXING_DATE) != null )
			{
				dp.setFixingDate(IdcDateC.newDate( ( Date ) trdRequest.getProperty( OARFSHandlerC.FIXING_DATE ) ));
			}
			if ( trdRequest.getProperty(OARFSHandlerC.SPOT_RATE) != null )
			{
				dp.getFXRate().setSpotRate( ( Double ) trdRequest.getProperty( OARFSHandlerC.SPOT_RATE ) );
			}
			if ( trdRequest.getProperty(OARFSHandlerC.FWD_PTS) != null )
			{
				dp.getFXRate().setForwardPoints( ( Double ) trdRequest.getProperty( OARFSHandlerC.FWD_PTS ) );
			}
			if ( trdRequest.getProperty( RFSMessage.UPI_KEY ) != null )
			{
				request.setProperty( RFSMessage.UPI_KEY, trdRequest.getProperty( RFSMessage.UPI_KEY ) );
			}
			if ( trdRequest.getProperty( RFSMessage.CPTY_A_KEY ) != null )
			{
				request.setProperty( RFSMessage.CPTY_A_KEY, trdRequest.getProperty( RFSMessage.CPTY_A_KEY ) );
			}
			if ( trdRequest.getProperty( RFSMessage.CPTY_B_KEY ) != null )
			{
				request.setProperty( RFSMessage.CPTY_B_KEY, trdRequest.getProperty( RFSMessage.CPTY_B_KEY ) );
			}
			if ( trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_1_KEY ) != null )
			{
				request.setProperty( RFSMessage.CLEARING_MEMBER_1_KEY, trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_1_KEY ) );
			}
			if ( trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_2_KEY ) != null )
			{
				request.setProperty( RFSMessage.CLEARING_MEMBER_2_KEY, trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_2_KEY ) );
			}
			if ( trdRequest.getProperty( RFSMessage.CLEARING_HOUSE_KEY ) != null )
			{
				request.setProperty( RFSMessage.CLEARING_HOUSE_KEY, trdRequest.getProperty( RFSMessage.CLEARING_HOUSE_KEY ) );
			}
			if ( trdRequest.getProperty( RFSMessage.SDR_KEY ) != null )
			{
				request.setProperty( RFSMessage.SDR_KEY, trdRequest.getProperty( RFSMessage.SDR_KEY ) );
			}
			if ( trdRequest.getProperty( OARFSHandlerC.USI ) != null )
			{
				request.setProperty( OARFSHandlerC.USI, trdRequest.getProperty( OARFSHandlerC.USI ) );
			}
			dp.getFXRate().setRate( trdRequest.getRate() );
			request.setOutrightLimitOrder( true );
		}
	}
	
	private void populateOutrightLiftOrderFields( TradeRequest trdRequest, OrderLiftRequest request )
	{
		if ( trdRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT) != null && ((Boolean) trdRequest.getProperty(OARFSHandlerC.IS_OUTRIGHT)) )
		{			
			//FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
			
			if ( trdRequest.getValueDate() != null )
			{
				//dp.setValueDate(IdcDateC.newDate( trdRequest.getValueDate() ));
				request.setValueDate( trdRequest.getValueDate().getTime() );
			}
			Object tenor = trdRequest.getProperty(OARFSHandlerC.TENOR);
			if ( tenor != null )
			{
				//dp.setTenor(new Tenor((String)trdRequest.getProperty(OARFSHandlerC.TENOR)));
				request.setTenor((String)tenor);
			}
			else
			{
				request.setTenor(null);
			}
			Object fixingTenor = trdRequest.getProperty(OARFSHandlerC.FIXING_TENOR);
			if ( fixingTenor != null )
			{
				request.setFixingTenor((String) fixingTenor);
			}
			
			Object fixingDate = trdRequest.getProperty(OARFSHandlerC.FIXING_DATE);
			if ( fixingDate != null )
			{
				request.setFixingDate((( Date )fixingDate).getTime() );
			}
			
			Object spotRate = trdRequest.getProperty(OARFSHandlerC.SPOT_RATE);
			if ( spotRate != null )
			{
				//dp.getFXRate().setSpotRate( ( Double ) trdRequest.getProperty( OARFSHandlerC.SPOT_RATE ) );
				request.setSpotRate( ( Double ) spotRate );
			}
			
			Object forwardPoint = trdRequest.getProperty(OARFSHandlerC.FWD_PTS);
			if ( forwardPoint != null )
			{
				//dp.getFXRate().setForwardPoints( ( Double ) trdRequest.getProperty( OARFSHandlerC.FWD_PTS ) );
				request.setForwardPoint( ( Double ) forwardPoint );
			}
			
			Object upi = trdRequest.getProperty( RFSMessage.UPI_KEY );
			if ( upi != null )
			{
				//request.setProperty( RFSMessage.UPI_KEY, trdRequest.getProperty( RFSMessage.UPI_KEY ) );
				request.setUPI((String)upi);
			}
			Object counterPartA = trdRequest.getProperty( RFSMessage.CPTY_A_KEY );
			if ( counterPartA != null )
			{
				//request.setProperty( RFSMessage.CPTY_A_KEY, trdRequest.getProperty( RFSMessage.CPTY_A_KEY ) );
				request.setCptyA((String)counterPartA);
			}
			
			Object counterPartyB = trdRequest.getProperty( RFSMessage.CPTY_B_KEY );
			if ( counterPartyB != null )
			{
				request.setCptyB((String)counterPartyB);
				//request.setProperty( RFSMessage.CPTY_B_KEY, trdRequest.getProperty( RFSMessage.CPTY_B_KEY ) );
			}
			
			Object cptyAClearingMember = trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_1_KEY );
			if ( cptyAClearingMember != null )
			{
				//request.setProperty( RFSMessage.CLEARING_MEMBER_1_KEY, trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_1_KEY ) );
				request.setCptyAClearingMember((String)cptyAClearingMember);
			}
			
			Object cptyBClearingMember = trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_2_KEY );
			if ( cptyBClearingMember != null )
			{
				//request.setProperty( RFSMessage.CLEARING_MEMBER_2_KEY, trdRequest.getProperty( RFSMessage.CLEARING_MEMBER_2_KEY ) );
				request.setCptyBClearingMember((String)cptyBClearingMember);
			}
			
			Object clearingHouse = trdRequest.getProperty( RFSMessage.CLEARING_HOUSE_KEY );
			if ( clearingHouse != null )
			{
				//request.setProperty( RFSMessage.CLEARING_HOUSE_KEY, trdRequest.getProperty( RFSMessage.CLEARING_HOUSE_KEY ) );
				request.setClearingHouse((String)clearingHouse);
			}
			
			Object sdr = trdRequest.getProperty( RFSMessage.SDR_KEY );
			if ( sdr != null )
			{
				//request.setProperty( RFSMessage.SDR_KEY, trdRequest.getProperty( RFSMessage.SDR_KEY ) );
				request.setSDR((String)sdr);
			}
			
			Object usi = trdRequest.getProperty( OARFSHandlerC.USI );
			if ( usi != null )
			{
				//request.setProperty( OARFSHandlerC.USI, trdRequest.getProperty( OARFSHandlerC.USI ) );
				request.setUSI((String)usi);
			}
			//dp.getFXRate().setRate( trdRequest.getRate() );
			request.setRate(trdRequest.getRate());
			//request.setOutrightLimitOrder( true );
			FXRateBasis fxRateBasis = request.getFXRateBasis();
			if (fxRateBasis != null) {
				if (fxRateBasis.isNonDeliverable()) {
					request.setProductType(ProductType.FXNDF);
				} else {
					request.setProductType(ProductType.FXFWD);
				}
			}
			
		}
	}

    private void setMatchingPreferences( Request request, Organization counterOrg )
	{
		ArrayList<Organization> lpList = new ArrayList<Organization>();
		lpList.add(counterOrg);
		request.setProperty(ISConstantsC.WF_PARAM_PreferredProviders, lpList);
	}

    public ResponseMessage quoteAccepted(TradeRequest request) {
        try {
            long st = System.currentTimeMillis();
            handleTradeRequest(request);
            long et = System.currentTimeMillis();
            StringBuilder sb = new StringBuilder(200).append("quoteAccepted: ");
            sb.append("OrderId=").append(request.getOrderTxnId()).append(", TradeId=").append(request.getTradeId());
            sb.append(", ReceivedTime=").append(st).append(", CompletedTime=").append(et);
            sb.append(", Processing [Creation,Submission,Match] time =").append(et - st).append("ms");
            log.warn(sb.toString());
        } catch (Exception e) {
            log.error("quoteAccepted() : could not start acceptance ", e);
			return respondFailure(request.getRequestId(), e.getMessage());
		}
		return respondSuccess(request.getRequestId());
	}

	private static ResponseMessage respondSuccess( String requestId )
	{
		final ResponseMessage response = MessageFactory.newResponseMessage();
		response.setRequestReferenceId(requestId);
		response.setStatus(ResponseMessage.SUCCESS);
		return response;
	}

	private static ResponseMessage respondFailure( String requestId, String reason )
	{
		final ResponseMessage response = MessageFactory.newResponseMessage();
		response.setRequestReferenceId(requestId);
		response.setStatus(ResponseMessage.FAILURE);
		response.setFailureReason(reason);
		return response;
	}

}
