package com.integral.adaptor.order.handler;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

import com.integral.adaptor.request.OrderHandler;
import com.integral.broker.cache.BrokerQuoteCache;
import com.integral.broker.cache.ProviderQuoteCache;
import com.integral.broker.model.DealerSpread;
import com.integral.broker.model.Product;
import com.integral.broker.publish.MDSRFSPublisher;
import com.integral.broker.publish.rfs.RFSPublisher;
import com.integral.broker.quote.MDSPriceFacade;
import com.integral.broker.request.BrokerTradeHandler;
import com.integral.broker.request.TradeRequestFacade;
import com.integral.broker.request.TradeResponseC;
import com.integral.broker.request.rfs.RFSSpotAndSwapRiskEmailHandler;
import com.integral.broker.rfs.RFSHandler;
import com.integral.broker.rfs.subscribe.RFSRequestFacadeC;
import com.integral.broker.rfs.subscribe.RFSSubscriber;
import com.integral.exception.IdcException;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.TimeInForce;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.rfs.RFSFXLeg;
import com.integral.is.message.rfs.RFSFXRate;
import com.integral.is.message.rfs.RFSSubscribe;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.is.message.rfs.RFSUnsubscribe;
import com.integral.is.order.OAWorkflowFunctorC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.persistence.ExternalSystemId;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;
import com.integral.user.User;

public class OARFSHandlerC implements RFSHandler, Serializable
{
    /**
     * Logger for this class and its descendants.
     */
    protected Log log = LogFactory.getLog( this.getClass() );
    
    public static final String IS_OUTRIGHT = "IsOutright";
    public static final String FWD_PTS = "FwdPoints";
    public static final String FIXING_DATE = "FixingDate";
    public static final String FIXING_TENOR = "FixingTenor";
    public static final String SPOT_RATE = "BaseRate";
    public static final String TENOR = "Tenor";
    public static final String USI = "USI";
    
    protected OrderHandler orderhandler ;
    
    public OARFSHandlerC(OrderHandler orderhandler)
	{
    	this.orderhandler = orderhandler;
	}
    
	public ResponseMessage startRFSSubscription( RFSSubscribe message )
	{
		return null;
	}
	
	private TradeRequest getTradeRequestFromRFSTradeRequest( RFSTradeRequest request )
	{
		TradeRequest tradeRequest = MessageFactory.newTradeRequest();

		ISUtilImpl isUtil = ISUtilImpl.getInstance();

		if ( isUtil.isWarmUpObject(request) )
		{
			isUtil.setAsWarmUpObject(tradeRequest);
		}
		Organization org = ISUtilImpl.getInstance().getOrg(request.getOrgShortName());
		RFSFXLeg nearLeg = request.getNearLeg();
		User fiUser = org.getUser( request.getUserShortName() );
		tradeRequest.setRequestId(request.getRequestId());
		tradeRequest.setState(TradeRequest.OPEN);
		tradeRequest.setOrgShortName(request.getOrgShortName());
		tradeRequest.setLeShortName(request.getLeShortName());
		tradeRequest.setUserShortName(request.getUserShortName());
		/*	if (.getProviderConfig().isMakerServiceEnabled()){
				tradeRequest.setProperty(ISConstantsC.MAKER_NOTIFICATION_DISABLED, "1");
			}*/
		tradeRequest.setProperty(OAWorkflowFunctorC.TAKER_ORDER_TIMEINFORCE, TimeInForce.FOK);
		tradeRequest.setServerId(request.getServerId());
		tradeRequest.setProviderShortName(request.getProviderShortName());
		tradeRequest.setBaseCcy(request.getBaseCurrency());
		tradeRequest.setVariableCcy(request.getVariableCurrency());
		tradeRequest.setTradeId(request.getRequestId());
		tradeRequest.setOriginatingUser(request.getOriginatingUser());
		tradeRequest.setOriginatingCpty(request.getOriginatingCpty());
		if ( nearLeg.getBidOfferMode() == DealingPrice.OFFER )
		{
			RFSFXRate offerRate = nearLeg.getOfferRate();
			tradeRequest.setAmount(offerRate.getDealtAmount());
			tradeRequest.setProperty("BaseRate", offerRate.getSpotRate());
			tradeRequest.setBuySell(TradeRequest.BUY);
			tradeRequest.setRate(offerRate.getRate());
			tradeRequest.setProperty(FWD_PTS, offerRate.getForwardPoints());

		}
		if ( nearLeg.getBidOfferMode() == DealingPrice.BID )
		{
			RFSFXRate bidRate = nearLeg.getBidRate();
			tradeRequest.setAmount(bidRate.getDealtAmount());
			tradeRequest.setProperty("BaseRate", bidRate.getSpotRate());
			tradeRequest.setBuySell(TradeRequest.SELL);
			tradeRequest.setRate(bidRate.getRate());
			tradeRequest.setProperty(FWD_PTS, bidRate.getForwardPoints());
		}

		tradeRequest.setDealtCcy(nearLeg.getDealtCurrency());
		tradeRequest.setTradeDate(request.getTradeDate());
		tradeRequest.setTradeEntryDate(request.getTradeDate());
		tradeRequest.setValueDate(nearLeg.getValueDate());

		ExternalSystemId esi = fiUser.getExternalSystemId(org.getShortName() + "_UserId");
		String traderExternalId = esi == null ? null : esi.getSystemId();
		if ( traderExternalId != null )
		{
			tradeRequest.setTraderExternalId(traderExternalId);
		}
		Object isPBTrade = request.getProperty(ISConstantsC.isPrimeBrokerCoverTradeEnabled);
		if ( isPBTrade != null )
		{
			Boolean isPrimeBrokerTradeEnabled = (Boolean)isPBTrade;
			tradeRequest.setProperty(ISConstantsC.isPrimeBrokerCoverTradeEnabled,isPrimeBrokerTradeEnabled );
		}
		tradeRequest.setProperty( ISConstantsC.TRADE_TRANSACTIONID, request.getRequestId() );
		tradeRequest.setProperty( OAWorkflowFunctorC.TAKER_ORDERID, request.getRequestId() );
		//This will always be taker request
        tradeRequest.setProperty( OAWorkflowFunctorC.TAKER_ORDER_EFFECTIVETIME, System.currentTimeMillis() );

		tradeRequest.setOrderTxnId(request.getRequestId());
		tradeRequest.setProperty(IS_OUTRIGHT, true);
		tradeRequest.setProperty(FIXING_DATE, nearLeg.getFixingDate());
		tradeRequest.setProperty(FIXING_TENOR, nearLeg.getFixingTenor());
		tradeRequest.setProperty(TENOR, nearLeg.getTenor());
		tradeRequest.setProperty(USI, nearLeg.getUSI());
		
		tradeRequest.setOriginatingDealId(request.getRequestId());
		tradeRequest.setTakerReferenceId(request.getTakerReferenceId());
		tradeRequest.setProviderQuoteId(request.getProviderQuoteId());
		
		if ( log.isDebugEnabled() )
		{
			log.debug("OARFSHandlerC.getTradeRequestFromRFSTradeRequest: " + tradeRequest.toString());
		}
		tradeRequest.getProperties().putAll(request.getSEFParameters());
		/**
		 * Added to process requests spaces way
		 */
		if (ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled()) {
			tradeRequest.setProperty( ISCommonConstants.SPACES_ENABLED, true );
		}
		return tradeRequest;
	}

	
	/**
	   * This method will be invoked by the adaptor framework when a quote is accepted on an RFS
	   * stream
	   *
	   * @param rfsTradeRequest the quote acceptance message
	   * @return the status of the trade request
	   */
	public ResponseMessage quoteAccepted( RFSTradeRequest rfsTradeRequest )
	{			
		if ( rfsTradeRequest.getProviderShortName() != null && !ISFactory.getInstance().getISMBean().isOutrightDisplayOrderEnabled(rfsTradeRequest.getProviderShortName()) )
		{
			log.error("OARFSHandlerC.quoteAccepted:: OA org  " + rfsTradeRequest.getProviderShortName() + " does not support RFS/Outright trade requests");
			ResponseMessage response = MessageFactory.newResponseMessage();
			response.setStatus( ResponseMessage.FAILURE );
	        response.setFailureReason( "RFS Outright trade is not supported by Provider" );
			return response;
		}
		TradeRequest tradeRequest = getTradeRequestFromRFSTradeRequest(rfsTradeRequest);
		
		return orderhandler.quoteAccepted(tradeRequest);
	}

	public ResponseMessage stopRFSSubscription( RFSUnsubscribe message )
	{
		return null;
	}

	public Message handle( Message message ) throws IdcException
	{
		return null;
	}

	public BrokerQuoteCache getBrokerQuoteCache()
	{
		return null;
	}

	public BrokerTradeHandler getBrokerTradeHandler()
	{
		return null;
	}

	public ProviderQuoteCache getProviderQuoteCache()
	{
		return null;
	}

	public RFSRequestFacadeC getRFSSubscriptionFacade()
	{
		return null;
	}

	public RFSUnsubscribe getRFSUnsubscribe()
	{
		return null;
	}

	public void gotTradeResponse( TradeRequestFacade tradeRequestFacade, TradeResponseC tradeResponse )
	{

	}

	public MDSPriceFacade getMdsPriceFacade()
	{
		return null;
	}

	public RFSPublisher getRfsPublisher()
	{
		return null;
	}

	public MDSRFSPublisher getMdsPublisher()
	{
		return null;
	}

	public Product getProduct()
	{
		return null;
	}

	@Override
	public Product getEspProduct(){
		return null;
	}

	@Override
	public void setSwapPointSpreadBuySell(double value){
		throw new UnsupportedOperationException("Not implemented");
	}
	@Override
	public void setSwapPointSpreadSellBuy(double value){
		throw new UnsupportedOperationException("Not implemented");
	}
	@Override
	public void setSwapPointSpreadCust(double value){
		throw new UnsupportedOperationException("Not implemented");
	}

	@Override
	public void setQuotedUser(User quotedUser) {
		throw new UnsupportedOperationException("Not implemented");
	}

	@Override
	public User getQuotedUser() {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public List<String> getWithdrawnQuoteIds() {
		return null;
	}
	
	@Override
	public void setWithdrawnQuoteIds(List<String> withdrawnQuoteIds) {
		throw new UnsupportedOperationException("Not implemented");
	}

	@Override
	public DealerSpread getDealerSpread() {
		return null;
	}
	
	@Override
	public DealerSpread getPPDealerSpread() {
		return null;
	}

	@Override
	public double getReferenceRate() {
		return 0;
	}
    @Override
	public void setMidRate(double midRate,boolean isOverride){

    }
	@Override
	public ConcurrentMap<String, BrokerTradeHandler> getSpotAndSwapBrokerTradeHandlerMap() {
		return null;
	}
	
	@Override
	public RFSSpotAndSwapRiskEmailHandler getRFSSpotAndSwapRiskEmailHandler() {
		return null;
	}

	public boolean isAtBestOrderRFS()
	{
		return false;
	}

	public boolean isRfqTrade(){
		return false;
	}

	public boolean isSyntheticRFSForward() {
		return false;
	}

	public RFSSubscriber getSubscriber(){
		return null;
	}

	public RFSHandler getSyntheticHandler(){
		return null;
	}

	public TradeRequestFacade getTradeRequestFacade() {
		return null;
	}
}
