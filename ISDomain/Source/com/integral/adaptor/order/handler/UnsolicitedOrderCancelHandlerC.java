package com.integral.adaptor.order.handler;

import com.integral.finance.dealing.Request;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.HandlerCacheC;
import com.integral.is.common.cache.UserCacheHandlerC;
import com.integral.is.common.client.handler.datamessage.ISDataMessage;
import com.integral.is.common.client.handler.datamessage.ISDataMessageFactoryC;
import com.integral.is.common.client.handler.datamessage.ISResponseMessageCompactionFacade;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.oms.OMSUtilC;
import com.integral.is.system.notification.NotificationMessageSender;
import com.integral.jmsx.JMSManager;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageFactory;
import com.integral.message.WorkflowMessage;
import com.integral.message.WorkflowMessageBatch;
import com.integral.message.WorkflowMessageBatchC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.user.User;

import javax.jms.DeliveryMode;
import javax.jms.Destination;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Unsolicited order cancel handler.
 */
public class UnsolicitedOrderCancelHandlerC
{
	private Log log = LogFactory.getLog(this.getClass());

	private static UnsolicitedOrderCancelHandlerC _instance = new UnsolicitedOrderCancelHandlerC();

	public UnsolicitedOrderCancelHandlerC()
	{
	}

	public static UnsolicitedOrderCancelHandlerC getInstance()
	{
		return _instance;
	}

	public void cancelOrders()
	{
		UserCacheHandlerC userCache = UserCacheHandlerC.getUserCacheHandler();
		Map<String, List<Request>> userDetails = userCache.getUserDetailsCache();
		if ( userDetails != null && !userDetails.isEmpty() )
		{
			for ( String key : userDetails.keySet() )
			{
				Collection<Request> requestList = userDetails.get(key);
				for ( Request orderRequest : requestList )
				{
					try
					{
						if ( !OMSUtilC.isOrder(orderRequest) )
						{
							continue;
						}

						orderRequest.getRequestAttributes().setIsUnsolicitedCancel(Boolean.TRUE);
						WorkflowMessage wfMsg = MessageFactory.newWorkflowMessage();
						wfMsg.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
						wfMsg.setEvent(ISConstantsC.MSG_EVT_WITHDRAW);
						wfMsg.setObject(orderRequest);
						ServiceFactory.getOrderRequestService().process(wfMsg);
						WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();

						sendNotificationForChiefDealer(orderRequest.getUser(), replyMessage);

						WorkflowMessageBatch wfBatch = new WorkflowMessageBatchC();
						wfBatch.addMessage(replyMessage);
						ISResponseMessageCompactionFacade facade = new ISResponseMessageCompactionFacade();
						ISDataMessage dataMessage = ISDataMessageFactoryC.getInstance().getISDataMessage(ISDataMessage.RESPONSE_TYPE_ESP);
						String strMessage = (String) dataMessage.populateData(wfBatch, facade);
						Map jmsProps = dataMessage.getJMSProps(replyMessage);
						JMSManager.getInstance().sendMessage(ISFactory.getInstance().getISMBean().getBroker(), getQueue(orderRequest.getUser()), strMessage, jmsProps, DeliveryMode.NON_PERSISTENT, javax.jms.Message.DEFAULT_PRIORITY, javax.jms.Message.DEFAULT_TIME_TO_LIVE);
					}
					catch ( Exception ex )
					{
						log.error("UnsolicitedOrderCancelHandlerC.cancelOrders : Error in cancelling order " + ex);
					}
				}
			}
		}
	}

	public boolean cancelOrder( String orderId )
	{
		try
		{
			HandlerCacheC requestHandlerCache = HandlerCacheC.getHandlerCache();
			Request orderRequest = requestHandlerCache.getRequest(orderId);
			if ( orderRequest == null )
			{
				log.warn("UnsolicitedOrderCancelHandlerC.cancelOrder : Could not cancel the order for orderId " + orderId + " since orderRequest is null");
				return false;
			}
			IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext();
			if ( ctx == null )
			{
				ctx = IdcSessionManager.getInstance().getSessionContext(orderRequest.getUser());
				IdcSessionManager.getInstance().setSessionContext(ctx);
			}
			orderRequest.getRequestAttributes().setIsUnsolicitedCancel(Boolean.TRUE);
			WorkflowMessage wfMsg = MessageFactory.newWorkflowMessage();
			wfMsg.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
			wfMsg.setEvent(ISConstantsC.MSG_EVT_WITHDRAW);
			wfMsg.setObject(orderRequest);
			ServiceFactory.getOrderRequestService().process(wfMsg);
			WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();

			sendNotificationForChiefDealer(orderRequest.getUser(), replyMessage);

			WorkflowMessageBatch wfBatch = new WorkflowMessageBatchC();
			wfBatch.addMessage(replyMessage);
			ISResponseMessageCompactionFacade facade = new ISResponseMessageCompactionFacade();
			ISDataMessage dataMessage = ISDataMessageFactoryC.getInstance().getISDataMessage(ISDataMessage.RESPONSE_TYPE_ESP);
			String strMessage = (String) dataMessage.populateData(wfBatch, facade);
			Map jmsProps = dataMessage.getJMSProps(replyMessage);
			JMSManager.getInstance().sendMessage(ISFactory.getInstance().getISMBean().getBroker(), getQueue(orderRequest.getUser()), strMessage, jmsProps, DeliveryMode.NON_PERSISTENT, javax.jms.Message.DEFAULT_PRIORITY, javax.jms.Message.DEFAULT_TIME_TO_LIVE);
		}
		catch ( Exception ex )
		{
			log.error("UnsolicitedOrderCancelHandlerC.cancelOrder : Error in cancelling order " + ex);
			return false;
		}
		return true;
	}

	/**
	 * Send notification to chiefdealer user
	 *
	 * @param user    user
	 * @param message message
	 */
	private void sendNotificationForChiefDealer( User user, Message message )
	{
		try
		{
			WorkflowMessageBatch batchMsg;
			if ( message instanceof WorkflowMessage )
			{
				batchMsg = MessageFactory.newWorkflowMessageBatch();
				batchMsg.addWorkflowMessage((WorkflowMessage) message);
			}
			else
			{
				batchMsg = (WorkflowMessageBatch) message;
			}

			ISResponseMessageCompactionFacade facade = new ISResponseMessageCompactionFacade();
			ISDataMessage dataMessage = ISDataMessageFactoryC.getInstance().getISDataMessage(ISDataMessage.RESPONSE_TYPE_ESP);
			String messageContent = (String) dataMessage.populateData(batchMsg, facade);
			Map<String, String> jmsProps = dataMessage.getJMSProps(null);
			NotificationMessageSender.sendMessageToUserPermission(messageContent, jmsProps, user, ISConstantsC.CHIEFDEALER_MPVIEW_PERM);

		}
		catch ( Exception e )
		{
			log.error("UnsolicitedOrderCancelHandlerC.sendNotificationForChiefDealer::Error sending notification " + e);
		}
	}

	/**
	 * Destination from User Session context.
	 * Take it everytime from session and do not store in local variable.
	 * Reconnect from the user may change the destination.
	 *
	 * @param user user
	 * @return destination
	 */
	protected Destination getQueue( User user )
	{
		return (Destination) (IdcSessionManager.getInstance().getSessionContext(user)).getAttribute("Destination");
	}
}
