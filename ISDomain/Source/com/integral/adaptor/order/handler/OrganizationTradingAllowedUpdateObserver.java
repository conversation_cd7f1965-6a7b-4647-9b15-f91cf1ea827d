package com.integral.adaptor.order.handler;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.admin.AdminServiceC;
import com.integral.exception.IdcException;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.oms.OrderRequestServiceC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.NamedEntity;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Map;

/**
 * This observer is invoked when any organization's TradingAllowed attribute is modified
 */
public class OrganizationTradingAllowedUpdateObserver implements MessageHandler
{
	private Log log = LogFactory.getLog(this.getClass());

	public Message handle( Message message ) throws IdcException
	{
		Map props = message.getMap();
		String organizationName = (String) props.get(NamedEntity.ShortName);
		String notificationType = (String) props.get(AdminServiceC.ORGANIZATION_MODIFICATION_FUNCTOR_KEY_NOTIFICATIONTYPE);

		if ( notificationType.equals(AdminServiceC.TRADING_ALLOWED_UPDATE_ACTION) )
		{
			log.info("OrganizationTradingAllowedUpdateObserver.handle Notification Type:" + notificationType + ", org  " + organizationName);
			Organization org = ISUtilImpl.getInstance().getOrg(organizationName);
			if ( !org.isTradingAllowed() )
			{
				Collection<Organization> orderProviderList = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
				if ( orderProviderList.contains(org) )
				{
					log.info("OrganizationTradingAllowedUpdateObserver.handle Invoking OrderRequestServiceC for   org  " + organizationName);
					OrderRequestServiceC ors = (OrderRequestServiceC) ServiceFactory.getOrderRequestService();
					final WorkflowMessage wfm = MessageFactory.newWorkflowMessage();
					wfm.setEvent(ISConstantsC.MSG_EVT_WITHDRAWALL);
					wfm.setTopic(ISConstantsC.MSG_TOPIC_REQUEST);
					wfm.setObject(org);

					WorkflowMessage responseMessage = ors.process(wfm);
					Message out = responseMessage.getReplyMessage();
					if ( out == null )
					{
						out = responseMessage;
					}
					return out;
				}
			}
		}
		return message;
	}
}
