package com.integral.adaptor.order.handler;

//Copyright (c) 2005 Integral Development Corp. All rights reserved.

import com.integral.adaptor.request.OrderHandler;
import com.integral.adaptor.request.RFSHandler;
import com.integral.adaptor.request.RequestHandlerFactory;
import com.integral.adaptor.request.SubscriptionHandler;

/**
 * RequestHandlerFactory for OA trades.
 */
public class OAHandlerFactory extends RequestHandlerFactory
{

	private static OAHandlerFactory current = new OAHandlerFactory();
	private SubscriptionHandler subscriptionHandler;
	private OrderHandler orderHandler;
	private RFSHandler rfsHandler;

	private OAHandlerFactory()
	{
		try
		{
			orderHandler = new OrderhandlerC();
			subscriptionHandler = new SubscriptionHandlerC();
			rfsHandler = new OARFSHandlerC(orderHandler);
		}
		catch ( Exception e )
		{
			e.printStackTrace();
		}
	}

	public static OAHandlerFactory getInstance()
	{
		return current;
	}

	public SubscriptionHandler getSubscriptionHandler()
	{
		return subscriptionHandler;
	}

	public OrderHandler getOrderHandler()
	{
		return orderHandler;
	}
	
	public RFSHandler getRFSHandler()
	{
		return rfsHandler;
	}
}
