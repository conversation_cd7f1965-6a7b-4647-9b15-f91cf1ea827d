package com.integral.adaptor.order;

import com.integral.adaptor.AdaptorConstantC;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.adaptor.order.handler.*;
import com.integral.adaptor.order.management.ServerRuntimeMBeanNotificationListener;
import com.integral.adaptor.order.subscription.OutrightDisplayOrderSubscription;
import com.integral.adaptor.order.warmup.OAWarmupWorker;
import com.integral.adaptor.request.RequestHandlerFactory;
import com.integral.adaptor.request.RequestHandlerFactoryRegistry;
import com.integral.adaptor.request.RequestServiceC;
import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.LocalBrokerAdaptorFactory;
import com.integral.broker.cache.EMSQuoteCacheFactory;
import com.integral.ems.EMSServerFactory;
import com.integral.ems.LGPriceBookHandler;
import com.integral.ems.LGPriceBookPriceElementHandler;
import com.integral.ems.engine.EMSEngineConfiguration;
import com.integral.ems.engine.EMSServer;
import com.integral.lp.provision.PMPriceControChangeListener;
import com.integral.marketmaker.config.ClientChannelConfigCache;
import com.integral.broker.BrokerAdaptorStartupC;
import com.integral.facade.FacadeFactory;
import com.integral.finance.businessCenter.EndOfDayService;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.Provider;
import com.integral.is.common.ProviderManagerObserver;
import com.integral.is.common.liquidityProvision.functor.LiquidityProvisionRemoteNotificationFunctor;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.functor.CounterPartyRemoteTransactionFunctor;
import com.integral.is.functor.OrganizationMasterControlRemoteTransactionFunctor;
import com.integral.is.functor.OrganizationModificationRemoteTransactionFunctor;
import com.integral.is.functor.UserRemoteTransactionFunctor;
import com.integral.is.oms.*;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.oms.descriptors.MatchingCriteriaFactory;
import com.integral.is.oms.event.EventManagers;
import com.integral.is.oms.scheduler.DayOrderExpirationHandlerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.lp.QuoteDistributionInfo;
import com.integral.message.MessageHandler;
import com.integral.oms.spaces.fx.esp.FXOrderBuilder;
import com.integral.oms.spaces.fx.esp.descriptor.FXMatchingCriteriaBuilder;
import com.integral.oms.spaces.fx.esp.netting.FXOrderNettingServiceC;
import com.integral.oms.spaces.fx.esp.service.FXESPOrderRequestService;
import com.integral.oms.spaces.fx.esp.service.FXESPOrderResponseService;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.StartupTask;
import com.integral.takerorg.TakerOrgProvisionControlObserver;
import com.integral.takerorg.service.TakerOrgfactory;
import com.integral.transaction.remote.GenericRemoteNotificationFunctor;
import com.integral.uig.LGPriceDistributionManager;
import com.integral.uig.listener.PriceBookMulticastListener;
import com.integral.user.Organization;

import javax.management.AttributeChangeNotificationFilter;
import javax.management.InstanceAlreadyExistsException;
import javax.management.ObjectName;

import java.util.*;

public class OrderAdaptorStartupC implements StartupTask
{

	private static final Log LOG = LogFactory.getLog(OrderAdaptorStartupC.class);

	public OrderAdaptorStartupC()
	{
		super();
	}

	public String startup( String aName, Hashtable htArgs )
	{

		QuoteDistributionInfo.addObserver(new OMSQuoteDistributionInfoObserver());
		RequestServiceC.getRequestService();
		createOrderServices();
		if ( ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() )
		{
			OrderFactory.setBuilder(new FXOrderBuilder());
			MatchingCriteriaFactory.setBuilder(new FXMatchingCriteriaBuilder());
		}
		registerFacades();
		intializeRequestService();
		setupProviderManagerObserver();
		registerCalculators();
		registerOrderConfigurationMBean();
		registerDayOrderExpirationHandler();
		registerMarketMakerNotification();
        registerMarketMakerPriceControlNotification();
		OrganizationModificationRemoteTransactionFunctor.addObserver(new BrokerOrganizationModificationObserver());
		OrganizationModificationRemoteTransactionFunctor.addObserver(new OrganizationVirtualServerUpdateHandlerC());
		OrganizationModificationRemoteTransactionFunctor.addObserver(new OrganizationTradingAllowedUpdateObserver());
		OrganizationModificationRemoteTransactionFunctor.addObserver(new OrganizationStatusChangeObserver());
		OrganizationModificationRemoteTransactionFunctor.addObserver(new PrimeBrokerForMVModificationObserver());
		CounterPartyRemoteTransactionFunctor.addObserver(new LegalEntityStatusChangeObserver());
		UserRemoteTransactionFunctor.addObserver(new UserStatusChangeObserver());
		OrderProviderProvisionedNotificationHandler handler = new OrderProviderProvisionedNotificationHandler();
		GenericRemoteNotificationFunctor.addHandler(handler, GenericRemoteNotificationFunctor.EVENT_ADD_PROVIDER);
		GenericRemoteNotificationFunctor.addHandler(handler, GenericRemoteNotificationFunctor.EVENT_PROVISION_PROVIDER);
		OrganizationMasterControlRemoteTransactionFunctor.addObserver(new OrganizationMasterControlChangeObserver());		
		OrganizationMasterControlRemoteTransactionFunctor.addObserver(new BrokerPriceMakingObserver());
		LiquidityProvisionChangeObserver liquidityProvisionChangeObserver = new LiquidityProvisionChangeObserver();
		TakerOrgProvisionControlObserver takerOrgControlObserver = new TakerOrgProvisionControlObserver();
		Set<Organization> brokerOrgs = new HashSet<Organization>(10);
		for (Organization i : OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList())
		{
			String orgName = i.getShortName();
			LiquidityProvisionRemoteNotificationFunctor.addObserver(orgName, liquidityProvisionChangeObserver);						
			TakerOrgfactory.getInstance().getTakerProvisionService().registerForObserver(orgName, takerOrgControlObserver);
			Organization brokerOrg = i.getBrokerOrganization();
			if(brokerOrg!=null){
				brokerOrgs.add(brokerOrg);
			}
		}
		EventManagers.getInstance().setup();
		registerServerRuntimeNotification();
        OutrightDisplayOrderSubscription.subscribe();
		//MakerPortalMessagesListener.start();
		if(OrderConfiguration.getInstance().isLocalV4Enabled()){
			try {
					LocalBrokerAdaptorFactory.init();
			}catch (Exception e){
					LOG.error("Exception in startup isLocalV4Enabled: " + aName);
			}
			registerV4LiquidityGroups(brokerOrgs);
		}
		if ( ISFactory.getInstance ().getISMBean ().isOAWarmupWorkerEnabled () )
		{
			new Thread ( new OAWarmupWorker (), "OAWarmupWorker" ).start ();
		}
		else
		{
			LOG.info ( "OAS.startup - skipping OA warmup worker." );
		}
		return null;
	}

	private void createOrderServices()
	{
		OrderAdaptorFactory.getInstance().setMatchingRules(new MatchingRules());
		ServiceFactory.setOrderRequestService(new FXESPOrderRequestService());
		OrderAdaptorFactory.setOrderResponseService(new FXESPOrderResponseService());
		com.integral.is.spaces.fx.service.ServiceFactory.setNettingService(new FXOrderNettingServiceC());
	}


	private void registerV4LiquidityGroups(Set<Organization> brokerOrgs){
		try {
			for(Organization brokerOrg:brokerOrgs){
				LocalBrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(brokerOrg.getShortName());
			}
			LOG.info("MDF priceBook Listening property is Enabled");
			PriceBookMulticastListener.getInstance().init(LGPriceDistributionManager.getInstance());
			LGPriceDistributionManager.getInstance().registerPriceDistributionLine(brokerOrgs);
			LGPriceDistributionManager.getInstance().addHandler(LGPriceBookPriceElementHandler.getInstance());


		} catch (Exception e) {
			LOG.error("Exception in registerV4LiquidityGroups " +brokerOrgs,e );
		}
	}

	/**
	 * Register <code>OrderConfigurationMBean</code> with the MBean server so that it is accessible through the JMX
	 * console.
	 *
	 * @see OrderConfigurationMBean
	 */
	private void registerOrderConfigurationMBean()
	{
		final OrderConfigurationMBean orderConfigurationMBean = OrderConfigurationFactory.getOrderConfigurationMBean();
		try
		{
			final StringBuilder name = new StringBuilder(60);
			name.append("OrderAdaptor:Type=OrderConfiguration,Name=OrderConfiguration");
			ConfigurationFactory.getMBeanServer().registerMBean(orderConfigurationMBean, ObjectName.getInstance(name.toString()));
		}
		catch ( InstanceAlreadyExistsException e )
		{
			//Ignore
			LOG.debug("OAS.registerMBean: - InstanceAlreadyExists - This attempt ignored: " + e.toString(), e);
		}
		catch ( Exception e )
		{
			LOG.error("OAS.registerMBean: Error while registering the MBean: " + e.toString(), e);
		}
	}

	private void registerFacades()
	{
		FacadeFactory.setFacade(Order.FACADE_NAME, DealingPrice.class, OrderC.class);
		//FacadeFactory.setFacade( Orders.FACADE_NAME, Quote.class, Orders.class );
		BrokerAdaptorStartupC.registerFacades();
	}

	private void registerCalculators()
	{
		OrderCalculatorFactory.getInstance();
		BrokerAdaptorStartupC.registerCalculators();
	}

	/**
	 * Registers day order expiration handler with EndOfDayService to support expiring day orders.
	 *
	 * @see DayOrderExpirationHandlerC
	 * @see EndOfDayService#addEndOfDayHandler(MessageHandler)
	 */
	private void registerDayOrderExpirationHandler()
	{
		final DayOrderExpirationHandlerC dayOrderExpirationHandler = new DayOrderExpirationHandlerC();
		final EndOfDayService endOfDayService = EndOfDayServiceFactory.getEndOfDayService();
		endOfDayService.addEndOfDayHandler(dayOrderExpirationHandler);
	}

	private void intializeRequestService()
	{
		try
		{
			Collection<Organization> makers = OrderConfigurationFactory.getOrderConfigurationMBean().getExtendedOrderProviderOrgsList();
			for ( Organization org : makers )
			{
				RequestHandlerFactoryRegistry.register(ISCommonConstants.PROVIDER_TYPE_OA, org.getShortName(), OAHandlerFactory.getInstance());

				// For backward compatibility
				RequestHandlerFactoryRegistry.register(org.getShortName(), OAHandlerFactory.getInstance());
			}
			RequestHandlerFactoryRegistry.register(AdaptorConstantC.OMS_PROVIDER, OAHandlerFactory.getInstance());
			RequestHandlerFactoryRegistry.register(RequestHandlerFactory.DEFAULT_FACTORY_NAME, OAHandlerFactory.getInstance());
		}
		catch ( Exception ex )
		{
			LOG.error("OAS.initializeRequestService : Error in create request service instance " + ex);
		}
	}

	private void setupProviderManagerObserver()
	{
		ProviderManagerC pMan = ProviderManagerC.getInstance();
		ProviderManagerObserver pmo = new OMSProviderManagerObserverC();
		pMan.addObserver(pmo);
		LOG.warn("OrderAdaptorStartupC.setupProviderManagerObserver::Set ProviderManagerObserver on startup");

		Iterator iter = ProviderManagerC.getInstance().getProviderMap().values().iterator();
		while ( iter.hasNext() )
		{
			Object obj = iter.next();
			if ( obj instanceof Provider )
			{
				Provider provider = (Provider) obj;
				provider.setOARateListener(new ProviderRateListener());
				provider.addProviderStatusObserver(new OMSProviderStatusObserverC(provider.getName()));
				LOG.warn("OAS.setupProviderManagerObserver:Set OrderMessageListener and OMSProviderStatusObserver for " + provider.getName());
			}
		}
	}

	protected void registerServerRuntimeNotification()
	{
		AttributeChangeNotificationFilter filter = new AttributeChangeNotificationFilter();
		filter.enableAttribute("tradesEnabled");
		RuntimeFactory.getServerRuntimeMBean().addNotificationListener(new ServerRuntimeMBeanNotificationListener(), filter, null);
	}

	private void registerMarketMakerNotification()
	{
		ClientChannelConfigCache.getInstance().initialize();
		LOG.info("OAS.registerMarketMakerNotification:Registered for Market Maker Notification");
	}

    private void registerMarketMakerPriceControlNotification()
    {
        PMPriceControChangeListener.getInstance().initialize();
        LOG.info("OAS.registerMarketMakerPriceControlNotification:Registered for Market Maker Price Control Notification");
    }

}
