package com.integral.adaptor.order;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.scheduler.ScheduleFunctorC;

/**
 * Created by clusind on 12/2/2014.
 */
public class OrderBroadcasterFilterReferenceUpdaterC extends ScheduleFunctorC {

    protected final Log log = LogFactory.getLog(this.getClass());

    @Override
    public void execute(WorkflowMessage msg) {
        log.info("execute: Clearing FilterReferenceBand caches");
        OrderBroadcaster.cachedFilterReferenceBands.clear();
    }

    @Override
    public String getDescription() {
        return this.getClass().getSimpleName();
    }
}
