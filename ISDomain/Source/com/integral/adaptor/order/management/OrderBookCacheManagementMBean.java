package com.integral.adaptor.order.management;

/**
 * <AUTHOR> Development Corp.
 */
public interface OrderBookCacheManagementMBean
{

	public int getOrderBooksCount();

	/**
	 * Display all the Orders from OrderBook
	 *
	 * @return
	 */
	public String listOrderBookNames();

	/**
	 * Cancels all maker Orders
	 */
	public void cancelAllMakerOrders();

	/**
	 * Cancels provider quotes in all Order Books
	 *
	 * @param providerName
	 */
	public void cancelProviderQuotes( String providerName );

	public void suspendMatching();

	public void resumeMatching();
}
