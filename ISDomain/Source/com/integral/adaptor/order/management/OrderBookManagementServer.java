/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.management;

import java.util.ArrayList;
import java.util.Collection;

import javax.servlet.http.HttpServletRequest;

import org.json.JSONArray;
import org.json.JSONObject;

import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCacheC;

/**
 * <AUTHOR>
 *
 */
public class OrderBookManagementServer
{

	public static String processCommand( HttpServletRequest request )
	{
		String command = request.getParameter("cmd");
		if ( command != null && !"".equals(command) )
		{
			if ( "orderBooksList".equalsIgnoreCase(command) )
			{
				return getOrderBookList2();
			}
			else if ( "evictOrder".equalsIgnoreCase(command) )
			{
				return getOrderBookList();
			}
			else if ( "ordersSnapshot".equalsIgnoreCase(command) )
			{
				return getOrderBookList();
			}
			else if ( "orderDetails".equalsIgnoreCase(command) )
			{
				return getOrderBookList();
			}
		}
		return null;
	}

	/**
	 * 
	 * @return JSON object
	 */
	static String getOrderBookList()
	{
		try
		{
			JSONObject generator = new JSONObject();
			generator.put("command", "orderBooksList");
			JSONArray books = new JSONArray(OrderBookCacheC.getInstance().getOrderBooks().keySet());
			generator.put("books", books);
			return generator.toString();
		}
		catch ( Exception e )
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	/**
	* 
	* @return JSON object
	*/
	static String getOrderBookList2()
	{
		try
		{
			JSONObject generator = new JSONObject();
			generator.put("command", "orderBooksList");
			Collection<JSONArray> bookss = new ArrayList<JSONArray>();
			Collection<OrderBook> bookids = OrderBookCacheC.getInstance().getOrderBooks().values();
			int i = 1;
			for ( OrderBook book : bookids )
			{
				JSONArray bookjson = new JSONArray();
				bookjson.put(i++);
				bookjson.put(book.getBookName());
				bookjson.put(book.getOrganization());
				bookjson.put("idx=" + book.getIndex());
				bookss.add(bookjson);
			}
			JSONArray books = new JSONArray(bookss);
			generator.put("books", books);
			return generator.toString();
		}
		catch ( Exception e )
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;
	}

	static String getOrdersSnapshot( String orderBook )
	{
		try
		{
			OrderBook book = OrderBookCacheC.getInstance().getOrderBook(orderBook, null, null);
			JSONObject generator = new JSONObject();
			generator.put("command", "ordersSnapshot");
			generator.put("bidOrders", getOrderIds(book.getSortedBidOrders()));
			generator.put("offerOrders", getOrderIds(book.getSortedOfferOrders()));
			return generator.toString();
		}
		catch ( Exception e )
		{
			// TODO: handle exception
		}
		return null;
	}

	static JSONArray getOrderIds( Collection<Order> orders )
	{
		JSONArray Ids = new JSONArray();
		for ( Order order : orders )
		{
			Ids.put(order.getOrderId());
		}
		return Ids;
	}

}
