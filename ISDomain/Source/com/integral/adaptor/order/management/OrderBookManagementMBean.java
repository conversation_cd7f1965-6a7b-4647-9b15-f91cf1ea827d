package com.integral.adaptor.order.management;

/**
 * <AUTHOR> Development Corp
 */
public interface OrderBookManagementMBean
{

	public int getMakerBidOrders();

	public int getMakerOfferOrders();

	public int getBidQuotes();

	public int getOfferQuotes();

	/**
	 * Displays all the orders in the order book
	 *
	 * @return
	 */
	public String displayOrders();

	/**
	 * Cancels the given OrderId
	 *
	 * @param orderID
	 * @return
	 */
	public boolean cancelOrderID( String orderID );

	/**
	 * Cancels the provider quotes
	 *
	 * @param providerName
	 * @return
	 */
	public void cancelProviderQuotes( String providerName );

	/**
	 * Displays all the maker Orders
	 *
	 * @return
	 */
	public String displayMakerOrders();

	/**
	 * Displays all MakerBidOrders
	 *
	 * @return
	 */
	public String displayMakerBidOrders();

	/**
	 * Display all MakerOfferOrders
	 *
	 * @return
	 */
	public String displayMakerOfferOrders();

	/**
	 * Display all the provider Quotes
	 *
	 * @return
	 */
	public String displayProviderQuotes();

	/**
	 * Displays all Bid side provider quotes
	 *
	 * @return
	 */
	public String displayProviderBidQuotes();

	/**
	 * Displays all the Offer side provider quotes
	 *
	 * @return
	 */
	public String displayProviderOfferQuotes();

	public void suspendMatching();

	public void resumeMatching();

}