package com.integral.adaptor.order.management;

import com.integral.adaptor.order.handler.UnsolicitedOrderCancelHandlerC;
import com.integral.adaptor.order.handler.UnsolicitedOrderCancelHandlerCS;
import com.integral.finance.dealing.Quote;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookC;
import com.integral.is.oms.Orders;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.staging.oms.passorder.OrderCancelledBy;
import com.integral.system.configuration.ConfigurationFactory;

import java.util.ArrayList;
import java.util.Collection;

/**
 * <AUTHOR> Development Corp
 */
public class OrderBookManagement implements OrderBookManagementMBean, Metrics
{

	OrderBookC book;
	private Log log = LogFactory.getLog(this.getClass());

	public OrderBookManagement( OrderBook book )
	{
		this.book = (OrderBookC) book;
	}

	public int getMakerBidOrders()
	{
		return this.book.getBidOrders().size();
	}

	public int getMakerOfferOrders()
	{
		return this.book.getOfferOrders().size();
	}

	public int getBidQuotes()
	{
		return this.book.getBidQuotes().size();
	}

	public int getOfferQuotes()
	{
		return this.book.getOfferQuotes().size();
	}

	public String displayMakerBidOrders()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append("BID Orders(Sorted)").append('\n');
		Collection<Order> Orders = new ArrayList<Order>(book.getSortedBidOrders());
		for ( Order order : Orders )
		{
			sb.append(order).append('\n');
		}
		return sb.toString();
	}

	public String displayMakerOfferOrders()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append("OFFER Orders(Sorted)").append('\n');
		Collection<Order> Orders = new ArrayList<Order>(book.getSortedOfferOrders());
		for ( Order order : Orders )
		{
			sb.append(order).append('\n');
		}
		return sb.toString();
	}

	public String displayMakerOrders()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append(displayMakerBidOrders());
		sb.append(displayMakerOfferOrders());
		return sb.toString();
	}

	public String displayProviderBidQuotes()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append("BID Quotes(Sorted)").append('\n');
		Collection<Order> Orders = new ArrayList<Order>(book.getSortedBidQuotes());
		for ( Order order : Orders )
		{
			sb.append(order).append('\n');
		}
		return sb.toString();
	}

	public String displayProviderOfferQuotes()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append("Offer Quotes(Sorted)").append('\n');
		Collection<Order> Orders = new ArrayList<Order>(book.getSortedOfferQuotes());
		for ( Order order : Orders )
		{
			sb.append(order).append('\n');
		}
		return sb.toString();
	}

	public String displayProviderQuotes()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append(displayProviderBidQuotes());
		sb.append(displayProviderOfferQuotes());
		return sb.toString();
	}

	public boolean cancelOrderID( String orderID )
	{
		if ( book.lookupOrder(orderID, true) == null && book.lookupOrder(orderID, false) == null )
		{
			return false;
		}
		return ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() ? new UnsolicitedOrderCancelHandlerCS().cancelOrder(orderID, OrderCancelledBy.OTHER) : new UnsolicitedOrderCancelHandlerC().cancelOrder(orderID);
	}

	public String displayOrders()
	{
		StringBuilder sb = new StringBuilder(100);
		sb.append("BID Orders(Unsorted)").append('\n');
		Collection<Order> Orders = new ArrayList<Order>(book.getAllBidOrders());
		for ( Order order : Orders )
		{
			sb.append(order).append('\n');
		}
		sb.append("OFFER Orders(Unsorted)").append('\n');
		Orders = new ArrayList<Order>(book.getAllOfferOrders());
		for ( Order order : Orders )
		{
			sb.append(order).append('\n');
		}
		return sb.toString();
	}

	public void cancelProviderQuotes( String providerName )
	{
        Orders orders = book.cancelAll(providerName);
        if (orders != null) {
            orders.getQuote().decReference(Quote.ReferenceHolder.OrderBook);
        }
	}

	public void suspendMatching()
	{
		book.suspendMatching();
	}

	public void resumeMatching()
	{
		book.resumeMatching();
	}

	/**
	 * Registers the MBean
	 */
	private void registerMBean()
	{
		try
		{
			MetricsManager.instance().register(this);
		}
		catch ( Exception e )
		{
			log.warn("OrderBookManagement.registerMBean: Error while registering with metrics manager -", e);
		}

	}

	/* (non-Javadoc)
	 * @see com.integral.pipeline.metrics.Metrics#report()
	 */
	public StringBuilder report()
	{
		StringBuilder buff = new StringBuilder(200);
		buff.append("OB n=").append(book.getBookName());
		buff.append(",bo=").append(book.getSortedBidOrders().size());
		buff.append(",oo=").append(book.getSortedOfferOrders().size());
		buff.append(",blo=").append(book.getBidLiftOrders().size());
		buff.append(",olo=").append(book.getOfferLiftOrders().size());
		buff.append(",s=").append(book.counters.submitted);
		buff.append(",c=").append(book.counters.cancelled);
		buff.append(",e=").append(book.counters.executions);
		buff.append(",fe=").append(book.counters.failedexecutions);
		book.counters.submitted = 0;
		book.counters.cancelled = 0;
		book.counters.executions = 0;
		book.counters.failedexecutions = 0;
		return buff;
	}

}
