package com.integral.adaptor.order.management;

import com.integral.adaptor.order.handler.UnsolicitedOrderCancelHandlerC;
import com.integral.finance.dealing.Quote;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCache;
import com.integral.is.oms.Orders;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.system.configuration.ConfigurationFactory;

import javax.management.InstanceAlreadyExistsException;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;

/**
 * <AUTHOR> Development Corp
 */
public class OrderBookCacheManagement implements OrderBookCacheManagementMBean, Metrics
{

	OrderBookCache orderBookCache;
	private Log log = LogFactory.getLog(this.getClass());

	/**
	 * @param orderBookCache order book cache
	 */
	public OrderBookCacheManagement( OrderBookCache orderBookCache )
	{
		this.orderBookCache = orderBookCache;
		registerMBean();
	}

	public int getOrderBooksCount()
	{
		return this.orderBookCache.getOrderBooks().size();
	}

	/* (non-Javadoc)
	  * @see com.integral.is.oms.OrderBookCacheManagementMBean#displayAllOrders()
	  */
	public String listOrderBookNames()
	{
		StringBuilder sb = new StringBuilder(100);
		Collection<String> orderBooks = new ArrayList<String>(orderBookCache.getOrderBooks().keySet());
		Iterator<String> itr = orderBooks.iterator();
		while ( itr.hasNext() )
		{
			String book = itr.next();
			sb.append(book).append('\n');
		}
		return sb.toString();
	}

	/* (non-Javadoc)
	  * @see com.integral.is.oms.OrderBookCacheManagementMBean#cancelAllMakerOrders()
	  */
	public void cancelAllMakerOrders()
	{
		new UnsolicitedOrderCancelHandlerC().cancelOrders();
	}

	/* (non-Javadoc)
	  * @see com.integral.is.oms.OrderBookCacheManagementMBean#cancelProviderQuotes(java.lang.String)
	  */
	public void cancelProviderQuotes( String providerName )
	{
		Collection<OrderBook> orderBooks = new ArrayList<OrderBook>(orderBookCache.getOrderBooks().values());
		Iterator<OrderBook> itr = orderBooks.iterator();
		while ( itr.hasNext() )
		{
			OrderBook book = itr.next();
            Orders orders = book.cancelAll(providerName);
            if (orders != null) {
                orders.getQuote().decReference(Quote.ReferenceHolder.OrderBook);
            }
		}
	}

	public void suspendMatching()
	{
		orderBookCache.suspendMatching();
	}

	public void resumeMatching()
	{
		orderBookCache.resumeMatching();
	}

	/**
	 * Registers the MBean
	 */
	private void registerMBean()
	{
		MBeanServer mbeanServer = ConfigurationFactory.getMBeanServer();
		try
		{
			StringBuilder name = new StringBuilder(20);
			name.append("OrderAdaptor:Type=OrderBookCache,Name=OrderBookCache");
			if ( mbeanServer != null )
			{
				mbeanServer.registerMBean(this, ObjectName.getInstance(name.toString()));
			}
		}
		catch ( InstanceAlreadyExistsException e1 )
		{
			//Ignore
			log.debug("OrderBookCacheManagement.registerMBean: - InstanceAlreadyExists - This attempt ignored");
		}
		catch ( Exception e )
		{
			log.error("OrderBookCacheManagement.registerMBean: Error while registering the OrderBookCache MBean.", e);
		}

		MetricsManager.instance().register(this);

	}

	/* (non-Javadoc)
	 * @see com.integral.pipeline.metrics.Metrics#report()
	 */
	public StringBuilder report()
	{
		return new StringBuilder("OBC n=").append(orderBookCache.getOrderBooks().size());
	}

}
