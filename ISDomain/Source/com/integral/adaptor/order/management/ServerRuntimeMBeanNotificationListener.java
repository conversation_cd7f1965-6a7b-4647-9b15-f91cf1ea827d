/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.management;

import javax.management.AttributeChangeNotification;
import javax.management.Notification;
import javax.management.NotificationListener;

import com.integral.adaptor.order.OrderBroadcaster;
import com.integral.is.system.notification.ServerRuntimeNotificationListenerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.runtime.RuntimeFactory;

/**
 * Handler TradesEnabled attribute change notification from ServerRuntimeMbean.
 * if trades are enabled then triggers match with existing quotes. if trades are disabled then
 * generates alert thar order matching will be suspended.
 * <AUTHOR>
 * @since 4.4
 */
public class ServerRuntimeMBeanNotificationListener implements NotificationListener
{

	private static Log log = LogFactory.getLog(ServerRuntimeNotificationListenerC.class);

	/* (non-Javadoc)
	 * @see javax.management.NotificationListener#handleNotification(javax.management.Notification, java.lang.Object)
	 */
	@Override
	public void handleNotification( Notification notification, Object handback )
	{
		log.info("handleNotification : New notification : " + notification.getMessage());
		try
		{
			if ( notification instanceof AttributeChangeNotification )
			{
				AttributeChangeNotification acn = (AttributeChangeNotification) notification;
				if ( "tradesEnabled".equals(acn.getAttributeName()) )
				{
					if ( !RuntimeFactory.getServerRuntimeMBean().isTradesEnabled() )
					{
						log.warn("handleNotification - suspending order matching.");
						OrderBroadcaster.republishAll();
					}
					else if ( RuntimeFactory.getServerRuntimeMBean().isTradesEnabled() )
					{
						log.warn("handleNotification - resuming order matching.");
						OrderBroadcaster.republishAll();
					}
				}
			}
		}
		catch ( Exception e )
		{
			log.error("handleNotification: Failed to handle notification.", e);
		}
	}

}
