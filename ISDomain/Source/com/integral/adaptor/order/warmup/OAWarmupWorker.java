package com.integral.adaptor.order.warmup;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.persistence.util.LogUtil;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class OAWarmupWorker implements Runnable
{
	private static Log log = LogFactory.getLog(OAWarmupWorker.class);

	/* (non-Javadoc)
	  * @see java.lang.Runnable#run()
	  */
	public void run()
	{
		try
		{
			LogUtil.setDisableDBQueryLogSwitch( true );
			long t0 = System.currentTimeMillis();
			Collection<Organization> makers = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
			int numberOfMakers = makers != null ? makers.size() : -1;
			//Takers warm-up
			for (Organization maker : makers)
			{
				if ( !maker.isActive () )
				{
					continue;
				}
				Collection<Organization> takers = maker.getRelatedActiveOrganizations (ISCommonConstants.LP_ORG_RELATIONSHIP);
				if (takers != null)
				{
					for (Organization taker : takers)
					{
						log.info(" warming up m=" + maker + ",t=" + taker + " ,t.du=" + taker.getDefaultDealingUser());
						WarmUpTradeUtilC.getInstance().performReferenceDataWarmupOnSubscription(taker, maker, taker.getDefaultDealingUser(), false, null, null);
					}
				}
			}
			long t1 = System.currentTimeMillis();
			//LPs warm-up
			Set<String> lps = ProviderManagerC.getInstance().getProviderMap().keySet();
			int numberOfLPs = lps != null ? lps.size() : -1;
			for (Organization fi : makers)
			{
				if (lps != null)
				{
					for (String alp : lps)
					{
						Organization lp = ISUtilImpl.getInstance().getOrg(alp);
						if (lp == null || !lp.isActive () )
						{
							continue;
						}
						log.info(" warming up fi=" + fi + ",lp=" + alp + ",fi.du " + fi.getDefaultDealingUser());
						WarmUpTradeUtilC.getInstance().performReferenceDataWarmupOnSubscription(fi, lp, fi.getDefaultDealingUser(), false, null, null);
					}
				}
			}

			log.info(new StringBuilder(200).append("OWW.run : finished warming up order providers and LPs. timeTakenForOrderProviders=").append(t1 - t0).append(",timeTakenForLPs=").append(System.currentTimeMillis() - t1).append(",orderProviders=").append(numberOfMakers).append(",lpCount=").append(numberOfLPs).toString());
		}
		finally
		{
			LogUtil.removeDisableDBQueryLogSwitch();
		}
	}
}
