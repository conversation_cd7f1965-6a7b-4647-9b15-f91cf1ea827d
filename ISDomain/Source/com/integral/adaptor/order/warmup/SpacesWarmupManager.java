package com.integral.adaptor.order.warmup;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.handler.OATradeHandler;
import com.integral.audit.SpacesAuditEventC;
import com.integral.broker.config.BrokerConfigurationService;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.log.QuoteDropMetrics;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Stream;
import com.integral.ems.snapshot.MarketSnapshot;
import com.integral.ems.snapshot.MarketSnapshotElement;
import com.integral.ems.snapshot.MarketSnapshotElementList;
import com.integral.ems.warmup.EMSWarmupManager;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.creditLimit.CreditLimit;
import com.integral.finance.creditLimit.SpaceCreditUtilizationEventC;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.fix.client.FixConstants;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.message.IMTPMessageFactory;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.Provider;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.*;
import com.integral.is.message.directed.orders.responses.OrderFillResponse;
import com.integral.is.message.directed.orders.responses.OrderRejectResponse;
import com.integral.is.message.directed.orders.responses.RejectReason;
import com.integral.is.oms.*;
import com.integral.is.oms.calculator.ExecuteOrderWorkflowCalculator;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.order.configuration.OMSConfigurationFactory;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.spaces.fx.esp.primebroker.PrimeBrokerUtil;
import com.integral.is.spaces.fx.esp.util.DealingModelUtil;
import com.integral.is.spaces.fx.handler.AdaptorResponseHandler;
import com.integral.is.spaces.fx.handler.DirectedOrderResponseHandler;
import com.integral.is.spaces.fx.handler.DirectedOrderVerificationHandler;
import com.integral.is.spaces.fx.listener.TradeListener;
import com.integral.is.spaces.fx.service.ServiceFactory;
import com.integral.is.warmuptrade.WarmUpTradeFactory;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.message.*;
import com.integral.message.MessageFactory;
import com.integral.model.TradeInfo;
import com.integral.model.dealing.*;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.descriptor.CoveredOrderRequestDescriptor;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.model.dealing.descriptor.OriginatingOrderRequestDescriptor;
import com.integral.model.ems.ClientChannelType;
import com.integral.model.ems.CustomExecutionRule;
import com.integral.model.ems.EMSExecutionRule;
import com.integral.model.ems.EMSExecutionType;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.oms.spaces.fx.esp.validation.FXESPOrderRequestValidator;
import com.integral.provider.ProviderOrgFunction;
import com.integral.serialization.ISMessageSerializerUtil;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.spaces.serialize.SerializationException;
import com.integral.spaces.serialize.SerializationHandler;
import com.integral.spaces.serialize.SerializerFactory;
import com.integral.spaces.serialize.SerializerUtil;
import com.integral.spaces.spi.mongodb.MongoDBMetaspaceHandle;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;
import com.integral.user.OrganizationRelationship;
import com.integral.user.User;

import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * User: pulkit
 * Date: 11/14/12
 * Time: 10:06 AM
 */
public class SpacesWarmupManager extends OAWarmupTradeManagerC {
    private static final int NUM_WARMUP_MESSAGES_FOR_BROKER = 1000;

    protected Log log = LogFactory.getLog(this.getClass());

    protected List<Organization> providerList = new ArrayList<Organization>();
    protected List<String> currencyPairs = new ArrayList<String>();
    protected ConcurrentHashMap<String, List<Organization>> lpFIOrganizations = new ConcurrentHashMap<String, List<Organization>>();
    private final int NUMBER_OPERATIONS = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getWarmupOperationCount();
    private static final int NUMBER_THREADS = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getWarmupThreadCount();
    private CountDownLatch warmupCountLatch;
    private boolean hasFIsConfigured = false;
    private long warmupExecutionTimeInMS = 120000;  // do a warmup for 2 minutes
    private Date ttlExpiryDate;
    /**
     * The name of the organization custom field which holds the quote owner ruleset which contains rules for supported ccy pairs
     */
    protected final static String QUICK_TRADE_CCYPAIR_CUSTOM_FIELD_NAME = "DirectFX_OneClickCurrencyPair";
    /**
     * Delimiter used in the currency pair name separating the base and variable currencies.
     */
    protected final static char CURRENCY_PAIR_DELIMITER = '/';

    private ConcurrentMap<String, List<String>> allFiLpSupportedCurrencyPairs = new ConcurrentHashMap<String, List<String>>();

    /**
     * Broker org short name, if this server is run as a broker and the broker org is not deployed as an OA.
     * Please note that this will be null, if broker org is deployed as OA also.
     */
    private Collection<String> brokerOrgs;
    
    protected AtomicLong id = new AtomicLong(1000l);

    public SpacesWarmupManager() {
        setupTestData();
    }


    private void setupTestData() {
        Date currentDate = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(currentDate);
        calendar.set(Calendar.DAY_OF_WEEK, 7);
        ttlExpiryDate = calendar.getTime();
        log.info("Setting ttl time to " + ttlExpiryDate);
        currencyPairs = (ArrayList<String>) WarmUpTradeUtilC.getInstance().getTestTradeMBean().getStartUpAppMonitoredCcyPairs();
        initAdditionalCounterparties();
        ArrayList<Organization> fiOrgs = new ArrayList<Organization>();
        brokerOrgs = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizationNames();
        if( brokerOrgs != null ) {
            for (String brokerName : brokerOrgs) {
                if (brokerName != null && !"MASFXI".equals(brokerName)) {
                    Organization brokerOrg = ISUtilImpl.getInstance().getOrg(brokerName);
                    if (brokerOrg != null) {
                        fiOrgs.add(brokerOrg);
                    }
                }
            }
        }
        Collection<Organization> allFIOrgs = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
        if (allFIOrgs != null && !allFIOrgs.isEmpty()) {
            for (Organization o : allFIOrgs) {
                if (o.getShortName().equals("MASFXI")) {
                    continue;
                }
                fiOrgs.add(o);
            }
        }
        HashSet<String> allFiOrgShortNames = new HashSet<String>();
        for (Organization current : fiOrgs) {
            allFiOrgShortNames.add(current.getShortName());
        }

        for (Provider prov : ProviderManagerC.getInstance().getProviderMap().values()) {
            Organization providerOrg = ISUtilImpl.getInstance().getOrg(prov.getName());
            if (providerOrg != null) {
                List<Organization> cptyAOrgs = new ArrayList<Organization>();
                if (providerOrg.getTradingVenueOrgFunction() != null) {
                    Collection<TradingVenueRelationShip> tvRelations = providerOrg.getTvCustomersRelations();
                    if (tvRelations != null) {
                        for (TradingVenueRelationShip tvRelation : tvRelations)
                        {
                            final Organization tvCustOrg = tvRelation.getCustomerOrg();
                            if ( tvCustOrg != null && tvCustOrg.isActive () )
                            {
                                cptyAOrgs.add ( tvCustOrg );
                            }
                        }
                    }
                } else {
                    cptyAOrgs = (List<Organization>) WarmUpTradeUtilC.getInstance().getAllCounterPartyAOrgs(providerOrg);
                }

                ArrayList<Organization> filteredOrgs = new ArrayList<Organization>();
                for (Organization org : cptyAOrgs) {
                    if (allFiOrgShortNames.contains(org.getShortName())) {
                        List<String> ccyPairs = (providerOrg.getTradingVenueOrgFunction() != null) ?
                                getVenueCurrencyPairs(org, providerOrg) : getCurrencyPairs(org, providerOrg);
                        if (ccyPairs != null && ccyPairs.size() > 0) {
                            filteredOrgs.add(org);
                        }
                    }
                }
                if (filteredOrgs.size() > 0) {
                    hasFIsConfigured = true;
                    providerList.add(providerOrg);
                    prioritizeOrgs(filteredOrgs, additionalCounterparties);
                    lpFIOrganizations.put(prov.getName(), filteredOrgs);
                }
            }

        }
        for (Map.Entry<String, List<Organization>> currentEntry : lpFIOrganizations.entrySet()) {
            StringBuilder sb = new StringBuilder(500);
            sb.append("WILL WARMUP LP: ").append(currentEntry.getKey()).append(" for FIs:-");
            for (Organization org : currentEntry.getValue()) {
                sb.append(org.getShortName()).append(",");
            }
            log.info(sb.toString());
        }

    }

    public boolean isWarmupTestTradeRequired(String cptyB) {
        return true;
    }

    @Override
    public Map getTradeDetails() {
        return null;
    }

    @Override
    public Map getTradeRequestCache() {
        return null;
    }

    @Override
    public void removeWarmupObjectsFromCache() {
        FXESPWorkflowCache.clearWarmUpObjects();
    }

    protected void initAdditionalCounterparties() {
        StringTokenizer stFIs = new StringTokenizer(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getPrioritizedCounterPartyList(), ",");
        while (stFIs.hasMoreTokens()) {
            String fiName = stFIs.nextToken();
            Organization fi = ISUtilImpl.getInstance().getOrg(fiName);
            if (fi != null) {
                additionalCounterparties.add(fi);
            }
        }
    }


    @Override
    public void initiateRunTimeTestRequests(String providerName) {
    }

    @Override
    public void tradeResponseCallBack(WorkflowMessage msg) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void tradeResponseCallBack(TradeResponse tradeResponse) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void tradeResponseCallBack(TradeResponses tradeResponse) {
        throw new UnsupportedOperationException();
    }

    protected void warmupOALayer(Quote testQuote, Organization cptyBOrg, Organization cptyAOrg, User user, String ccyPair) {
        log.info("SpacesWarmupManager.warmupOALayer:  Start  for CptyB Org= " + cptyBOrg.getShortName() + " CptyA Org= " + cptyAOrg.getShortName() + " User= " + user.getShortName());

        OMSConfigurationFactory.getOMSConfig();
        OrderConfiguration.getInstance();

        initializeUserSession(user);//initialize trading user session

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setMessageId(0);
        msg.setSender(user);
        msg.setEvent(MessageEvent.CREATE);
        msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);
        ISUtilImpl.getInstance().setAsWarmUpObject(msg);
        SingleLegOrder order = getSingleLegOrder(user, user.getOrganization(), testQuote.getCurrencyPair(), testQuote);
        order.setTimeInForce(com.integral.model.dealing.TimeInForce.GTC);
        order.setExecutionFlags(order.getExecutionFlags() | ExecutionFlags.ALLOW_ORDER_MATCH | ExecutionFlags.ALLOW_QUOTE_CROSS | ExecutionFlags.BEST_PRICE);
        msg.setObject(order);
        msg.setParameterValue("messageHandler", new WarmupResponseHandler());
        com.integral.is.finance.dealing.ServiceFactory.getOrderRequestService().process(msg, new WarmupResponseHandler());
        if (msg.getReplyMessage().getStatus().equals(MessageStatus.FAILURE)) {
            FXOrderCache.remove(order.get_id());
            return;
        }

        warmupMatching( testQuote, order, cptyBOrg, cptyAOrg, user, ccyPair );
        
        ExecuteOrderWorkflowCalculator calc = OrderCalculatorFactory.getInstance().getExecuteOrderWorkflowCalculator();
        if (calc != null) {
            calc.executeOrder(order, testQuote, getOrderExecutionDetails(order, testQuote));
        } else {
            log.info("SpacesWarmupManager.warmupOALayer: executeOrderWorkflowCalculator is null.");
        }

        // create a withdraw workflow message
        Order order1 = FXOrderCache.get(order.get_id());
        if (order1 != null) {
            OrderBookCacheC.getInstance().cancelOrder(order1, true);
        }
        FXOrderCache.remove(order.get_id());
    }

    protected void warmupMatching(Quote testQuote, SingleLegOrder orderRequest, Organization cptyBOrg, Organization cptyAOrg, User user, String ccyPair) {
        log.info( "SpacesWarmupManager.warmupMatching: Start  for CptyB Org= " + cptyBOrg.getShortName() + " CptyA Org= " + cptyAOrg.getShortName() + " User= " + user.getShortName() );
        try
        {
            List<Organization> preferredProvs = new ArrayList<Organization>();
            preferredProvs.add( cptyBOrg );
            orderRequest.getOrderRequestAttributes().setPreferredProviders( preferredProvs );
            testQuote.setActive( true );
            if ( testQuote.getBids() != null && !testQuote.getBids().isEmpty() )
            {
                testQuote.getBids().get( 0 ).setDealtAmount( 1L );
                testQuote.getBids().get( 0 ).setTotalLimit( 1L );
            }
            if ( testQuote.getOffers() != null && !testQuote.getOffers().isEmpty() )
            {
                testQuote.getOffers().get( 0 ).setDealtAmount( 1L );
                testQuote.getOffers().get( 0 ).setTotalLimit( 1L );
            }
            QuoteDropMetrics metrics = new QuoteDropMetrics( "OrderBook:" + "warmup" );
            Orders orders = ( Orders ) testQuote.getOrdersFacade();
            String bookName = getOrderBookName( ccyPair, cptyAOrg.getShortName() );
            if ( orders == null )
            {
                orders = new Orders();
                testQuote.setOrdersFacade( orders );
                orders.setBookName( bookName );
            }
            orders.setHourglass( false );
            orders.setEntity( testQuote );
            orders.setZeroLiquidity( testQuote.isZeroLiquidity() );

            for ( DealingPrice dp : testQuote.getBids() )
            {
                FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) dp;
                Order order = null;
                try
                {
                    order = ( Order ) ( ( FXLegDealingPrice ) dealingPrice ).getOrderPriceObjectFacade();
                    if ( order == null )
                    {
                        order = new MarketDataOrderC( dealingPrice );
                        dealingPrice.setOrderPriceObjectFacade( order );
                    }
                    order.init( bookName, cptyAOrg.getShortName() );
                }
                catch ( Exception ex )
                {
                    log.info( new StringBuilder( "SpacesWarmupManager.warmupMatching " ).append( " order creation failed for " ).append( dealingPrice.getGUID() ).append( " for book " ).append( bookName ).toString(), ex );
                }
                order.setHourglassPricingSupported( false );
                orders.add( order );
            }

            for ( DealingPrice dp : testQuote.getOffers() )
            {
                FXLegDealingPrice dealingPrice = ( FXLegDealingPrice ) dp;
                Order order = null;
                try
                {
                    order = ( Order ) ( ( FXLegDealingPrice ) dealingPrice ).getOrderPriceObjectFacade();
                    if ( order == null )
                    {
                        order = new MarketDataOrderC( dealingPrice );
                        dealingPrice.setOrderPriceObjectFacade( order );
                    }
                    order.init( bookName, cptyAOrg.getShortName() );
                }
                catch ( Exception ex )
                {
                    log.info( new StringBuilder( "SpacesWarmupManager.warmupMatching " ).append( " order creation failed for " ).append( dealingPrice.getGUID() ).append( " for book " ).append( bookName ).toString(), ex );
                }
                order.setHourglassPricingSupported( false );
                orders.add( order );
            }
            MatchingRiders FORCETOBFALSE = new MatchingRiders();
            Order order = OrderFactory.newOrder( orderRequest );
            MatchingCriteriaValidatorC._match( orders.getOffers(), order, FORCETOBFALSE, false, metrics, 1L );
        }
        catch ( Exception ex ){
            log.info( new StringBuilder( "SpacesWarmupManager.warmupMatching Encountered exception for cptyBOrg=" ).append(cptyBOrg.getShortName()).append( ", cptyAOrg=" )
                    .append( cptyAOrg.getShortName() ).toString() );
        }
    }

    public String getOrderBookName( String ccyPair, String org )
    {
        StringBuilder orderBookName = new StringBuilder(20);
        orderBookName.append(org).append('+');
        orderBookName.append(ccyPair);
        return orderBookName.toString();
    }

    protected SingleLegOrder warmupOALayer_Venue(Quote testQuote, Organization cptyBOrg, Organization cptyAOrg, User user) {
        log.info("SpacesWarmupManager.warmupOALayer_Venue:  Start  for CptyB Org= " + cptyBOrg.getShortName() + " CptyA Org= " +
                cptyAOrg.getShortName() + " User= " + user.getShortName());
        OMSConfigurationFactory.getOMSConfig();
        OrderConfiguration.getInstance();
        initializeUserSession(user);//initialize trading user session
        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setMessageId(0);
        msg.setSender(user);
        msg.setEvent(MessageEvent.CREATE);
        msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);
        ISUtilImpl.getInstance().setAsWarmUpObject(msg);
        SingleLegOrder order = getSingleLegOrder(user, user.getOrganization(), testQuote.getCurrencyPair(), testQuote);
        order.setTimeInForce(com.integral.model.dealing.TimeInForce.GTC);
        order.setExecutionFlags(order.getExecutionFlags() | ExecutionFlags.ALLOW_ORDER_MATCH | ExecutionFlags.ALLOW_QUOTE_CROSS | ExecutionFlags.BEST_PRICE);
        msg.setObject(order);
        msg.setParameterValue("messageHandler", new WarmupResponseHandler());
        com.integral.is.finance.dealing.ServiceFactory.getOrderRequestService().process(msg, new WarmupResponseHandler());
        if (msg.getReplyMessage().getStatus().equals(MessageStatus.FAILURE)) {
            if(order.get_id() != null) {
                Order order1 = FXOrderCache.get( order.get_id() );
                if ( order1 != null ) {
            FXOrderCache.remove(order.get_id());
                }
            }
            return null;
        }
        ExecuteOrderWorkflowCalculator calc = OrderCalculatorFactory.getInstance().getExecuteOrderWorkflowCalculator();
        if (calc != null) {
            calc.executeOrder(order, testQuote, getOrderExecutionDetails(order, testQuote));
        } else {
            log.info("SpacesWarmupManager.warmupOALayer: executeOrderWorkflowCalculator is null.");
        }
        return order;
    }


    protected OrderExecutionDetails getOrderExecutionDetails(SingleLegOrder order, Quote quote) {
        OrderExecutionDetails orderExecutionDetails = OrderFactory.newOrderExecutionDetails();
        FXLegDealingPrice quoteDp = (FXLegDealingPrice) quote.getQuotePrice(ISConstantsC.OFFER);
        orderExecutionDetails.setMatchableAmount(quoteDp.getDealtAmount());
        orderExecutionDetails.setPriceName(quoteDp.getName());
        orderExecutionDetails.setCandidateOrderID(order.get_id());
        orderExecutionDetails.setMatchedOrderID("WarmUpQuoteID@" + quote.getGUID());
        orderExecutionDetails.setFilledAmount(quoteDp.getDealtAmount());
        orderExecutionDetails.setUnfilledAmount(0.0);
        orderExecutionDetails.setMakerUser(order.getUser());
        orderExecutionDetails.setExecutionPrice(quoteDp.getSpotRate());
        orderExecutionDetails.setExecutionType(OrderExecutionDetails.EXEC_LP_CROSS);
        orderExecutionDetails.setHandler(new OATradeHandler());
        return orderExecutionDetails;
    }

    private boolean warmupVenueOrderResponse(TradingVenueEntity tradingVenue, Organization cptyAOrg, User user, LegalEntity cmLegalEntity, SingleLegOrder orderRequest, boolean rejectNext, ResponsTuple counter ){
        try {
            log.info(new StringBuilder(200).append("SpacesWarmupManager.warmupVenueOrderResponse:  Start  for TV= ").append(tradingVenue.getShortName()).
                    append(" CUST= ").append(cptyAOrg.getShortName()).append(" USER= ").append(user.getShortName()).append(", cmLE=").
                    append(cmLegalEntity == null ? " " : cmLegalEntity.getShortName()).append(", reject=" + rejectNext ).toString());
            OrderMatchRequest activeMatchRequest = null;
            for( OrderMatchRequest matchRequest : orderRequest.getActiveMatchRequests() ){
                activeMatchRequest = matchRequest;
            }
            if( activeMatchRequest == null ){
                return false;
            }
            if (rejectNext) {
                OrderRejectResponse orderReject = new OrderRejectResponse();
                orderReject.setOrderId( activeMatchRequest.get_id() );
                orderReject.setReason( RejectReason.INTERNAL_REJECT );
                orderReject.getTimestamp();
                orderReject.setRejectType( OrderRejectResponse.RejectType.NEW_ORDER );
                DirectedOrderResponseHandler.getInstance().handleRejectResponse( orderReject );
                counter.getRejectionCount().incrementAndGet();
            } else {
                OrderFillResponse orderFillResponse = new OrderFillResponse();
                orderFillResponse.setOrderId( activeMatchRequest.get_id() );
                orderFillResponse.setFillId( "10" + activeMatchRequest.get_id() );
                orderFillResponse.setCounterPartyFillId( "11" + activeMatchRequest.get_id() );
                orderFillResponse.setCounterPartyLE( activeMatchRequest.getLegalEntity() );
                orderFillResponse.setCptyClearingMemberLE( cmLegalEntity );
                orderFillResponse.setFillQty( orderRequest.getOrderAmount() );
                orderFillResponse.setAggressor(true);
                long[] timings = new long[4];
                timings[0] = System.currentTimeMillis() + 100;
                timings[1] = System.currentTimeMillis() + 200;
                timings[2] = System.currentTimeMillis() + 300;
                timings[3] = System.currentTimeMillis() + 400;
                orderFillResponse.setEventTimes( timings );
                DirectedOrderVerificationHandler.getInstance().handleFillResponse( orderFillResponse );
                counter.getVerificationCount().incrementAndGet();
            }
        }
        catch ( Exception ex )
        {
            log.error( "Error: happened in SpacesWarmupManager", ex);
        }
        finally {
            Order order1 = FXOrderCache.get(orderRequest.get_id());
            if (order1 != null) {
                OrderBookCacheC.getInstance().cancelOrder(order1, true);
            }
            FXOrderCache.remove(orderRequest.get_id());
            FXESPWorkflowCache.removeMatchRequest(orderRequest.get_id());
        }
        return true;
    }


    /**
     * @param testQuote
     * @param cptyBOrg
     * @param cptyAOrg
     * @param user
     * @param rejectNext
     * @param counter
     * @return true if the trade workflow was executed successfully.
     */
    protected boolean initiateAppServerTradeRequest(Quote testQuote, Organization cptyBOrg, Organization cptyAOrg, User user, boolean rejectNext, ResponsTuple counter) {
        log.info("SpacesWarmupManager.initiateAppServerTradeRequest:  Start  for CptyB Org= " + cptyBOrg.getShortName() + " CptyA Org= " + cptyAOrg.getShortName() + " User= " + user.getShortName());

        initializeUserSession(user);//initialize trading user session

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setMessageId(0);
        msg.setSender(user);
        msg.setEvent(MessageEvent.CREATE);
        msg.setTopic(FixConstants.MSG_TOPIC_REQUEST);
        boolean brokerInitiated = false;

        if (brokerOrgs != null && brokerOrgs.contains(cptyAOrg.getShortName())) {
            brokerInitiated = true;
        }
        SingleLegOrder order = getSingleLegOrder(user, user.getOrganization(), testQuote.getCurrencyPair(), testQuote);
        if (brokerInitiated) {
            order.setTimeInForce(TimeInForce.GTD);
            order.setEmsInitiated(true);
            order.setExecutionRule(new EMSExecutionRule());
            order.getExecutionRule().setExecutionType(EMSExecutionType.COVER);
        }
        msg.setObject(order);
        msg.setParameterValue("messageHandler", new WarmupResponseHandler());
        WorkflowMessage wfmsg1 = ServiceFactory.getFxisRequestService().process(msg, new WarmupResponseHandler());
        if (order.getState().getName().equals(State.Name.RSCANCELLED) || order.getState().getName().equals(State.Name.RSDECLINED) || order.getState().getName().equals(State.Name.RSEXCEPTION)) {
            return false;
        }
        // place match event
        SingleLegOrderMatch matchEvent = DealingModelFactory.getInstance().newMatchEventPQ(order);
        matchEvent.getMatchedQuote().setStreamId(testQuote.getStream());

        msg = ISUtilImpl.getInstance().createWorkflowMessage(null, ISConstantsC.MSG_EVT_CREATE, ISConstantsC.MSG_TOPIC_REQUEST);
        msg.setMessageId(0);
        msg.setSender(order.getUser());
        matchEvent.setHandler(new WarmupResponseHandler());
        // Set the broker initiated flag on MatchEvent.
        matchEvent.setBrokerInitiated(brokerInitiated);
        matchEvent.setEmsInitiated(brokerInitiated);
        long nowMillis = System.currentTimeMillis();
        MatchEventTimes matchEventTimes = matchEvent.getMatchEventTimes();
        matchEventTimes.setClientSentAcceptance(nowMillis);
        matchEventTimes.setOrderMatchedByServer(nowMillis);

        msg.setParameterValue(ISConstantsC.CPU_USAGE_COMPACT, ISConstantsC.NA);
        msg.setParameterValue(ISConstantsC.LAST_POLL_LATANCY_COMPACT, ISConstantsC.NA);
        msg.setParameterValue(ISConstantsC.ATTEMPTS_COMPACT, ISConstantsC.NA);
        msg.setParameterValue(ISConstantsC.MEMORY_USAGE_COMPACT, ISConstantsC.NA);
        FXLegDealingPrice dp = (FXLegDealingPrice) testQuote.getQuotePrice("OFFER");
        if (testQuote.getPriceType() == Quote.PRICE_TYPE_ORDERS) {
            if (order.getCreatedTime() > dp.getEffectiveTime()) {//if broadcast order is older then this order is taker.
                msg.setParameterValue(ISConstantsC.ISMAKER_FLAG_COMPACT, "false");
            } else if (order.getCreatedTime() != dp.getEffectiveTime()) {
                msg.setParameterValue(ISConstantsC.ISMAKER_FLAG_COMPACT, "true");
            }
        } else {//if it is multi-tier or multi-quote update from rate providers then order is taker.
            msg.setParameterValue(ISConstantsC.ISMAKER_FLAG_COMPACT, "false");
        }
        String matchedPriceName = "OFFER";
        double matchedAmount = order.getOrderAmount();
        DealingModelUtil.populateMatchEvent(matchEvent, order, testQuote, matchedPriceName, matchedAmount);
        if (brokerInitiated) {
            if (Math.random() > 0.5d) {
                matchEvent.setTimeInForce(TimeInForce.IOC);
            } else {
                matchEvent.setTimeInForce(TimeInForce.FOK);
            }
        }
        // Check if the provider is a broker since we need to populate extra attributes for broker.
        Organization providerOrg = matchEvent.getMatchedQuote().getOrganization();
        boolean isProviderBroker = providerOrg.isBroker() || (providerOrg.getRealLP() != null && providerOrg.getRealLP().isBroker());

        if (isProviderBroker) {
            matchEvent.setMatchEventDO(new MatchEventDO());
        }

        long nanoTime = System.nanoTime();
        msg.setParameterValue(ISConstantsC.MESSAGE_ID, nanoTime);
        msg.setObject(matchEvent);
        long time = System.currentTimeMillis();
        matchEvent.getMatchEventTimes().setOaSentAcceptance(time);
        matchEvent.getMatchEventTimes().setAcceptanceSentToIS(time);
        WorkflowMessage wfmsg = ServiceFactory.getFxisRequestService().process(msg, new WarmupResponseHandler());

        SingleLegTrade trade = (SingleLegTrade) matchEvent.getTrades().get(0);

        if (trade.getState().getName().equals(State.Name.TSFAILED) || (wfmsg.getReplyMessage() != null && MessageStatus.FAILURE.equals(wfmsg.getReplyMessage().getStatus()))) {
            log.warn("Trade :" + trade.get_id() + " is in failed state or replyMessage had failure set.");
            counter.getFailureCount().incrementAndGet();
            return false;
        }
        String tradeIdForResponse = trade.get_id();
        if (matchEvent.isPrimeBrokerCoverEnabled()) {
            tradeIdForResponse = PrimeBrokerUtil.generatePrimeBrokerCoverTradeId(trade.get_id());
        }

        try {
            if (rejectNext) {
                if (!trade.isInternalRejection()) {
                    if (isProviderBroker) {
                        final BrokerOrderResponse response = getBrokerOrderResponse(cptyBOrg);
                        response.setTradeId(tradeIdForResponse);
                        response.setStatusCode(BrokerOrderResponse.StatusCode.COMPLETE);
                        response.setResponseType(BrokerOrderResponse.ResponseType.REJECT);
                        response.setAsWarmupMessage(true);
                        response.setReasonDescription("WARMUP REJECTING TRADE with ID:" + tradeIdForResponse);

                        IMTPApplicationMessage applicationMessage = IMTPMessageFactory.borrowApplicationMessage(ISMessageSerializerUtil.serialize(response));
                        applicationMessage.addProperty("OBJECTTYPE", response.getClass().getName());
                        applicationMessage.addProperty("PROVIDER_NAME", cptyBOrg.getShortName());
                        applicationMessage.addProperty("objType", response.getClass().getName());
                        applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                        applicationMessage.setAppId(tradeIdForResponse);
                        applicationMessage.setAppSelector("TOIS.MESSAGES");

                        TradeListener tradeListener = new TradeListener(applicationMessage, System.currentTimeMillis());
                        tradeListener.run();

                        counter.getRejectionCount().incrementAndGet();
                    } else {
                        TradeReject tradeReject = com.integral.is.message.MessageFactory.newTradeReject();
                        tradeReject.setTradeId(tradeIdForResponse);
                        tradeReject.setReasonDescription("WARMUP REJECTING TRADE with ID:" + tradeIdForResponse);

                        tradeReject.getTiming().setTime(AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
                                System.currentTimeMillis());
                        tradeReject.setAsWarmupMessage(true);
                        tradeReject.toString();
                        String tvMsg = tradeReject.getToString();
                        tradeReject.populateObject(tvMsg);

                        IMTPApplicationMessage applicationMessage = IMTPMessageFactory.borrowApplicationMessage(tradeReject.getToString());
                        applicationMessage.addProperty("OBJECTTYPE", tradeReject.getClass().getName());
                        applicationMessage.addProperty("PROVIDER_NAME", cptyBOrg.getShortName());
                        applicationMessage.addProperty("objType", tradeReject.getClass().getName());
                        applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                        applicationMessage.setAppId(tradeIdForResponse);
                        applicationMessage.setAppSelector("TOIS.MESSAGES");

                        TradeListener tradeListener = new TradeListener(applicationMessage, System.currentTimeMillis());
                        tradeListener.run();

                        counter.getRejectionCount().incrementAndGet();
                    }
                }
            } else {
                if (isProviderBroker) {
                    BrokerOrderResponse response = getBrokerOrderResponse(cptyBOrg);
                    response.setTradeId(tradeIdForResponse);
                    response.setResponseType(BrokerOrderResponse.ResponseType.VERIFY);
                    response.setAsWarmupMessage(true);


                    IMTPApplicationMessage applicationMessage = IMTPMessageFactory.borrowApplicationMessage(ISMessageSerializerUtil.serialize(response));
                    applicationMessage.addProperty("OBJECTTYPE", response.getClass().getName());
                    applicationMessage.addProperty("PROVIDER_NAME", cptyBOrg.getShortName());
                    applicationMessage.addProperty("objType", response.getClass().getName());
                    applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                    applicationMessage.setAppId(tradeIdForResponse);
                    applicationMessage.setAppSelector("TOIS.MESSAGES");

                    TradeListener tradeListener = new TradeListener(applicationMessage, System.currentTimeMillis());
                    tradeListener.run();

                    counter.getVerificationCount().incrementAndGet();
                } else {
                    TradeVerify tradeVerify = com.integral.is.message.MessageFactory.newTradeVerify();
                    tradeVerify.setTradeId(tradeIdForResponse);
                    tradeVerify.setAcceptedAmount(matchEvent.getMatchEventLeg().getFinalAcceptanceAmount());
                    tradeVerify.setAcceptedPrice(matchEvent.getMatchEventLeg().getFinalAcceptanceSpotRate());

                    tradeVerify.getTiming().setTime(AdaptorResponseHandler.EVENT_TIME_RESPONSE_RECEIVED_BY_APP,
                            System.currentTimeMillis());
                    tradeVerify.toString();
                    tradeVerify.setAsWarmupMessage(true);
                    String tvMsg = tradeVerify.getToString();
                    tradeVerify.populateObject(tvMsg);

                    IMTPApplicationMessage applicationMessage = IMTPMessageFactory.borrowApplicationMessage(tradeVerify.getToString());
                    applicationMessage.addProperty("OBJECTTYPE", tradeVerify.getClass().getName());
                    applicationMessage.addProperty("PROVIDER_NAME", cptyBOrg.getShortName());
                    applicationMessage.addProperty("objType", tradeVerify.getClass().getName());
                    applicationMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_STRING);
                    applicationMessage.setAppId(tradeIdForResponse);
                    applicationMessage.setAppSelector("TOIS.MESSAGES");

                    TradeListener tradeListener = new TradeListener(applicationMessage, System.currentTimeMillis());
                    tradeListener.run();
                    counter.getVerificationCount().incrementAndGet();
                }
            }
        } catch (Exception ex) {
            log.error("Error: happened in SpacesWarmupManager", ex);
        }
        return true;
    }


    protected void initializeUserSession(User user) {
        IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
        ctx.setAttribute("clientVersion", "2.4.0.6");
        ctx.setAttribute("clientName", "FXInside");
        ctx.setAttribute("isFXISession", Boolean.TRUE);
        ctx.setAttribute(ISConstantsC.CLIENT_VERSION, ISConstantsC.FXI_CLIENT_VERSION_2_0);
        IdcSessionManager.getInstance().setSessionContext(ctx);

    }

    public void initiateStartUpTestRequests() {
        warmupCreditSerializers();
        warmupBrokerOrderRequestSerializers();
        warmupBrokerOrderResponseSerializers();
        warmupTradeResponseSerializers();
        warmupSerializerClassInfo();

        try {
            // If this is a BrokerAdaptor, invoke the EMSWarmupManager to warmup EMS.
            BrokerConfigurationService brokerConfigurationService = BrokerConfigurationServiceFactory.getBrokerConfigurationService();
            for(String broker : brokerConfigurationService.getDeployedBrokerOrganizationNames()){
                warmupBroker(broker);
            }
            EMSWarmupManager.getInstance().warmupBroker(brokerConfigurationService.getDeployedBrokerOrganizations());
        } catch (Exception ex) {
            // Ignore all the exceptions.
            log.error("Could not warmup the broker organization.", ex);
        }

        if (providerList.size() > 0 && currencyPairs.size() > 0 && hasFIsConfigured && lpFIOrganizations.size() > 0) {
            List<List<Organization>> organizationPerThreadArray = getOrganizationsPerThread(NUMBER_THREADS);
            int noThreads = organizationPerThreadArray.size();
            warmupCountLatch = new CountDownLatch(noThreads);
            log.info("Number of warmup thread: " + noThreads + " providercount: " + providerList.size() + " Warmup Execution time: " + warmupExecutionTimeInMS + " Operation count" + NUMBER_OPERATIONS);

            ExecutorService executorService = Executors.newFixedThreadPool(noThreads);
            List<WarmupRunnable> warmupTasks = new ArrayList<WarmupRunnable>(organizationPerThreadArray.size());
            for (int i = 0; i < organizationPerThreadArray.size(); i++) {
                WarmupRunnable task = new WarmupRunnable(i, (NUMBER_OPERATIONS / noThreads), warmupCountLatch, organizationPerThreadArray.get(i));
                warmupTasks.add(task);
                executorService.submit(task);
            }

            try {
                boolean status = warmupCountLatch.await(warmupExecutionTimeInMS, TimeUnit.MILLISECONDS);
                if (!status) {
                    log.warn("Warmup stopped as it has exceeded the maximum time allowed");
                }
            } catch (InterruptedException ex) {
                log.error("Thread interrupted, warmup will be stopped", ex);
            }

            for (WarmupRunnable task : warmupTasks) {
                task.stop();
            }
            executorService.shutdownNow();
            while (!executorService.isTerminated()) {
                try {
                    log.info("WARMUP: waiting for threads to complete");
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.error("ExecutorService for warmup interrupted");
                }
            }
            log.info("Exiting initiateStartUpTestRequests after warmup");
        }
    }

    private void warmupBrokerOrderResponseSerializers() {
        BrokerOrderResponse brokerOrderResponse = getBrokerOrderResponse(null);
        ByteBuffer byteBuffer = ByteBuffer.allocate(1024);
        SerializationHandler sh = SerializerFactory.getHandlerForType(SerializerFactory.Type.JSON);
        try {
            for (int i = 0; i < 500; i++) {
                sh.serializeObject(brokerOrderResponse, byteBuffer);
                byteBuffer.clear();
            }
        } catch (SerializationException ex) {
            log.error("SpacesWarmupManager.warmupBrokerOrderResponseSerializers() error in serialization", ex);
        }
    }

    private void warmupBrokerOrderRequestSerializers() {
        BrokerOrderRequest brokerOrderRequest = getBrokerOrderRequest(null);
        ByteBuffer byteBuffer = ByteBuffer.allocate(1024);
        SerializationHandler sh = SerializerFactory.getHandlerForType(SerializerFactory.Type.JSON);
        try {
            for (int i = 0; i < 500; i++) {
                sh.serializeObject(brokerOrderRequest, byteBuffer);
                byteBuffer.clear();
            }
        } catch (SerializationException ex) {
            log.error("SpacesWarmupManager.warmupBrokerOrderRequestSerializers() error in serialization", ex);
        }
    }

    private void warmupBroker(String brokerShortName) {
        Organization brokerOrg = ISUtilImpl.getInstance().getOrg(brokerShortName);
        if (brokerOrg != null) {
            log.info("Warming up the broker: " + brokerShortName);
            warmupBrokerOrderRequestSerialization(brokerOrg);
            warmupBrokerOrderResponseSerialization(brokerOrg);
        }
    }

    private void warmupBrokerOrderResponseSerialization(Organization brokerOrg) {
        try {
            for (int i = 0; i < NUM_WARMUP_MESSAGES_FOR_BROKER; i++) {
                BrokerOrderResponse response = getBrokerOrderResponse(brokerOrg);
                String serStr = ISMessageSerializerUtil.serialize(response);
                BrokerOrderResponse newResp = com.integral.is.message.MessageFactory.newBrokerOrderResponse();
                ISMessageSerializerUtil.deserialize(newResp, serStr);
            }
        } catch (Exception ex) {
            log.error("Could not warmup the BrokerOrderResponse.", ex);
        }
    }

    private BrokerOrderResponse getBrokerOrderResponse(Organization brokerOrg) {
        Random random = new Random();
        BrokerOrderResponse response = com.integral.is.message.MessageFactory.newBrokerOrderResponse();
        response.setTradeId("trd" + random.nextInt());
        response.setTradeAmount(random.nextDouble());
        response.setAcceptanceReceivedByAdaptorTime(random.nextLong());
        response.setAcceptedAmount(random.nextDouble());
        response.setStatusCode(BrokerOrderResponse.StatusCode.COMPLETE);
        response.setResponseType(BrokerOrderResponse.ResponseType.VERIFY);
        response.setValueDate(System.currentTimeMillis());
        response.setManualIntervention(false);

        response.setBaseCurrency(CurrencyFactory.getCurrency("USD"));
        response.setVariableCurrency(CurrencyFactory.getCurrency("JPY"));
        if (brokerOrg != null) {
            response.setMakerUser(brokerOrg.getDefaultDealingUser());
            response.setCoverLegalEntity(brokerOrg.getDefaultDealingEntity());
        }

        response.setResponseSentByAdaptorTime(System.nanoTime());
        response.setResponseReceivedByAppTime(System.currentTimeMillis() - 10);

        TradeLeg.CoverRateDescriptor coverRateDesc = new TradeLeg.CoverRateDescriptor();
        coverRateDesc.setPPPreTradeSpread(random.nextDouble());
        coverRateDesc.setPPPostTradeSpread(random.nextDouble());
        coverRateDesc.setPMPreTradeSpread(random.nextDouble());
        coverRateDesc.setPMPostTradeSpread(random.nextDouble());
        coverRateDesc.setPPSpotSpread(random.nextDouble());
        coverRateDesc.setPMSpotSpread(random.nextDouble());
        coverRateDesc.setPMMinSpread(random.nextDouble());
        coverRateDesc.setPMMaxSpread(random.nextDouble());
        coverRateDesc.setCoverExecutionRate(1.19893d);
        response.setCoverRateDescriptor(coverRateDesc);

        // Mark this as warmup response.
        response.setAsWarmupMessage(true);
        response.toString();
        return response;
    }

    private BrokerOrderRequest getBrokerOrderRequest(Organization brokerOrg) {
        Random random = new Random();
        BrokerOrderRequest request = com.integral.is.message.MessageFactory.newBrokerOrderRequest();
        if (brokerOrg != null) {
            request.setProviderShortName(brokerOrg.getShortName());
            request.setOriginatingLP(brokerOrg.getShortName());
            request.setOrganization(brokerOrg);
            request.setUser(brokerOrg.getDefaultDealingUser());
        }
//        request.setStreamId(streams.get(0).getShortName());

        request.setServerId("testServer");
        request.setBuySell(random.nextBoolean() ? TradeRequest.SELL : TradeRequest.BUY);
        request.setAmount(10000.0);
        request.setRate(99.00);
        request.setCustomerOrderPureMarketOrder(random.nextBoolean());
        request.setCustomerOrderAmount(2030.00D);
        request.setBrokerForCustomer(true);
        request.setPriceType(MatchEventPQ.QuoteDescriptor.Type.MULTI_QUOTE);
        request.setOrderType(OrderRequest.Type.LIMIT);
        request.setCustomerSpotPrecision(4);
        request.setState(1);

        request.setBaseCurrency(CurrencyFactory.getCurrency("EUR"));
        request.setVariableCurrency(CurrencyFactory.getCurrency("USD"));
        request.setDealtCurrency(CurrencyFactory.getCurrency("EUR"));

        request.setRequestId("req" + random.nextInt());
        request.setOrderTxnId("ordTxId" + random.nextInt());
        request.setOriginatingOrderId("order" + random.nextInt());
        request.setTraderExternalId("trade" + random.nextInt());
        request.setTakerReferenceId("takerId" + random.nextInt());

        CustomExecutionRule executionRule = new CustomExecutionRule();
        executionRule.setOrderTIF(EMSExecutionRule.OrderTIF.FOK);
        executionRule.setPriceTakingTIF(EMSExecutionRule.PriceTakingTIF.FOK);
        executionRule.setMinimumFillAmount(10000.0D);
        executionRule.setPPPostTradeSpread(0.0004d);
        executionRule.setPPPreTradeSpread(0.0003d);
        executionRule.setExternalSpread(0.0d);
        executionRule.setNoCoverEnabled(random.nextBoolean());
        executionRule.setPriceValidationDisabled(random.nextBoolean());

        CoveredTradeDescriptor desc = new CoveredTradeDescriptor();
        desc.setTradeRef(new DealingModelRef());
        request.setCoveredTrade(desc);

        request.setCoveredOrder(new CoveredOrderRequestDescriptor());
        request.setOriginatingOrder(new OriginatingOrderRequestDescriptor());

        Set<String> exclProviders = new HashSet<String>();
        exclProviders.add("TESTLP1");
        exclProviders.add("TESTLP2");
        executionRule.setExcludedProviders(exclProviders);
        request.setCustomExecutionRule(executionRule);
        request.setValueDateLong(System.currentTimeMillis());
        request.setProviderType("BA");
        request.setOrderExpiryTime(System.currentTimeMillis() + 100);
        request.setClientChannelType(ClientChannelType.OA);

        request.setAsWarmupMessage(true);
        request.toString();
        return request;
    }


    private void warmupBrokerOrderRequestSerialization(Organization brokerOrg) {
        // Wrap everything in a try block so that we ignore any exceptions that
        try {
            BrokerOrganizationFunction function = (BrokerOrganizationFunction) brokerOrg.getOrganizationFunction(BrokerOrganizationFunction.ORG_FUNC_NAME);
            if (function == null || function.getStreams().isEmpty()) {
                return;
            }
            List<Stream> streams = new ArrayList<Stream>(function.getStreams());
            // Warmup the serializtaion of BrokerOrderResponse.
            for (int i = 0; i < NUM_WARMUP_MESSAGES_FOR_BROKER; i++) {
                BrokerOrderRequest request = getBrokerOrderRequest(brokerOrg);
                request.setStreamId(streams.get(0).getShortName());

                String serStr = ISMessageSerializerUtil.serialize(request);
                BrokerOrderRequest newRequest = com.integral.is.message.MessageFactory.newBrokerOrderRequest();
                ISMessageSerializerUtil.deserialize(newRequest, serStr);
            }
        } catch (Exception ex) {
            log.error("Could not warmup the SpacesBrokerOrderHandler for broker: " + brokerOrg.getShortName(), ex);
        }
    }

    private List<List<Organization>> getOrganizationsPerThread(int numberThreads) {
        int finalCount = providerList.size() > numberThreads ? numberThreads : providerList.size();
        List<List<Organization>> allThreadOrganizations = new ArrayList<List<Organization>>(finalCount);
        for (int i = 0; i < finalCount; i++) {
            List<Organization> providerOrganizations = new ArrayList<Organization>();
            allThreadOrganizations.add(providerOrganizations);
        }
        /**
         * The logic has been modified,we just hash all the organizations to thread rather than deriving on the condition
         * for the count of organizations to be greater than a threshold.
         */
        if (providerList.size() > numberThreads) {
            int i = 0;
            for (Organization aProviderList : providerList) {
                allThreadOrganizations.get(i++ % numberThreads).add(aProviderList);
            }
        } else {
            /**
             * Just assign a thread to each organization
             */
            int i = 0;
            for (Organization organization : providerList) {
                allThreadOrganizations.get(i++).add(organization);
            }
        }
        return allThreadOrganizations;
    }

    /**
     * Warmup the serializers for credit events.
     * Exception is caught outside of the for loop, as even if a single serialization attempt, all the following should fail as well.
     */
    private void warmupCreditSerializers() {
        SpaceCreditUtilizationEventC scue = new SpaceCreditUtilizationEventC();
        Organization mainOrg = ISUtilImpl.getInstance().getOrg("MAIN");
        scue.setNamespace(mainOrg.getNamespace());
        scue.setBuySell(CreditLimit.BUY);
        ByteBuffer byteBuffer = ByteBuffer.allocate(1024);
        SerializationHandler sh = SerializerFactory.getHandlerForType(SerializerFactory.Type.JSON);
        try {
            for (int i = 0; i < 500; i++) {
                sh.serializeObject(scue, byteBuffer);
                byteBuffer.clear();
            }
        } catch (SerializationException ex) {
            log.error("SpacesWarmupManager.warmupCreditSerializers() error in serialization", ex);
        }

    }

    private void warmupSerializerClassInfo()
    {
        SerializerUtil.getClassInfo(MarketSnapshotElement.class);
        SerializerUtil.getClassInfo(MarketSnapshotElementList.class);
        SerializerUtil.getClassInfo(MarketSnapshot.class);
        SerializerUtil.getClassInfo(MongoDBMetaspaceHandle.MongoIDQuery.class);
        SerializerUtil.getClassInfo(SpreadInfo.class);
        SerializerUtil.getClassInfo(com.integral.spaces.notification.Notification.class);
        SerializerUtil.getClassInfo(SpacesAuditEventC.class);
        SerializerUtil.getClassInfo(TradeInfo.class);

    }


    protected SingleLegOrder getSingleLegOrder(User user, Organization fi, CurrencyPair ccyPair, Quote quote) {
        SingleLegOrder orderRequest = DealingModelFactory.getInstance().newOrderRequest();
        orderRequest.setNamespace(user.getNamespace());
        orderRequest.setWarmUpObject(true);
        orderRequest.setUser(user);
        orderRequest.setCurrencyPair(ccyPair);
        orderRequest.setDealtCurrency(ccyPair.getBaseCurrency());
        orderRequest.setType(com.integral.model.dealing.OrderRequest.Type.LIMIT);
        orderRequest.setChannel("DNET");
        orderRequest.setOrganization(fi);
        orderRequest.setTimeInForce(com.integral.model.dealing.TimeInForce.FOK);
        orderRequest.setTimeToLive(ttlExpiryDate);
        FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getDefault();
        FXRateBasis rateBasis = fxRateConvention.getFXRateBasis(orderRequest.getCurrencyPair());
        orderRequest.setFxRateBasis(rateBasis);
        orderRequest.setLegalEntity(user.getOrganization().getDefaultDealingEntity());
        FXLegDealingPrice quoteDp = (FXLegDealingPrice) quote.getQuotePrice(ISConstantsC.OFFER);
        double rate = quoteDp.getBaseRate();
        com.integral.model.dealing.OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        requestLeg.setBuySellMode(com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode.BUY);
        requestLeg.setTenor(FixConstants.SPOT_TENOR.getName());
        requestLeg.setAmount(1);
        requestLeg.setSpotRate(rate);
        
        long oid = id.incrementAndGet();
        orderRequest.set_id("WRMO"+oid);
        orderRequest.setTransactionId("WRMR"+oid);
        
        return orderRequest;
    }

    private class WarmupResponseHandler implements MessageHandler {

        public Message handle(Message message) {
            log.info("Received message on warmup response handler ...................");
            return message;
        }
    }

    protected void doWarmUpForOA(Quote testQuote, Organization cptyBOrg, Organization cptyAOrg, User user) {

    }

    private List<String> getVenueCurrencyPairs(Organization fiOrg, Organization venueOrg) {
        String fiLPString = fiOrg.getShortName() + "-" + venueOrg.getShortName();
        List<String> ccyPairs = allFiLpSupportedCurrencyPairs.get(fiLPString);
        if (ccyPairs == null) {
            log.info("Trying to get Currency pairs after resolution for FI:" + fiOrg.getShortName() + " LP: " + venueOrg.getShortName());
            ccyPairs = new ArrayList<String>();
            CurrencyPairGroup ccyPairGroup = venueOrg.getTradingVenueOrgFunction().getTradingVenue().getSupportedCurrencyPairs();
            if ((ccyPairGroup != null) && (ccyPairGroup.getCurrencyPairs() != null)) {
                for (CurrencyPair ccyPair : ccyPairGroup.getCurrencyPairs()) {
                    ccyPairs.add((new StringBuilder(ccyPair.getBaseCurrency().getShortName()).append("/").append(ccyPair.getVariableCurrency().getShortName())).toString());
                }
            }
            log.info("CurrencyPair count: " + ccyPairs.size() + " list" + ccyPairs);
        }
        allFiLpSupportedCurrencyPairs.put(fiLPString, ccyPairs);
        return ccyPairs;
    }

    private List<String> getCurrencyPairs(Organization fiOrg, Organization lpOrg) {
        boolean pbSetup = false;
        String fiLPString = fiOrg.getShortName() + "-" + lpOrg.getShortName();
        List<String> ccyPairs = allFiLpSupportedCurrencyPairs.get(fiLPString);
        if (ccyPairs == null) {
            log.info("Trying to get Currency pairs for FI:" + fiOrg.getShortName() + " LP: " + lpOrg.getShortName());
            Collection<TradingParty> tradingParties = fiOrg.getTradingParties(ISConstantsC.FI_ORG_RELATIONSHIP);
            for (TradingParty tradingParty : tradingParties) {
                if (tradingParty.getLegalEntityOrganization().getIndex() == lpOrg.getIndex()) {
                    if (tradingParty.isPrimeBrokerCoverTradeEnabled()) {
                        Organization pbOrg = tradingParty.getPrimeBrokerOrganization();
                        if (pbOrg != null) {
                            lpOrg = pbOrg;
                            pbSetup = true;
                            break;
                        }
                    }
                }
            }
            if (!pbSetup) {
                if (lpOrg.isMasked()) {
                    if (!lpOrg.isMaskLPCurrencyPairs()) {
                        lpOrg = lpOrg.getRealLP();
                    }
                }
            }
            log.info("Trying to get Currency pairs after resolution for FI:" + fiOrg.getShortName() + " LP: " + lpOrg.getShortName());
            ccyPairs = getCurrencyPairStrings(fiOrg, lpOrg);
            log.info("CurrencyPair count: " + ccyPairs.size() + " list" + ccyPairs);
        }
        allFiLpSupportedCurrencyPairs.put(fiLPString, ccyPairs);
        return ccyPairs;
    }


    private ArrayList<String> getCurrencyPairStrings(Organization fiOrg, Organization lpOrg) {
        Set ccyPairsFromCcyGrp = new HashSet(100);
        Set ccyPairs = new HashSet(100);
        ArrayList<String> allCurrencyPairs = new ArrayList<String>();
        if (lpOrg.getShortName().equals("FXI")) {
            return allCurrencyPairs;
        }
        Collection ccyPairList = (Collection) fiOrg.getCustomFieldValue(QUICK_TRADE_CCYPAIR_CUSTOM_FIELD_NAME + lpOrg.getShortName());

        if (ccyPairList != null && !ccyPairList.isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug(new StringBuffer(200).append("SpacesWarmupManager.findAll.DEBUG : lp quick trade ccy pairs=")
                        .append(ccyPairList).append(",lp=").append(lpOrg.getShortName()).append(",fi=").append(fiOrg.getShortName()).toString());
            }

            Iterator ccyPairIter = ccyPairList.iterator();
            while (ccyPairIter.hasNext()) {
                String ccyPair = (String) ccyPairIter.next();
                ccyPairs.add(ccyPair);
            }
        } else {
            ProviderOrgFunction orgFunct = lpOrg.getProviderOrgFunction();
            OrganizationRelationship orgRel = getOrgRelationShip(lpOrg, fiOrg);

            if (null != orgRel) {
                CurrencyPairGroup ccyPairGroup = orgRel.getOneClickCurrencyPairGroup();

                if (null == ccyPairGroup && null != orgFunct) {
                    ccyPairGroup = orgFunct.getOneClickCurrencyPairGroup();
                }

                Collection currencyPairs = null;

                if (null != ccyPairGroup) {
                    currencyPairs = ccyPairGroup.getCurrencyPairs();
                    ccyPairsFromCcyGrp.addAll(currencyPairs);
                }

                if (null == currencyPairs || currencyPairs.isEmpty()) {
                    log.warn(new StringBuffer(200).append("SpacesWarmupManager.findAll.WARN : No quick trade currency pair list for lp org=")
                            .append(lpOrg.getShortName()).append(" for fi Org=").append(fiOrg.getShortName()).toString());
                }

                if (log.isDebugEnabled()) {
                    log.debug(new StringBuffer(200).append("SpacesWarmupManager.findAll.DEBUG : Custom or default lp quick trade ccy pairs=")
                            .append(currencyPairs).append(",lp=").append(lpOrg.getShortName()).append(",fi=").append(fiOrg.getShortName()).toString());
                }
            } else {
                log.warn(new StringBuffer(200).append("SpacesWarmupManager.findAll.WARN : No OrganizationRelationship for lp org=")
                        .append(lpOrg.getShortName()).append(" for fi Org=").append(fiOrg.getShortName()).toString());
            }
        }

        if (!ccyPairs.isEmpty()) {
            if (log.isDebugEnabled()) {
                log.debug(new StringBuffer(200).append("SpacesWarmupManager.findAll.DEBUG : quick trade ccy pairs =")
                        .append(ccyPairs).append(",fi=").append(fiOrg.getShortName()).toString());
            }

            Collection result = new ArrayList(ccyPairs.size());
            Iterator ccyPairIter = ccyPairs.iterator();
            while (ccyPairIter.hasNext()) {
                String ccyPairName = (String) ccyPairIter.next();
                result.add(getCurrencyPair(ccyPairName));
            }
            ccyPairsFromCcyGrp.addAll(result);

        } else if (ccyPairsFromCcyGrp.isEmpty()) {
            log.warn("SpacesWarmupManager.findAll.WARN : No quick trade currency pairs for  org : " + fiOrg.getShortName());
        }
        if (!ccyPairsFromCcyGrp.isEmpty()) {
            for (Object aCcyPairList : ccyPairsFromCcyGrp) {
                CurrencyPair ccyPair = (CurrencyPair) aCcyPairList;
                allCurrencyPairs.add((new StringBuilder(ccyPair.getBaseCurrency().getShortName()).append("/").append(ccyPair.getVariableCurrency().getShortName())).toString());

            }
        }
        return allCurrencyPairs;
    }

    private OrganizationRelationship getOrgRelationShip(Organization lpOrg, Organization fiOrg) {
        OrganizationRelationship orgRel = null;
        Collection orgRels = lpOrg.getOrganizationRelationships();
        Iterator orgRelItr = orgRels.iterator();

        while (orgRelItr.hasNext()) {
            orgRel = (OrganizationRelationship) orgRelItr.next();
            if (orgRel.getRelatedOrganization().getShortName().equals(fiOrg.getShortName()) &&
                    orgRel.getClassification().getShortName().equals(ISConstantsC.LP_ORG_RELATIONSHIP))
                return orgRel;
        }
        return orgRel;
    }

    protected CurrencyPair getCurrencyPair(String ccyPairName) {
        return CurrencyFactory.newCurrencyPair(ccyPairName.substring(0, ccyPairName.indexOf(CURRENCY_PAIR_DELIMITER)), ccyPairName.substring(ccyPairName.indexOf(CURRENCY_PAIR_DELIMITER) + 1));
    }


    private class WarmupRunnable implements Runnable {
        private final int threadIndex;
        private final int count;
        private final CountDownLatch latch;
        private final List<Organization> cptyBList;
        boolean stop;

        public WarmupRunnable(int index, int count, CountDownLatch warmupCountLatch, List<Organization> organizations) {
            this.threadIndex = index;
            this.count = count;
            this.latch = warmupCountLatch;
            this.cptyBList = organizations;
            this.stop = false;
        }


        @Override
        public void run() {
            String baseCurrency = null;
            String termCurrency = null;
            Thread.currentThread().setName("WARMUP-" + threadIndex);
            int verificationCount = (90 * count) / 100;
            int rejectionCount = count - verificationCount;
            StringBuilder sbr = new StringBuilder("Will warmup for LP ");
            for (Organization organization : cptyBList) {
                sbr.append(organization.getShortName()).append(",");
            }
            log.info(sbr.toString());
            log.info("Starting warmup Thread: " + threadIndex + " will attempt to verify: " + verificationCount + " reject: " + rejectionCount);
            ResponsTuple tuple = new ResponsTuple();
            int myCount = count;
            boolean rejectNext;
            long startTime = System.currentTimeMillis();
            Set<String> skippedCombinations = new HashSet<String>();
            try {
                while (myCount > 0 && !stop) {
                    for (Organization cptyB : cptyBList) {
                        if (stop) {
                            break;
                        }
                        List<Organization> fiOrgs = lpFIOrganizations.get(cptyB.getShortName());
                        for (Organization cptyA : fiOrgs) {

                            if (stop) {
                                break;
                            }
                            List<String> allSupportedCurrencyPairs = getCurrencyPairs(cptyA, cptyB);
                            if (cptyB.isBroker()) {
                                log.info("Starting warmup for BrokerOrderRequest/BrokerOrderResponse for Broker=" + cptyB.getShortName());
                                warmupBrokerOrderRequestSerialization(cptyB);
                                warmupBrokerOrderResponseSerialization(cptyB);
                                log.info("Ended warmup for BrokerOrderRequest/BrokerOrderResponse for Broker=" + cptyB.getShortName());
                            }
                            for (String currencyPair : allSupportedCurrencyPairs) {
                                if (myCount < 1) {
                                    stop = true;
                                }
                                if (stop) {
                                    break;
                                }
                                User user = WarmUpTradeUtilC.getInstance().getUser(cptyA);
                                boolean status = false;
                                try {
                                    StringBuilder sb = new StringBuilder(cptyB.getShortName());
                                    sb.append(cptyA.getShortName()).append(currencyPair);
                                    if (skippedCombinations.contains(sb.toString())) {
                                        Thread.sleep(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDelayBetweenAcceptances());
                                        continue;
                                    }
                                    log.info("Starting warmup for FI:" + cptyA.getShortName() + " LP:" + cptyB.getShortName() + " CURRENCY PAIR= " + currencyPair + " currentCount " + myCount);
                                    baseCurrency = CurrencyFactory.getBaseCurrency(currencyPair);
                                    termCurrency = CurrencyFactory.getTermCurrency(currencyPair);
                                    Quote testQuote = WarmUpTradeFactory.newTestQuote(cptyB, cptyA, baseCurrency, termCurrency);
                                    if (testQuote == null) {
                                        skippedCombinations.add(sb.toString());
                                        continue;
                                    }
                                    FXLegDealingPrice quoteDp = (FXLegDealingPrice) testQuote.getQuotePrice(ISConstantsC.OFFER);
                                    if (quoteDp.getBaseRate() == 0) {
                                        log.info("Skipping this combination as quotePrice for offer has BaseRate of 0");
                                        skippedCombinations.add(sb.toString());
                                        continue;
                                    }
                                    testQuote.incReference(Quote.ReferenceHolder.WarmUp);

                                    try {
                                        rejectNext = myCount < rejectionCount;
                                        if (testQuote.getPriceType() == Quote.PRICE_TYPE_VENUE) {
                                            status = true;
                                        } else {
                                            status = initiateAppServerTradeRequest(testQuote, cptyB, cptyA, user, rejectNext, tuple);
                                        }

                                        Thread.sleep(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDelayBetweenAcceptances());


                                            if (ISUtilImpl.getInstance().isVenueProvider(cptyB)) {
                                            if(FXESPOrderRequestValidator.validateOrdersOrg(cptyA) == null) {
                                                TradingVenueEntity tradingVenue = cptyB.getTradingVenueOrgFunction().getTradingVenue();
                                                TradingVenueRelationShip tradingVenueRelationShip = cptyA.getTradingVenueRelationShip(cptyB);
                                                if(tradingVenueRelationShip != null){
                                                    LegalEntity custCMLE = tradingVenueRelationShip.getClearingMember();
                                                    Collection<LegalEntity> clearingMembers = tradingVenue.getClearingMembers();
                                                    // iterating over rest of the customers in the same venue
                                                    for (LegalEntity cptyCMLE : clearingMembers) {
                                                        if (myCount < 1) {
                                                            stop = true;
                                                            break;
                                                        }
                                                        if (!custCMLE.isSameAs(cptyCMLE) && !CounterpartyUtilC.isValidRelationBetweenLEs(custCMLE, cptyCMLE))
                                                            continue;
                                                        Collection<LegalEntity> custLEs = cptyA.getLegalEntities();
                                                        for (LegalEntity custLE : custLEs) {
                                                            try {
                                                                if (myCount < 1) {
                                                                    stop = true;
                                                                    break;
                                                                }
                                                                if (!CounterpartyUtilC.isValidRelationBetweenLEs(custLE, custCMLE)) continue;
                                                                rejectNext = myCount < rejectionCount;
                                                                log.info(new StringBuilder(400).append("SpacesWarmupManager:Warming up for combination: custOrg=").append(cptyA.getShortName()).append(", custLE=")
                                                                        .append(custLE.getShortName()).append(", tv=").append(tradingVenue.getShortName()).append(", cptyCMLE=").append(cptyCMLE.getShortName())
                                                                        .append(", custCMLE=").append(custCMLE.getShortName()).append(", ccyPair=").append(currencyPair).toString());

                                                                SingleLegOrder order = null;
                                                                order = warmupOALayer_Venue(testQuote, cptyB, cptyA, user);
                                                                if (null == order) continue;
                                                                status = warmupVenueOrderResponse(tradingVenue, cptyA, user, cptyCMLE, order, rejectNext, tuple);
                                                            } catch (Exception ex) {
                                                                log.warn("Error running the warmup for venue: " + cptyB.getShortName() + " cptya: " + cptyA.getShortName() + "basecurrency: " + baseCurrency + " termcurrency" + termCurrency, ex);
                                                            } finally {
                                                                //if (status) {
                                                                //    myCount--;
                                                                //}
                                                            }

                                                        }
                                                    }
                                                }
                                            }
										}
                                        else {
                                            // Warmup OA layer if cptyA is not a broker org. But, avoid warming up OA layer,
                                            // if this broker org is not deployed as an OA on this server.
                                            if (brokerOrgs == null || !brokerOrgs.contains(cptyA.getShortName())) {
                                                warmupOALayer(testQuote, cptyB, cptyA, user, currencyPair);
                                            }
                                        }
                                    } finally {
                                        if (testQuote != null) {
                                            testQuote.decReference(Quote.ReferenceHolder.WarmUp);
                                        }
                                        // Get rid of quotes which causes memory leak.
                                        Provider prov = (Provider) com.integral.is.common.ProviderManagerC.getProviderManager().getProvider(cptyB.getShortName());
                                        if (prov != null) {
                                            prov.getQuoteCache().removeAll();
                                        }
                                    }
                                } catch (InterruptedException ex) {
                                    log.info("WARMUP-" + threadIndex + " interrupted, quiting now.");
                                    stop = true;
                                    break;
                                } catch (Exception ex) {
                                    log.warn("Error running the warmup for cptyb: " + cptyB.getShortName() + " cptya: " + cptyA.getShortName() + "basecurrency: " + baseCurrency + " termcurrency" + termCurrency, ex);
                                } finally {
                                    if (status) {
                                        myCount--;
                                    }
                                }
                            }
                        }
                        log.info("Completed warmup for LP" + cptyB.getShortName());

                    }
                }
            } catch (Exception ex) {
                log.warn("WARMUP-" + threadIndex + " interrupted stopping now", ex);
            }
            log.info("WARMUP-" + threadIndex + " executed: " + (count - myCount) + " verification count: " +
                    tuple.getVerificationCount().get() + " rejection count: " + tuple.getRejectionCount().get() + " failures: " + tuple.getFailureCount() + " time taken in secs: " + (System.currentTimeMillis() - startTime) / 1000);
            latch.countDown();
        }

        public void stop() {
            log.info("Stop warmup task WARMUP-" + threadIndex);
            stop = true;
        }
    }

    private class ResponsTuple {
        private AtomicInteger verificationCount = new AtomicInteger(0);
        private AtomicInteger rejectionCount = new AtomicInteger(0);
        private AtomicInteger failureCount = new AtomicInteger(0);

        public AtomicInteger getVerificationCount() {
            return verificationCount;
        }

        public AtomicInteger getRejectionCount() {
            return rejectionCount;
        }

        public AtomicInteger getFailureCount() {
            return failureCount;
        }
    }
}
