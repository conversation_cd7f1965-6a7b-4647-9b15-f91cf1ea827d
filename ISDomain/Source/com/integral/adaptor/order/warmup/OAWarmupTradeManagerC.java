package com.integral.adaptor.order.warmup;

import java.util.ArrayList;
import java.util.Collection;
import java.util.StringTokenizer;

import com.integral.adaptor.order.CommonAdaptorFrameworkConstants;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.handler.OATradeHandler;
import com.integral.exception.IdcException;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.dealing.fx.FXLegDealingPriceC;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateC;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceC;
import com.integral.finance.trade.Tenor;
import com.integral.is.client.ISClientService;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.Provider;
import com.integral.is.common.converter.StringStack;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.message.MessageFactory;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderExecutionDetails;
import com.integral.is.oms.OrderFactory;
import com.integral.is.oms.calculator.ExecuteOrderWorkflowCalculator;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.order.configuration.OMSConfigurationFactory;
import com.integral.is.warmuptrade.ISWarmUpTradeManagerC;
import com.integral.is.warmuptrade.WarmUpCptyTradeDetails;
import com.integral.is.warmuptrade.WarmUpTradeFactory;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.is.warmuptrade.WarmUpTransactionDetail;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.user.Organization;
import com.integral.user.User;

public class OAWarmupTradeManagerC extends ISWarmUpTradeManagerC
{

	private Log log = LogFactory.getLog(this.getClass());

	public OAWarmupTradeManagerC()
	{
		super();
	}

	public boolean initializeForTestTrade()
	{
		String providerList = "";
		for ( Provider prov : ProviderManagerC.getInstance().getProviderMap().values() )
		{
			providerList = prov.getName() + "," + providerList;
		}
		initializeTestSupportedCurrencyPairs();
		initializeTestTradeForAllCptyBs(providerList);
		return true;
	}

	protected void initAdditionalCounterparties()
	{
		super.initAdditionalCounterparties();
		additionalCounterparties.addAll(OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList());
	}

	protected void doWarmUpForOA( Quote testQuote, Organization cptyBOrg, Organization cptyAOrg, User user )
	{
		log.info("OAWarmupTradeManagerC.doWarmUpForOA:Warm up started for OA for cptyBOrg:" + cptyBOrg.getShortName() + " ,cptyAOrg:" + cptyAOrg.getShortName() + " ,User:" + user.getShortName());
		// 1. Create dummy orders
		Request testRequest = getLimitRequest(cptyAOrg, user, testQuote);
		ISCommonUtilC.setAsWarmUpObject(testRequest);
		boolean isSucess = sendLimitRequest(testRequest);
		if ( isSucess )
		{
			ExecuteOrderWorkflowCalculator calc = OrderCalculatorFactory.getInstance().getExecuteOrderWorkflowCalculator();
			if ( calc != null )
			{
				calc.executeOrder(testRequest, testQuote, getOrderExecutionDetails(testRequest, testQuote));
			}
			else
			{
				log.info("OAWarmupTradeManagerC.doWarmUpForOA: executeOrderWorkflowCalculator is null.");
			}
			WarmUpTradeUtilC.removeQuoteFromCache(testQuote);
			OrderBookCacheC.getInstance().cancelAll(testRequest);
			Order order = OrderFactory.lookupOrder(testRequest);
			if ( order != null )
			{
				OrderBookCacheC.getInstance().cancelOrder(order, true);
			}

		}
		else
		{
			log.info("OAWarmupTradeManagerC.doWarmUpForOA:Submission Failed in OA for cptyBOrg:" + cptyBOrg.getShortName() + " ,cptyAOrg:" + cptyAOrg.getShortName() + " ,User:" + user.getShortName());
		}
	}

	private Request getLimitRequest( Organization cptyAOrg, User user, Quote testQuote )
	{
		FXLegDealingPrice quoteDp = (FXLegDealingPrice) testQuote.getQuotePrice(ISConstantsC.OFFER);
		Request request = DealingFactory.newRequest();
		request.setRequestClassification(ISUtilImpl.getInstance().getRequestClassification(CommonAdaptorFrameworkConstants.MAKEPRICE_CREATE_TYPE));
		String tradeId = "TestTrade1";
		double rate = quoteDp.getSpotRate();
		double amount = quoteDp.getDealtAmount();
		String baseCCY = testQuote.getCurrencyPair().getBaseCurrency().getName();
		String varCCY = testQuote.getCurrencyPair().getVariableCurrency().getName();
		String dealtCCY = baseCCY;
		LegalEntity le = cptyAOrg.getDefaultDealingEntity();
		int intBidOffer = DealingPrice.BID;

		if ( tradeId != null )
		{
			request.setExternalRequestId(tradeId);
		}

		FXLegDealingPrice dp = FXDealingFactory.newFXLegDealingPrice();
		FXLegDealingPrice fillPrice = new FXLegDealingPriceC();
		fillPrice.setDealtAmount(0.0);
		dp.setFillDealingPrice(fillPrice);
		dp.setDealtAmount(amount);
		dp.setDealtCurrency(CurrencyFactory.getCurrency(dealtCCY));
		dp.setDealtCurrencyProperty(baseCCY.equals(dealtCCY) ? FXLegDealingPrice.CCY1 : FXLegDealingPrice.CCY2);
		dp.setSettledCurrency(CurrencyFactory.getCurrency(dealtCCY.equals(baseCCY) ? varCCY : baseCCY));
		dp.setDealtAmount(amount);
		dp.setTenor(Tenor.SPOT_TENOR);
		FXDealingPriceElement priceElement = FXDealingFactory.newFXDealingPriceElementDependent();
		FXPrice fxprice = new FXPriceC();
		FXRate fxRate = new FXRateC();
		fxRate.setRate(rate);
		fxRate.setSpotRate(rate);
		fxRate.setBaseCurrency(CurrencyFactory.getCurrency(baseCCY));
		fxRate.setVariableCurrency(CurrencyFactory.getCurrency(varCCY));
		fxRate.setFXRateConvention(QuoteConventionUtilC.getInstance().getFXRateConvention(cptyAOrg));
		if ( intBidOffer == DealingPrice.BID )
		{
			fxprice.setBidFXRate(fxRate);
			dp.setBidOfferMode(DealingPrice.BID);
		}
		else if ( intBidOffer == DealingPrice.OFFER )
		{
			fxprice.setOfferFXRate(fxRate);
			dp.setBidOfferMode(DealingPrice.OFFER);
		}
		priceElement.setPrice(fxprice);
		dp.setPriceElement(priceElement);
		request.setRequestPrice(ISConstantsC.SINGLE_LEG, dp);
		IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
		IdcSessionManager.getInstance().setSessionContext(ctx);
		request.setUser(user);
		Organization counterOrg = testQuote.getOrganization();
		Collection<Organization> toOrgs = new ArrayList<Organization>();
		toOrgs.add(counterOrg);
		request.setToOrganizations(toOrgs);
		request.setCounterparty(le);
		request.setOrganization(le.getOrganization());
		request.setChannel(ISUtilImpl.getInstance().getExtSys("DirectFXTrader"));
		request.setTimeInForce(TimeInForce.GTC);

		return request;
	}

	protected OrderExecutionDetails getOrderExecutionDetails( Request request, Quote quote )
	{
		OrderExecutionDetails orderExecutionDetails = OrderFactory.newOrderExecutionDetails();
		FXLegDealingPrice dp = (FXLegDealingPrice) request.getRequestPrice(ISConstantsC.SINGLE_LEG);
		FXLegDealingPrice quoteDp = (FXLegDealingPrice) quote.getQuotePrice(ISConstantsC.OFFER);
		orderExecutionDetails.setMatchableAmount(dp.getDealtAmount());
		orderExecutionDetails.setPriceName(quoteDp.getName());
		orderExecutionDetails.setCandidateOrderID(request.getOrderId());
		orderExecutionDetails.setMatchedOrderID("WarmUpQuoteID@" + quote.getGUID());
		orderExecutionDetails.setFilledAmount(dp.getDealtAmount());
		orderExecutionDetails.setUnfilledAmount(0.0);
		orderExecutionDetails.setMakerUser(request.getUser());
		orderExecutionDetails.setExecutionPrice(quoteDp.getSpotRate());
		orderExecutionDetails.setExecutionType(OrderExecutionDetails.EXEC_LP_CROSS);
		orderExecutionDetails.setHandler(new OATradeHandler());
		return orderExecutionDetails;
	}

	private boolean sendLimitRequest( Request request )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setMessageId(0);
		msg.setSender(request.getUser());
		msg.setEvent(MessageEvent.CREATE);
		msg.setTopic(CommonAdaptorFrameworkConstants.MSG_TOPIC_REQUEST);
		msg.setParameterValue(CommonAdaptorFrameworkConstants.MESSAGE_HANDLER, new MessageHandler() {

			@Override
			public Message handle( Message message ) throws IdcException
			{
				log.info("Message received - " + message);
				return null;
			}
		});
		msg.setParameterValue(CommonAdaptorFrameworkConstants.WF_PARAM_ORDER_TYPE, "LIMIT");
		msg.setParameterValue(CommonAdaptorFrameworkConstants.WF_PARAM_LP_CROSSING_ENABLED, true);
		long nanoTime = System.nanoTime();
		request.setNotes(new StringBuilder().append(nanoTime).append("##").append(request.getUser().getFullName()).toString());
		msg.setParameterValue(CommonAdaptorFrameworkConstants.MESSAGE_ID, nanoTime);
		msg.setParameterValue(CommonAdaptorFrameworkConstants.WF_PARAM_TRADE_CHANNEL, CommonAdaptorFrameworkConstants.TRADER_TRADE_CHANNEL);// Trade
		msg.setObject(request);
		ISUtilImpl.getInstance().setAsWarmUpObject(msg);
		try
		{
			WorkflowMessage respMessage = (WorkflowMessage) ServiceFactory.getOrderRequestService().process(msg).getReplyMessage();
			if ( respMessage != null && !MessageStatus.FAILURE.equals(respMessage.getStatus()) )
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		catch ( Exception e )
		{
			log.error("OAWarmupTradeManager.sendLimitRequest: Error -", e);
			return false;
		}

	}

	protected void initiateTestRequests( String providerName )
	{
		int fiCount = 0;
		Organization cptyBOrg = ISUtilImpl.getInstance().getOrg(providerName);
		if ( (!WarmUpTradeUtilC.getInstance().isBrokerOrg(cptyBOrg) && !additionalProviderList.contains(providerName)) )
		{
			return;
		}
		log.warn("OAWarmUpTradeManagerC.initiateTestRequests:  Start Warmup for Provider Org = " + providerName);
		WarmUpCptyTradeDetails cptyBOrgDetail = warmupTradeDetails.get(providerName);
		if ( cptyBOrgDetail == null )
		{
			return;
		}
		ArrayList cptyAOrgs = cptyBOrgDetail.getCptyAs();
		if ( cptyAOrgs.size() == 0 )
		{
			return;
		}
		ArrayList<String> currencyPairs = testStartUpCurrencyPairs;
		if ( currencyPairs.size() == 0 )
		{
			return;
		}
		String ccyPair = currencyPairs.get(0);
		String baseCurrency = CurrencyFactory.getBaseCurrency(ccyPair);
		String termCurrency = CurrencyFactory.getTermCurrency(ccyPair);
		ArrayList<String> allSupportedCurrencyPairs = WarmUpTradeUtilC.getInstance().getAllSupportedCurrencyPairs((Organization) cptyAOrgs.get(0));
		if ( "ALL".equalsIgnoreCase(baseCurrency) || "ALL".equalsIgnoreCase(termCurrency) )
		{
			currencyPairs = allSupportedCurrencyPairs;
		}
		if ( currencyPairs.size() == 0 )
		{
			return;
		}
		cptyBOrgDetail.setNumberOfTestRequired(currencyPairs.size() * cptyAOrgs.size());
		if ( additionalCounterparties.size() > 0 )
		{
			cptyAOrgs = prioritizeOrgs(cptyAOrgs, additionalCounterparties);
		}

		for ( int k = 0 ; k < cptyAOrgs.size() && (fiCount < WarmUpTradeUtilC.getInstance().getTestTradeMBean().getMaxFIPerBroker()) ; k++ )
		{
			Organization cptyAOrg = (Organization) cptyAOrgs.get(k);

			if ( !OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList().contains(cptyAOrg) || !cptyAOrg.isActive () )
			{
				continue;
			}

			User user = WarmUpTradeUtilC.getInstance().getUser(cptyAOrg);

			if ( cptyAOrg != null && cptyAOrg.isActive() )
			{
				fiCount++;
			}

			for ( String currencyPair : currencyPairs )
			{

				if ( cptyAOrg != null && user != null && cptyAOrg != cptyBOrg && cptyAOrg.isActive() && cptyBOrg.isActive() && allSupportedCurrencyPairs.contains(currencyPair) )
				{
					baseCurrency = CurrencyFactory.getBaseCurrency(currencyPair);
					termCurrency = CurrencyFactory.getTermCurrency(currencyPair);
					log.info("BASE CURRENCY = " + baseCurrency + " TERM CURRENCY = " + termCurrency);

					Quote testQuote = WarmUpTradeFactory.newTestQuote(cptyBOrg, cptyAOrg, baseCurrency, termCurrency);
					if ( testQuote == null )
					{
						continue;
					}
					WarmUpTradeUtilC.addQuoteToCache(testQuote);

					try
					{
						initiateAppServerTradeRequest(testQuote, cptyBOrg, cptyAOrg, user);
						doWarmUpForOA(testQuote, cptyBOrg, cptyAOrg, user);
						Thread.sleep(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDelayBetweenAcceptances());
					}
					catch ( Exception e )
					{
						warmupTradeComplete(cptyBOrg.getShortName());
						log.error("OAWarmUpTradeManagerC.initiateTestRequests: Error on caling initiateAppServerTradeRequest() ", e);
					}

					//remove from cache
					WarmUpTradeUtilC.removeQuoteFromCache(testQuote);
				}
				else
				{
					warmupTradeComplete(cptyBOrg.getShortName());
				}

			}
		}

		cptyBOrgDetail.setFirst(false);
		OMSConfigurationFactory.getOMSConfig();
		log.info("OAWarmUpTradeManagerC.initiateTestRequests:  End Warmup for Provider Org = " + providerName);
	}

	protected void initiateAppServerTradeRequest( Quote testQuote, Organization cptyBOrg, Organization cptyAOrg, User user )
	{
		log.info("OAWarmUpTradeManagerC.initiateAppServerTradeRequest:  Start  for CptyB Org= " + cptyBOrg.getShortName() + " CptyA Org= " + cptyAOrg.getShortName() + " User= " + user.getShortName());

		initializeUserSession(user);//initialize trading user session

		// create Test Trade Request
		String testTradeRequest = WarmUpTradeFactory.newFXITestTradeRequest(testQuote, user);
		log.info("TEST REQUEST MESSAGE= " + testTradeRequest);
		Message msg = WarmUpTradeUtilC.getInstance().getMessage(testTradeRequest, true);

		if ( MessageStatus.FAILURE.equals(msg.getStatus()) )
		{
			warmupTradeComplete(cptyBOrg.getShortName());
			log.error("OAWarmUpTradeManagerC.initiateAppServerTradeRequest Error: " + (String) msg.getProperty("Error"));
			String message = (String) ((WorkflowMessage) msg).getObject();
			log.warn("OAWarmUpTradeManagerC.initiateAppServerTradeRequest message: " + message);
			return;
		}

		msg.setSender(user);
		msg.setProperty("wfString", testTradeRequest);
		msg.setProperty("TradeType", "ESP");
		ISUtilImpl.getInstance().setAsWarmUpObject(msg);

		ISClientService cs = null;
		String response = null;
		Message responseMsg = null;

		try
		{
			cs = (ISClientService) getClientService(user);
			if ( cs == null )
			{
				log.error("OAWarmUpTradeManagerC.initiateAppServerTradeRequest:ClientService is null");
				return;
			}
			log.info("OAWarmUpTradeManagerC.initiateAppServerTradeRequest  request Message" + msg);
			responseMsg = cs.process(msg);
			log.info("OAWarmUpTradeManagerC.initiateAppServerTradeRequest  response Message" + responseMsg);
		}
		catch ( Exception re )
		{
			warmupTradeComplete(cptyBOrg.getShortName());
			// re.printStackTrace();
		}

		if ( responseMsg.getProperty(ISConstantsC.COMPACTED_RESPONSE) != null && (Boolean) responseMsg.getProperty(ISConstantsC.COMPACTED_RESPONSE) )
		{
			response = (String) ((WorkflowMessage) responseMsg).getObject();
			log.warn("compacted response " + response);
		}
		else
		{
			log.error("OAWarmUpTradeManagerC.initiateAppServerTradeRequest error response " + response);
		}

		/*

		  1194533054029

		  ~[1194533054037|1194533051608|CREATE|REQUEST|SUCCESS|G49dacd0f11161fb7dd1e1b|2007-11-08 14:44:12 GMT||966500|LIMIT|Broker5|FXI|kiranbrok5
		  |singleLeg|OFFER|SPOT|currency1|EUR|USD|1.0000||2007-11-08 14:44:11 GMT|FXI1340002||FXSpot|2007-11-08|Broker5le|FXI-le1||FXSPOTLEG|2007-11-13
		  |EUR|USD|EUR|USD|1.0000|1.0000|0.0000|STDQOTCNV|1.0000|0.0000|true||0||{TimeInForce;IOC}|{ISOrderId;966500}|{BatchSequenceNo;0}]

		  ~[1194533054057|1194533051609|CREATE|REQUEST|SUCCESS|G49dacd0f11161fb7dd271c|2007-11-08 14:44:13 GMT|false|966500|QUOTED|Broker5|CITI|kiranbrok5|
		  singleLeg|BID|SPOT|currency1|EUR|USD|1.0000||2007-11-08 14:44:13 GMT|FXI1340004||FXSpot|2007-11-08|Broker5le|CITI-le1|TSPENDING|FXSPOTLEG|
		  2007-11-08|EUR|USD|EUR|USD|1.0000|1.0000|0.0000|STDQOTCNV|1.0000|1.0000|false||0||{RequestTransactionId;FXI1340003}|{RequestDealtAmount;1.0}|{BatchSequenceNo;1}]

		   */

		StringTokenizer batchToker = new StringTokenizer(response, "~");
		String messageId = batchToker.hasMoreTokens() ? batchToker.nextToken() : "";
		String orderResponse = batchToker.hasMoreTokens() ? batchToker.nextToken() : "";

		if ( "".equals(orderResponse) )
		{
			warmupTradeComplete(cptyBOrg.getShortName());
			return;
		}

		StringStack orderResponseStack = new StringStack(orderResponse, "|");
		String orderResponseStatus = (String) orderResponseStack.get(orderResponseStack.size() - 5);
		String orderId = (String) orderResponseStack.get(orderResponseStack.size() - 9);//(String) wfMsg.getParameterValue(ISConstantsC.IS_ORDER_ID);
		String orderRequestId = (String) orderResponseStack.get(orderResponseStack.size() - 6); // (String) ((Request) wfMsg.getObject()).getReferenceId();

		log.info("ORDER SUBMISSION STATUS= " + orderResponseStatus + ", Order ID=" + orderId + ", Order Request Reference Id = " + orderRequestId);

		String tradeResponse = batchToker.hasMoreTokens() ? batchToker.nextToken() : "";

		if ( "".equals(tradeResponse) )
		{
			warmupTradeComplete(cptyBOrg.getShortName());
			return;
		}

		StringStack tradeResponseStack = new StringStack(tradeResponse, "|");
		String acceptanceResponseStatus = (String) tradeResponseStack.get(tradeResponseStack.size() - 5);
		String requestTransactionIDStr = (String) tradeResponseStack.get(tradeResponseStack.size() - 46);
		//String transactionID = (String) tradeResponseStack.get(tradeResponseStack.size() - 23);
		String transactionID = requestTransactionIDStr.substring(requestTransactionIDStr.indexOf(";") + 1, requestTransactionIDStr.indexOf("}"));
		//log.warn("ACCEPTANCE RESPONSE STATUS= " + acceptanceResponseStatus + ", REQUEST TRANSACTION ID= " + paramStr + "calc" +  paramStr.substring(paramStr.indexOf(";"),paramStr.indexOf("}")));

		if ( MessageStatus.SUCCESS.getName().equals(orderResponseStatus) )
		{

			if ( MessageStatus.SUCCESS.getName().equals(acceptanceResponseStatus) )
			{
				WarmUpTransactionDetail warmUpTransaction = new WarmUpTransactionDetail(transactionID);
				warmUpTransaction.setOrderRequestId(orderRequestId);
				warmUpTransaction.setUser(user);
				warmupTradeDetails.get(cptyBOrg.getShortName()).getTestTransactionsDetail().put(transactionID, warmUpTransaction);
			}
			else
			{
				closeOrder(user, orderRequestId, false);
			}
		}
		log.info("ACCEPTANCE RESPONSE STATUS= " + acceptanceResponseStatus + ", REQUEST TRANSACTION ID= " + transactionID);

		log.info("OAWarmUpTradeManagerC.initiateAppServerTradeRequest:  End  for CptyB Org= " + cptyBOrg.getShortName() + " ,CptyA Org = " + cptyAOrg.getShortName() + " User= " + user.getShortName());
	}

}
