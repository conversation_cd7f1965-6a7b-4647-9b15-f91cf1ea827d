package com.integral.adaptor.order.warmup.tradingvenue;

import java.util.HashMap;
import java.util.Map;

import com.integral.finance.currency.CurrencyPair;
import com.integral.imtp.IMTPMessageReceiver;
import com.integral.imtp.message.ApplicationMessage;
import com.integral.imtp.message.IMTPMessage;
import com.integral.is.common.mbean.DirectedOrderCommonMBean;
import com.integral.is.message.directed.orders.DirectedOrderMessageTranslatorUtil;
import com.integral.is.message.directed.orders.OrderStatus;
import com.integral.is.message.directed.orders.request.NewOrderRequest;
import com.integral.is.message.directed.orders.responses.DirectedOrderResponse;
import com.integral.is.message.directed.orders.responses.OrderFillResponse;
import com.integral.is.message.directed.orders.serializer.DirectedOrderSerializer;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.user.Organization;
import com.integral.user.User;


public final class WarmupVenue implements IMTPMessageReceiver  {

	 protected Log log = LogFactory.getLog( this.getClass() );
	 private final byte version;
	 private static final Map<String,DirectedOrderResponse> responses = new HashMap<String,DirectedOrderResponse>();
	 private final String selector;
	    
	 public WarmupVenue(byte version, String selector){
	      this.version = version;
	      this.selector = selector;
	    }
	 
	 public void clear() {
		responses.clear();
	}



      public void init() {
      }

  
      


  public final void messageReceived(final IMTPMessage imtpMessage) {
      ApplicationMessage msg = (ApplicationMessage) imtpMessage;
      byte[] data = (byte[]) msg.getApplicationData();
      log.info("Message Received");
  }


  
 
}
