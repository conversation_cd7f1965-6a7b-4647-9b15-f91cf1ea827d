package com.integral.adaptor.order.warmup.tradingvenue;


import com.integral.imtp.IMTPFactory;
import com.integral.imtp.config.IMTPConfigMBeanFactory;
import com.integral.imtp.config.IMTPConfigMBeanImpl;
import com.integral.imtp.connection.Acceptor;
import com.integral.imtp.connection.Connection;
import com.integral.imtp.connection.IMTPService;
import com.integral.imtp.connection.IMTPServiceListener;
import com.integral.imtp.session.IMTPSession;
import com.integral.imtp.session.IMTPSessionMessageHandler;

public class WarmupIMTPMessageListener implements IMTPServiceListener {

  private Acceptor acceptor;
  private String acceptorId;
  private final WarmupVenue receiver;
  private final byte version;
  private final String selector;
  private final IMTPWarmup sender;
  
  public WarmupIMTPMessageListener(final String acceptorId, final int port,
      final String host,
      final WarmupVenue receiver , 
      final IMTPWarmup sender ,byte version
      , String selector) throws Exception {
   
		IMTPConfigMBeanImpl conf = IMTPConfigMBeanImpl.copyIMTPConfig(IMTPConfigMBeanFactory.getInstance().getIMTPConfigMBean());
	conf.setAcceptorPort(port);
	conf.setAcceptorId(acceptorId);
	conf.setInitiatorId(acceptorId);
	conf.setAcceptorHost(host);
	this.acceptor = IMTPFactory.getInstance().createNewAcceptor(conf);
	this.acceptor.registerIMTPServiceListener(this);
    this.receiver = receiver;
    this.version = version;
    this.selector = selector;
    this.sender = sender;
  }
  
  @Override
  public void notifyIMTPServiceEvent(IMTPService imtpService, IMTPServiceEvent event) {
    // TODO Auto-generated method stub
    switch (event.type) {
      case CONNECTION_ACTIVATED:
        Connection con = (Connection) event.payload;
        IMTPSession session = con.getSession();
        String sessionId = session.getSessionId();
        IMTPSessionMessageHandler messageHandler = session.getMessageHandler();
        WarmupVenue myConnector = new WarmupVenue(version,selector);
        myConnector.init();
        messageHandler.registerReceiver( this.receiver, selector);
        break;
      default:
        break;
    }
  }

  public void start() throws Exception {
    this.acceptor.bind();
  }

  public void stop() throws Exception {
    this.acceptor.shutDown();
  }

  public void tearDown() {
    this.acceptor = null;
  }

  public String getAcceptorId() {
    return this.acceptorId;
  }

  public int getPort() {
    return this.acceptor == null ? -1 : this.acceptor.getBoundPort();
  }

}

