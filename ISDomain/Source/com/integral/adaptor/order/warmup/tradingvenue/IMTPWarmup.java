package com.integral.adaptor.order.warmup.tradingvenue;

import java.io.IOException;

import com.integral.commons.buffers.UnSafeBuffer;
import com.integral.imtp.IMTPMessageReceiver;
import com.integral.imtp.config.IMTPConfigMBeanFactory;
import com.integral.imtp.config.IMTPConfigMBeanImpl;
import com.integral.imtp.connection.IMTPConnection;
import com.integral.imtp.connection.IMTPConnectionManager;
import com.integral.imtp.message.IMTPApplicationMessage;
import com.integral.imtp.message.IMTPMessageFactory;
import com.integral.imtp.session.IMTPSessionManager;
import com.integral.imtp.session.Session;
import com.integral.imtp.session.IMTPSession.SessionState;
import com.integral.is.common.exception.MessageCommunicationExceptionC;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.directed.orders.request.DOCMessage;
import com.integral.is.message.directed.orders.serializer.DirectedOrderSerializer;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class IMTPWarmup{
  protected Log log = LogFactory.getLog( this.getClass() );
  private IMTPMessageReceiver messageReceiver;
  private Session session;
  private String sessionId;
  private IMTPConfigMBeanImpl config;
  private final String selector;
  private byte version;
  private static final int MAX_ATTEMPT = 5;

  public IMTPWarmup(String acceptorId, String hostname, int port,
		  IMTPMessageReceiver messageReceiver,
		  byte version,String selector) throws IOException {
    this.config =  IMTPConfigMBeanImpl.copyIMTPConfig(IMTPConfigMBeanFactory.getInstance().getIMTPConfigMBean());
    this.selector = selector;
    this.config.setAcceptorId(acceptorId);
    this.config.setInitiatorId(acceptorId);
    this.config.setAcceptorHost(hostname);
    this.config.setAcceptorPort(port);
    this.sessionId = IMTPSessionManager.getSessionId(config);
    this.session =
        (Session) IMTPSessionManager.getInstance().getSession(sessionId, IMTPConnectionManager.getInstance().getInitiator().getSessionPipelineSetupFunctor());
    this.messageReceiver = messageReceiver;
    session.getMessageHandler().registerReceiver(messageReceiver,  this.selector);
    this.version = version;
  }

  public void connect() throws IOException {
    IMTPConnection imtpConnection = session.getConnection();
    if (imtpConnection == null || imtpConnection.getConnectionState().equals(IMTPConnection.ConnectionState.CLOSED)) {
      if (!session.getAndSetIsPendingConnection(true)) {
        try {
          imtpConnection = session.getConnection();
          if (imtpConnection == null || imtpConnection.getConnectionState().equals(IMTPConnection.ConnectionState.CLOSED)) {
            log.info("Setting IMTP Connection for Peer:" + config.getAcceptorId());
            IMTPConnectionManager.getInstance().getInitiator().connect(config);
          }
        } finally {
          session.getAndSetIsPendingConnection(false);
        }
      } else {
        log.info("IMTP Connection already pending for session:" + session.getSessionId());
      }
    }
  }

  public void disconnect(){
    this.session.shutDown();
  }
  
  public void activate()
  {
	  int attempts = 0;
		while(!this.session.getSessionState().equals(SessionState.ACTIVE)
				&& attempts < MAX_ATTEMPT)
		{
			
			try {
				Thread.sleep(1000);
				connect();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			attempts++;
		}
  }

  public ResponseMessage send(byte[] bytes) throws MessageCommunicationExceptionC {
	    IMTPApplicationMessage appMessage = IMTPMessageFactory.borrowApplicationMessage(bytes);
		appMessage.setAppDataType(IMTPApplicationMessage.APP_DATA_TYPE_OBJECT);
		appMessage.setAppSelector(this.selector);
		try {
			session.getMessageHandler().sendMessage(appMessage, false);
		} catch (Exception ex) {
			this.log.error("IMTPMessageSender.sendMessage : Error in sending server warmup imtp message", ex);
			throw new MessageCommunicationExceptionC(new StringBuffer().append("Exception in sending ").append(appMessage.getClass().getName())
					.append(" to tv ").append("clob").append(" ").append(ex.toString()).append(": message{").append(ex.getMessage())
					.append("}").toString());
		}
		return null;

  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append("IMTPWarmup [session=").append(session).append(", sessionId=").append(sessionId).append("]");
    return builder.toString();
  }

}
