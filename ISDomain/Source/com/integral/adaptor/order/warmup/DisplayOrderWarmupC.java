/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.warmup;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicLong;

import com.integral.adaptor.manager.AdaptorTradeManager;
import com.integral.adaptor.order.CommonAdaptorFrameworkConstants;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.handler.OAHandlerFactory;
import com.integral.adaptor.request.RequestServiceC;
import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairC;
import com.integral.finance.dealing.DealingFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Request;
import com.integral.finance.dealing.TimeInForce;
import com.integral.finance.dealing.fx.FXDealingFactory;
import com.integral.finance.dealing.fx.FXDealingPriceElement;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXFactory;
import com.integral.finance.fx.FXRate;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.price.fx.FXPrice;
import com.integral.finance.price.fx.FXPriceFactory;
import com.integral.finance.trade.Tenor;
import com.integral.finance.trade.Trade;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.cache.HandlerCacheC;
import com.integral.is.common.cache.ISTradeCache;
import com.integral.is.common.cache.RequestHolder;
import com.integral.is.common.facade.EntityPropertyMap;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.finance.dealing.workflowhandler.BaseWorkflowHandlerC;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeRequest;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderFactory;
import com.integral.is.order.OAWorkflowFunctorC;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.persistence.TransactionIdFacade;
import com.integral.persistence.TransactionIdFacadeC;
import com.integral.persistence.spaces.PersistenceConstants;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.spaces.Metaspaces;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.system.runtime.StartupTask;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.LinkedEntryMap;

/**
 * This class will warm-up workflow related to display orders. 
 * <AUTHOR>
 *
 */
public class DisplayOrderWarmupC implements StartupTask
{

	/* (non-Javadoc)
	 * @see com.integral.system.runtime.StartupTask#startup(java.lang.String, java.util.Hashtable)
	 */
	static Log log = LogFactory.getLog(DisplayOrderWarmupC.class.getClass());
	
    protected AtomicLong id = new AtomicLong(2000l);

	public String startup( String aName, Hashtable args ) throws Exception
	{
		long start = System.currentTimeMillis();
		ServerRuntimeMBean sMbean = null;
		try
		{

			if ( !WarmUpTradeUtilC.getInstance().getTestTradeMBean().isWarmUpEnabled() )
			{
				log.warn("DisplayOrderWarmupC : WarmUp Trade  DISABLED");
				return null;
			}
			if ( !WarmUpTradeUtilC.getInstance().getTestTradeMBean().isDisplayOrderWarmUpEnabled() )
			{
				log.warn(" DisplayOrderWarmupC : Display Order WarmUp DISABLED ");
				return null;
			}

			log.warn("DisplayOrderWarmupC : Display Order WarmUp Started ");

			sMbean = RuntimeFactory.getServerRuntimeMBean();
			sMbean.setServerWarmingUp(true);

			while ( !ConfigurationFactory.getServerMBean().isFullyStarted() )
			{
				log.warn("DisplayOrderWarmupC: Waiting for Server to Fully Start Before triggering Warmup Workflow\" ");
				Thread.sleep(10000);
			}

			Map<Organization, Collection<Organization>> makers2TakersMap = getMakers2TakersMap();

			// CHECK THE MAX NUMBER OF ALLOWED TRADES
			int maxWarmupTrades = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDisplayOrderMaxWarmUpTrades();

			CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString("EUR/USD");

			// DIVIDE TAKER-MAKERS TRADES FAILRLY
			Iterator<Entry<Organization, Collection<Organization>>> it = makers2TakersMap.entrySet().iterator();

			while ( it.hasNext() )
			{
				Map.Entry<Organization, Collection<Organization>> maker2TakerEntry = (Map.Entry<Organization, Collection<Organization>>) it.next();
				Organization makerOrg = (Organization) maker2TakerEntry.getKey();
				Collection<Organization> takerOrgs = maker2TakerEntry.getValue();

				int noWarmUpTrades = 0;
				for ( Organization takerOrg : takerOrgs )
				{
					if ( noWarmUpTrades == maxWarmupTrades )
					{
						break;
					}


                    if ( ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() )
                    {
                        log.warn("DisplayOrderWarmupC: Warming up for spaces display order.");
                        doDisplayOrderWarmupSpaces(makerOrg, takerOrg, ccyPair, true);
                        doDisplayOrderWarmupSpaces(makerOrg, takerOrg, ccyPair, false);
                    }
                    else
                    {
                        log.warn("DisplayOrderWarmupC: Warming up for Non-Spaces display order.");
                        doDisplayOrderWarmup(makerOrg, takerOrg, ccyPair, true);
                        doDisplayOrderWarmup(makerOrg, takerOrg, ccyPair, false);
                    }

                    noWarmUpTrades++;

				}
				// REPEATE THIS FOR EVERY MAKER
			}

			Thread.sleep(100l);
		}
		catch ( Exception e )
		{
			log.error("DisplayOrderWarmupC. Exception occured during display order warmup - ", e);
			throw e;
		}
		finally
		{
			//CLEAN-UP AFTER THE WARMUP IS COMPLETED
			removeWarmupObjectsFromCache();
            // remove the warmup metaspace;
            if ( ConfigurationFactory.getServerMBean().isIntegralSpacesEnabled() )
                Metaspaces.getInstance().shutdownMetaspace(PersistenceConstants.WARMUP);
			log.info("DisplayOrderWarmupC: Warmup completed. Time Taken - " + (System.currentTimeMillis() - start));
		}
		RuntimeFactory.getServerRuntimeMBean().setServerWarmingUp(false);
		return null;
	}

	/**
	 * 6. PLACE ORDER FROM MAKER ORG.
	 * 6.1 MARK ORDER AS WARMUP ORDER
	 * 7. LIFT IT USING ITS TAKER ORG
	 * 7.1 MARK ORDER AS WARMUP ORDER
	 * @param makerOrg
	 * @param takerOrg
	 * @throws Exception
	 */
	private boolean doDisplayOrderWarmup( Organization makerOrg, Organization takerOrg, CurrencyPair ccyPair, boolean isBuy ) throws Exception
	{
		Order makerOrder = null;
		Request makerRequest = null;
		TradeRequest takerRequest = null;

		try
		{
			log.info("DisplayOrderWarmupC:Starting display order warmup for " + makerOrg.getShortName() + "->" + takerOrg.getShortName());

			User makerUser = getUserForOrg(makerOrg);
			if ( makerUser == null )
			{
				return false;
			}

            //BID = BUY, OFFER = SELL
			makerRequest = createLimitRequest(makerUser, takerOrg, ccyPair, FXLegDealingPrice.CCY1, 1000, 1.3, isBuy? DealingPrice.BID : DealingPrice.OFFER);
			if ( makerRequest == null )
			{
				return false;
			}

			ISCommonUtilC.setAsWarmUpObject(makerRequest);
            makerRequest.setNotes(new StringBuilder().append(System.nanoTime()).append("##").append(makerRequest.getUser().getFullName()).toString());
			boolean success = sendOrderRequest( makerUser, makerRequest);
			if ( success )
			{
				Thread.sleep(100l);
				makerOrder = OrderFactory.lookupOrder(makerRequest);
				User takerUser = getUserForOrg(takerOrg);
				if ( makerOrder == null || takerUser == null )
				{
					return false;
				}

				OrderBook book = OrderBookCacheC.getInstance().getOrderBook(makerOrder);
				int orderBookIndex = OrderBookCacheC.getInstance().getOrderBookIndex(book);
				if ( orderBookIndex == -1 )
				{
					return false;
				}
                //OFFER = BUY, BID = SELL
				takerRequest = getTradeRequest(makerOrg, makerOrder, orderBookIndex, takerUser, takerOrg, ccyPair, FXLegDealingPrice.CCY1, isBuy ? DealingPrice.BID : DealingPrice.OFFER, 1000, 1.3);
				if ( takerRequest == null )
				{
					return false;
				}
				ISCommonUtilC.setAsWarmUpObject(takerRequest);
                 /*
                 * The following statement will return doing nothing since server is not logged in.
                 * This has been introduced just to shave off 6-7ms.
                 */
                try {
                    RequestServiceC.getRequestService().quoteAccepted(takerRequest);
                } catch (Exception ex) {
                    log.error("DisplayOrderWarmupC.Exception occurred while calling RequestService.quoteAccepted() for Display order warmup of " +
                            makerOrg.getShortName() + " -> " + takerOrg.getShortName() +
                            ". This exception is not critical and this call for introduced to warmup the call for any runtime optimizations.", ex);
                }
                AdaptorTradeManager.getInstance().addTradeRequest(takerRequest);
				ResponseMessage responseMessage = OAHandlerFactory.getInstance().getOrderHandler().quoteAccepted(takerRequest);
				if ( responseMessage != null && MessageStatus.FAILURE.equals(responseMessage.getStatus()) )
				{
					log.warn("Warmup Lift Order failed for " + makerOrg.getShortName() + " -> " + takerOrg.getShortName());
				}
				Thread.sleep(200l);
				return true;

			}

			else
			{
				if ( log.isDebugEnabled() )
				{
					log.debug("Order submission could not be submitted order" + makerOrg.getShortName() + " -> " + takerOrg.getShortName());
				}
				return true;
			}

		}
		catch ( Exception e )
		{
			log.error("DisplayOrderWarmupC.Exception occurred during display order warmup of " + makerOrg.getShortName() + " -> " + takerOrg.getShortName(), e);
			return false;
		}
		finally
		{
			if ( makerOrder != null )
			{
				OrderBookCacheC.getInstance().cancelOrder(makerOrder, true);
			}
			if ( takerRequest != null )
			{
				String liftOrderID = takerRequest.getTradeId() + "ORD" + '-' + takerRequest.getTradeId();
				Request request = HandlerCacheC.getHandlerCache().getRequest(liftOrderID);
				if ( request != null )
				{
					Order takerOrder = OrderFactory.lookupOrder(request);
					if ( takerOrder != null )
					{
						OrderBookCacheC.getInstance().cancelOrder(takerOrder, true);
					}
				}
                AdaptorTradeManager.getInstance().removeTrade(takerRequest.getTradeId());
			}
		}

	}

	private User getUserForOrg( Organization org )
	{
		try
		{

			User user = WarmUpTradeUtilC.getInstance().getUser(org);
			IdcSessionContext ctx = IdcSessionManager.getInstance().getSessionContext(user);
			IdcSessionManager.getInstance().setSessionContext(ctx);
			return user;
		}
		catch ( Exception e )
		{
			return null;
		}

	}

	public Request createLimitRequest( User makeUser, Organization takerOrg, CurrencyPair ccyPair, String dealtCurrencyProperty, double dealtAmount, double rate, int bidOfferMode )
	{
		try
		{
			Request request = DealingFactory.newRequest();

			request.setRequestClassification(ISUtilImpl.getInstance().getRequestClassification(ISCommonConstants.MAKEPRICE_CREATE_TYPE));
			request.setChannel(ISUtilImpl.getInstance().getExtSys("DirectFXTrader"));
			request.setUser(makeUser);
			request.setOrganization(makeUser.getOrganization());
			if ( makeUser.getDefaultDealingEntity() == null )
			{
				log.info("DisplayOrderWarmupC.getTradeRequest : No default legal entity set for maker org " + makeUser.getOrganization().getShortName());

				return null;
			}
			request.setCounterparty(makeUser.getDefaultDealingEntity());
			request.getToOrganizations().add(takerOrg);

			FXLegDealingPrice newReqPrice = FXDealingFactory.newFXLegDealingPrice();
			if ( ISTransactionManager.isTransactionOn() )
			{
				newReqPrice = (FXLegDealingPrice) ISTransactionManager.getRegisteredObject(newReqPrice);
			}
			newReqPrice.setName(FXLegDealingPrice.SINGLE_LEG);
			newReqPrice.setDealtCurrencyProperty(dealtCurrencyProperty);

			if ( dealtCurrencyProperty.equals(FXLegDealingPrice.CCY1) )
			{
				newReqPrice.setDealtCurrency(ccyPair.getBaseCurrency());
				newReqPrice.setSettledCurrency(ccyPair.getVariableCurrency());
			}
			else
			{
				newReqPrice.setDealtCurrency(ccyPair.getVariableCurrency());
				newReqPrice.setSettledCurrency(ccyPair.getBaseCurrency());
			}
			newReqPrice.setDealtAmount(dealtAmount);
			newReqPrice.setBidOfferMode(bidOfferMode);
			newReqPrice.setTenor(Tenor.SPOT_TENOR);

			FXDealingPriceElement dpe = FXDealingFactory.newFXDealingPriceElementDependent();
			FXPrice price = FXPriceFactory.newFXPrice();
			FXRate fxRate = FXFactory.newFXRate(ccyPair.getBaseCurrency(), ccyPair.getVariableCurrency(), QuoteConventionUtilC.getInstance().getDefault());
			fxRate.setSpotRate(rate);
			if ( bidOfferMode == DealingPrice.BID )
			{
				price.setBidFXRate(fxRate);
			}
			else
			{
				price.setOfferFXRate(fxRate);
			}
			dpe.setPrice(price);
			newReqPrice.setPriceElement(dpe);

			request.setRequestPrice(FXLegDealingPrice.SINGLE_LEG, newReqPrice);
			request.getRequestAttributes().setMessageId(System.nanoTime() + "");
			String orderId = ISFactory.getInstance().getISOrderIdFacadeC().getID("IS");
			request.setOrderId(orderId);
			request.setTimeInForce(TimeInForce.GTC);
			TransactionIdFacadeC trasacFacade = (TransactionIdFacadeC) request.getFacade(TransactionIdFacade.FACADE_NAME);
			String transactionId = trasacFacade.newTransactionId(request.getChannel().getName());
			request.setTransactionID(transactionId);
			return request;
		}
		catch ( Exception e )
		{
			return null;
		}

	}

	private boolean sendOrderRequest( User user, Object request )
	{
		WorkflowMessage msg = MessageFactory.newWorkflowMessage();
		msg.setMessageId(0);
		msg.setSender( user );
		msg.setEvent(MessageEvent.CREATE);
		msg.setTopic(CommonAdaptorFrameworkConstants.MSG_TOPIC_REQUEST);
		msg.setParameterValue(CommonAdaptorFrameworkConstants.MESSAGE_HANDLER, new MessageHandler() {

			public Message handle( Message message ) throws IdcException
			{
				LogFactory.getLog(this.getClass()).info("Message received - " + message);
				return null;
			}
		});
		msg.setParameterValue(CommonAdaptorFrameworkConstants.WF_PARAM_ORDER_TYPE, "LIMIT");
		msg.setParameterValue(CommonAdaptorFrameworkConstants.WF_PARAM_LP_CROSSING_ENABLED, true);
		long nanoTime = System.nanoTime();
		msg.setParameterValue(CommonAdaptorFrameworkConstants.MESSAGE_ID, nanoTime);
		msg.setParameterValue(CommonAdaptorFrameworkConstants.WF_PARAM_TRADE_CHANNEL, CommonAdaptorFrameworkConstants.TRADER_TRADE_CHANNEL);
		msg.setObject(request);
		ISUtilImpl.getInstance().setAsWarmUpObject(msg);
		try
		{
			WorkflowMessage respMessage = (WorkflowMessage) ServiceFactory.getOrderRequestService().process(msg).getReplyMessage();
			if ( respMessage != null && !MessageStatus.FAILURE.equals(respMessage.getStatus()) )
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		catch ( Exception e )
		{
			log.error("DisplayOrderWarmupC.sendOrderRequest: Error -", e);
			return false;
		}

	}

	public TradeRequest getTradeRequest( Organization makerOrg, Order makerOrder, int orderBookIndex, User takerUser, Organization takerOrg, CurrencyPair ccyPair, String dealtCurrencyProperty, int acceptedbomode, double amount, double rate )
	{
		try
		{
			ISMBean isMbean = ISFactory.getInstance().getISMBean();
			TradeRequest tradeRequest = MessageFactory.newTradeRequest();
			tradeRequest.setRequestId("OADisplayOrderWarmup");
			tradeRequest.setState(TradeRequest.OPEN);
			tradeRequest.setUserShortName(takerUser.getShortName());
			String providerShortName = makerOrg.getShortName();
			tradeRequest.setServerId(isMbean.getServerId() + "@" + providerShortName);
			tradeRequest.setProviderShortName(providerShortName);
			tradeRequest.setOrgShortName(takerOrg.getShortName());
			if ( takerUser.getDefaultDealingEntity() == null )
			{
				log.info("DisplayOrderWarmupC.getTradeRequest : No default legal entity set for taker org " + takerOrg.getShortName());
				return null;
			}
			tradeRequest.setLeShortName(takerUser.getDefaultDealingEntity().getShortName());
			tradeRequest.setBaseCcy(ccyPair.getBaseCurrency().getShortName());
			tradeRequest.setVariableCcy(ccyPair.getVariableCurrency().getShortName());
			tradeRequest.setTradeId("FXIWarmup" + System.currentTimeMillis());
			tradeRequest.setAmount(amount);

			if ( dealtCurrencyProperty.equals(FXLegDealingPrice.CCY1) )
			{
				tradeRequest.setDealtCcy(ccyPair.getBaseCurrency().getShortName());
			}
			else
			{
				tradeRequest.setDealtCcy(ccyPair.getVariableCurrency().getShortName());
			}
			if ( acceptedbomode == FXLegDealingPrice.OFFER )
			{
				double offerRate = rate;
				tradeRequest.setRate(offerRate);
				tradeRequest.setProperty("BaseRate", offerRate);
				tradeRequest.setBuySell(TradeRequest.BUY);
			}
			else
			{
				double bidRate = rate;
				tradeRequest.setRate(bidRate);
				tradeRequest.setProperty("BaseRate", bidRate);
				tradeRequest.setBuySell(TradeRequest.SELL);
			}

			tradeRequest.setProperty(ISConstantsC.TRADE_TRANSACTIONID, tradeRequest.getTradeId() + "RE");
			tradeRequest.setProperty(OAWorkflowFunctorC.TAKER_ORDERID, tradeRequest.getTradeId() + "ORD");
			tradeRequest.setProperty(OAWorkflowFunctorC.TAKER_ORDER_EFFECTIVETIME, System.currentTimeMillis());
			tradeRequest.setProperty(OAWorkflowFunctorC.TAKER_ORDER_TIMEINFORCE, TimeInForce.GTC);

			StringBuilder pqId = new StringBuilder(100);
			pqId.append(-1).append('#');
			pqId.append(makerOrder.getEntityDescriptor().getUserReference().getObjectId()).append('#');
			pqId.append("BID-").append('#');
			pqId.append("OFFER-" + makerOrder.getOrderId()).append('#');
			pqId.append("BTR-").append('#');
			pqId.append("OTR-").append('#');
			pqId.append(orderBookIndex);
			tradeRequest.setProviderQuoteId(pqId.toString());
			return tradeRequest;
		}
		catch ( Exception e )
		{
			return null;
		}
	}

	/**
	 * 1. GET THE LIST OF ORGANIZATION WHO CAN PLACE DISPLAY ORDERS
	 * 2. GET THE LIST OF ALL THE POTENTIAL TAKERS
	 * 3. CHECK IF PREFERRED TAKERS LIST IS SPECIFIED
	 * @return
	 */
	private Map<Organization, Collection<Organization>> getMakers2TakersMap()
	{
		Map<Organization, Collection<Organization>> makers2Takers = new HashMap<Organization, Collection<Organization>>();
		Collection<Organization> makers = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
		Collection preferredMakersList = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getPreferredMakersList();

		for ( Organization maker : makers )
		{
			if ( (preferredMakersList != null && !preferredMakersList.contains(maker.getShortName())) || !maker.isActive() )
			{
				continue;
			}

			Collection<Organization> takers = maker.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);

			if ( takers != null && takers.size() > 0 )
			{
				Collection preferredTakersList = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getPreferredTakerList(maker.getName());
				ArrayList<Organization> preferredTakers = new ArrayList<Organization>();

				for ( Organization taker : takers )
				{
					if ( (preferredTakersList != null && !preferredTakersList.contains(taker.getShortName())) || !taker.isActive() )
					{
						continue;
					}
					preferredTakers.add(taker);
				}
				if ( preferredTakers.size() > 0 )
				{
					makers2Takers.put(maker, preferredTakers);
				}
			}
		}
		return makers2Takers;
	}

	/**
	 * Removes the warmupRequest from cache
	 */
	public void removeWarmupObjectsFromCache()
	{
		try
		{
			BaseWorkflowHandlerC handler = new BaseWorkflowHandlerC();
			Set<RequestHolder> reqHolderColl = new HashSet<RequestHolder>(HandlerCacheC.getHandlerCache().getCacheRFQHandlers().values());
			for ( RequestHolder reqHolder : reqHolderColl )
			{
				Request request = reqHolder.getRequest();
				try
				{
					if ( ISUtilImpl.getInstance().isWarmUpObject(request) )
					{
						handler.removeCache(request);
						Order order = OrderFactory.lookupOrder(request);
						if ( order != null )
						{
							OrderBookCacheC.getInstance().cancelOrder(order, true);
						}
						log.warn("DisplayOrderWarmupC.removeWarmupObjectsFromCache: Removed Request:" + request.getOrderId() + " from the cache.");
					}
				}
				catch ( Exception e )
				{
					log.error("DisplayOrderWarmupC.removeWarmupObjectsFromCache: Exception while removing request:" + request.getOrderId() + " from Cache.Exception:", e);
				}
			}

			LinkedEntryMap<String, Trade> trades = ISTradeCache.getInstance().getTradeMap();
			List<String> keysToBeRemoved = new ArrayList<String>();
			Iterator<String> keys = trades.keyIterator();
			while ( keys.hasNext() )
			{
				String tid = keys.next();
				Trade trd = trades.get(tid);
				if ( ISUtilImpl.getInstance().isWarmUpObject(trd) )
				{
					keysToBeRemoved.add(tid);
				}
			}
			for ( String key : keysToBeRemoved )
			{
				trades.remove(key);
			}
            FXESPWorkflowCache.clearWarmUpObjects();
		}
		catch ( Exception e )
		{
			log.error("DisplayOrderWarmupC.removeWarmupObjectsFromCache: Exception - ", e);
		}
		try
		{
			EntityPropertyMap.getInstance().getEntityPropertyMap().clear();
		}
		catch ( Exception ex )
		{
			log.error("DisplayOrderWarmupC.removeWarmupObjectsFromCache: Exception - ", ex);
		}
	}

    private boolean doDisplayOrderWarmupSpaces( Organization makerOrg, Organization takerOrg, CurrencyPair ccyPair, boolean isBuy ) throws Exception
    {
        Order makerOrder = null;
        SingleLegOrder makerRequest = null;
        TradeRequest takerRequest = null;

        try
        {
            log.info("DisplayOrderWarmupSpaces:Starting display order warmup for " + makerOrg.getShortName() + "->" + takerOrg.getShortName());

            User makerUser = getUserForOrg(makerOrg);
            if ( makerUser == null )
            {
                return false;
            }


            makerRequest = getSingleLegOrder(makerUser, takerOrg, ccyPair, 1.3, isBuy);
            if ( makerRequest == null )
            {
                return false;
            }

            boolean success = sendOrderRequest(makerUser, makerRequest);
            if ( success )
            {
                Thread.sleep(100l);
                makerOrder = FXOrderCache.get(makerRequest.get_id());
                User takerUser = getUserForOrg(takerOrg);
                if ( makerOrder == null || takerUser == null )
                {
                    return false;
                }

                OrderBook book = OrderBookCacheC.getInstance().getOrderBook(makerOrder);
                int orderBookIndex = OrderBookCacheC.getInstance().getOrderBookIndex(book);
                if ( orderBookIndex == -1 )
                {
                    return false;
                }
                //OFFER = BUY, BID = SELL
                takerRequest = getTradeRequest(makerOrg, makerOrder, orderBookIndex, takerUser, takerOrg, ccyPair, FXLegDealingPrice.CCY1, isBuy? DealingPrice.BID : DealingPrice.OFFER, 1000, 1.3);
                if ( takerRequest == null )
                {
                    return false;
                }
                ISCommonUtilC.setAsWarmUpObject(takerRequest);
                takerRequest.setProperty(ISCommonConstants.SPACES_ENABLED, true);
                takerRequest.setValueDate( new Date());
                /*
                 * The following statement will return doing nothing since server is not logged in.
                 * This has been introduced just to shave off 6-7ms.
                 */
                try {
                    RequestServiceC.getRequestService().quoteAccepted(takerRequest);
                } catch (Exception ex) {
                    log.error("DisplayOrderWarmupC.Exception occurred while calling RequestService.quoteAccepted() for Display order warmup of " +
                            makerOrg.getShortName() + " -> " + takerOrg.getShortName() +
                            ". This exception is not critical and this call for introduced to warmup the call for any runtime optimizations.", ex);
                }
                AdaptorTradeManager.getInstance().addTradeRequest(takerRequest);
                ResponseMessage responseMessage = OAHandlerFactory.getInstance().getOrderHandler().quoteAccepted(takerRequest);
                if ( responseMessage != null && MessageStatus.FAILURE.equals(responseMessage.getStatus()) )
                {
                    log.warn("DisplayOrderWarmupSpaces.Warmup Lift Order failed for " + makerOrg.getShortName() + " -> " + takerOrg.getShortName());
                }
                Thread.sleep(200l);
                return true;

            }

            else
            {
                if ( log.isDebugEnabled() )
                {
                    log.debug("DisplayOrderWarmupSpaces: Order submission could not be submitted order" + makerOrg.getShortName() + " -> " + takerOrg.getShortName());
                }
                return true;
            }

        }
        catch ( Exception e )
        {
            log.error("DisplayOrderWarmupSpaces.Exception occured during display order warmup of " + makerOrg.getShortName() + " -> " + takerOrg.getShortName(), e);
            return false;
        }
        finally
        {
            if ( makerOrder != null )
            {
                OrderBookCacheC.getInstance().cancelOrder(makerOrder, true);
            }
            if ( takerRequest != null )
            {
                String liftOrderID = takerRequest.getTradeId() + "ORD" + '-' + takerRequest.getTradeId();

                Order takerOrder = FXOrderCache.get(liftOrderID);
                if ( takerOrder != null )
                {
                    log.info("DisplayOrderWarmupSpaces Cancelling takerOrder " + liftOrderID);
                    OrderBookCacheC.getInstance().cancelOrder(takerOrder, true);
                }
                AdaptorTradeManager.getInstance().removeTrade(takerRequest.getTradeId());
            }
        }

    }

    private SingleLegOrder getSingleLegOrder( User user, Organization fi, CurrencyPair ccyPair, double rate, boolean isBuy )
    {
        SingleLegOrder orderRequest = DealingModelFactory.getInstance().newOrderRequest();
        orderRequest.setNamespace(user.getNamespace());
        orderRequest.setWarmUpObject(true);
        orderRequest.setUser(user);
        orderRequest.setCurrencyPair(ccyPair);
        orderRequest.setDealtCurrency(ccyPair.getBaseCurrency());
        orderRequest.setType(com.integral.model.dealing.OrderRequest.Type.LIMIT);
        orderRequest.setChannel("DNET");
        orderRequest.setOrganization(fi);
        orderRequest.setTimeInForce(com.integral.model.dealing.TimeInForce.GTC);
        FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getDefault();
        FXRateBasis rateBasis = fxRateConvention.getFXRateBasis(orderRequest.getCurrencyPair());
        orderRequest.setFxRateBasis(rateBasis);
        com.integral.model.dealing.OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        if(isBuy) {
            requestLeg.setBuySellMode(OrderRequest.RequestLeg.BuySellMode.BUY);
        } else {
            requestLeg.setBuySellMode(OrderRequest.RequestLeg.BuySellMode.SELL);
        }
        requestLeg.setTenor(FixConstants.SPOT_TENOR.getName());
        requestLeg.setAmount(1000);
        requestLeg.setSpotRate(rate);
        
        long oid = id.incrementAndGet();
        orderRequest.set_id("WRMDO"+oid);
        orderRequest.setTransactionId("WRMDOR"+oid);
        
        return orderRequest;

    }
}
