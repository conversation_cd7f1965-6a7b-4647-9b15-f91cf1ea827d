package com.integral.adaptor.order.warmup;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.warmup.tradingvenue.IMTPWarmup;
import com.integral.adaptor.order.warmup.tradingvenue.WarmupIMTPMessageListener;
import com.integral.adaptor.order.warmup.tradingvenue.WarmupVenue;
import com.integral.finance.counterparty.Counterparty;
import com.integral.finance.counterparty.CounterpartyUtilC;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.ExecutionFlags;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.fix.client.FixConstants;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.DirectedOrderCommonMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.message.directed.orders.DirectedOrderMessageTranslatorUtil;
import com.integral.is.message.directed.orders.OrderStatus;
import com.integral.is.message.directed.orders.request.NewOrderRequest;
import com.integral.is.message.directed.orders.responses.OrderFillResponse;
import com.integral.is.message.directed.orders.responses.OrderRejectResponse;
import com.integral.is.message.directed.orders.responses.RejectReason;
import com.integral.is.message.directed.orders.serializer.DirectedOrderSerializer;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.order.configuration.OMSConfigurationFactory;
import com.integral.is.spaces.fx.esp.cache.FXESPWorkflowCache;
import com.integral.is.spaces.fx.esp.factory.DealingModelFactory;
import com.integral.is.spaces.fx.handler.DirectedOrderResponseHandler;
import com.integral.is.spaces.fx.handler.DirectedOrderVerificationHandler;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageEvent;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.model.DirectedOrderInfo;
import com.integral.model.dealing.BidOfferMode;
import com.integral.model.dealing.OrderMatchRequest;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.oms.spaces.fx.esp.cache.FXOrderCache;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.tradingvenue.REXConfig;
import com.integral.tradingvenue.TradingVenueClassificationEnums;
import com.integral.tradingvenue.TradingVenueEntity;
import com.integral.tradingvenue.TradingVenueRelationShip;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * Created by pranabdas on 12/4/14.
 */
public class DirectedOrderWarmupManager extends OAWarmupTradeManagerC{

    protected Log log = LogFactory.getLog( this.getClass() );
    private static final int NUMBER_THREADS = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getWarmupThreadCount();
    private static final String SEPARATOR = ".";
    private Set<Organization> customerList = null;
    private CountDownLatch warmupCountLatch;
    private long warmupExecutionTimeInMS = 120000;  // do a warmup for 2 minutes
    private final int NUMBER_FI_TV_CCYPAIR_COMBINATIONS = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDirectedOrderWarmupOperationCount();
    private static final String WARMUP_HOST = "localhost";
    private static final String SELECTOR = "VENUE.WARMUP.OUTPUT.DOC";
    private static final int MAX_NUM_TRIES = 20;
    private IMTPWarmup sender = null;
    private WarmupIMTPMessageListener listener = null;
	private AtomicLong id = new AtomicLong(1000l);
    
    public DirectedOrderWarmupManager() {
        setupTestData();
    }


    private void setupTestData() {
       // filtering of list can be done here if required.
        customerList = new HashSet<Organization>( ISFactory.getInstance().getISMBean().getVirtualServer().getOrganizations() );
    }

    
    private void startImtp()
    {
        String vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
    	String warmupId = vsName + SEPARATOR +  WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDirectedOrderWarmupImtpId();
        String selector = SELECTOR + SEPARATOR + vsName;
        int port = WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDirectedOrderWarmupPort();
		WarmupVenue messageReciever = new WarmupVenue((byte)4,selector);
        sender = null;
        listener = null;
        int numTries = 0;
        boolean started = false;
        while(numTries < MAX_NUM_TRIES && !started)
        {
        try {
	    sender = new IMTPWarmup(warmupId, WARMUP_HOST, port,messageReciever,(byte)4,selector);
	    listener = new WarmupIMTPMessageListener(warmupId, port,WARMUP_HOST, messageReciever , sender ,(byte)4,selector);
	    listener.start();
	    sender.connect();
		sender.activate();
		started = true;
		} 
        catch (Exception e1) {
			// TODO Auto-generated catch block
        	started = false;
        	numTries++;
        	port = port + 1;
			log.warn( "DirectedOrderWarmup :Unable to start IMTP warmup , Retrying with Port" + port);
		}
        
        }
        if(!started)
        {
        	sender = null;
            listener = null;
			log.warn( "DirectedOrderWarmup :Unable to start IMTP warmup ,Will continue");

        }
    }

    public void initiateStartUpTestRequests() {

        if( customerList != null && customerList.size() > 0 ){
            List<List<Organization>> organizationPerThreadArray = getOrganizationsPerThread( NUMBER_THREADS );
            int noThreads = organizationPerThreadArray.size();
            warmupCountLatch = new CountDownLatch( noThreads );
            log.info("DirectedOrderWarmup : Number of warmup thread: " + noThreads + " provisioned Orgs count: " + customerList.size() + " Warmup Execution time: "
                    + warmupExecutionTimeInMS + " combinations count:" + NUMBER_FI_TV_CCYPAIR_COMBINATIONS );
            //warming up the mbeans
            warmupMBeans();
            startImtp();
            ExecutorService executorService = Executors.newFixedThreadPool( noThreads );
            List<DOWarmupRunnable> warmupTasks = new ArrayList<DOWarmupRunnable>( noThreads );
            for ( int i = 0; i < noThreads; i++ ) {
                DOWarmupRunnable task =  new DOWarmupRunnable( i, ( NUMBER_FI_TV_CCYPAIR_COMBINATIONS / noThreads ), warmupCountLatch, organizationPerThreadArray.get( i ) ,sender);
                warmupTasks.add( task );
                executorService.submit( task );
            }

            try {
                boolean status = warmupCountLatch.await( warmupExecutionTimeInMS, TimeUnit.MILLISECONDS );
                if ( !status ){
                    log.warn( "DirectedOrderWarmup : Warmup stopped as it has exceeded the maximum time allowed" );
                }
            } catch ( InterruptedException ex ){
                log.error( "DirectedOrderWarmup : Thread interrupted, warmup will be stopped", ex );
            }

            for( DOWarmupRunnable task : warmupTasks ){
                task.stop();
            }
            executorService.shutdownNow();
            while ( !executorService.isTerminated() ){
                try {
                    log.info( "DirectedOrderWarmup : waiting for threads to complete" );
                    Thread.sleep( 1000 );
                } catch ( InterruptedException e ){
                    log.error( "DirectedOrderWarmup : ExecutorService for warmup interrupted" );
                }
            }
            log.info( "DirectedOrderWarmup : Exiting initiateStartUpTestRequests after warmup" );
            if(listener != null)
				try {
					listener.stop();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					log.warn( "DirectedOrderWarmup :Unable to stop IMTP warmup , Will Continue ", e );

				}
        }

    }

    private void warmupMBeans(){
        REXConfig.getInstance().isVenueOnly();
    }

    private class DOWarmupRunnable implements Runnable{
        private final int threadIndex;
        private final int count;
        private final CountDownLatch latch;
        private final List<Organization> custOrgList;
        boolean stop;
        private final IMTPWarmup warmup;

        public DOWarmupRunnable( int index, int count, CountDownLatch warmupCountLatch,
        		List<Organization> organizations,
        		IMTPWarmup warmup) {
            this.threadIndex = index;
            this.count = count;
            this.latch = warmupCountLatch;
            this.custOrgList = organizations;
            this.warmup = warmup;
            this.stop = false;
        }

        @Override
        public void run(){
            String baseCurrency = null;
            String termCurrency = null;
            Thread.currentThread().setName( "DOWARMUP-" + threadIndex );
            int verificationCount = ( 90 * count ) / 100;
            int rejectionCount = count - verificationCount;
            StringBuilder sb = new StringBuilder( "Will warmup for Customer " );
            for ( Organization organization : custOrgList ){
                sb.append( organization.getShortName() ).append( "," );
            }
            log.info( sb.toString() );
            log.info( "Starting warmup Thread: " + threadIndex + " will attempt to verify: " + verificationCount + " reject: " + rejectionCount );
            ResponseTuple tuple = new ResponseTuple();
            int myCount = count;
            boolean rejectNext;
            long startTime = System.currentTimeMillis();
            try{
                while ( myCount > 0 && !stop ){
                    for ( Organization custOrg : custOrgList ){
                        if ( stop ){
                            break;
                        }
                        Collection<TradingVenueRelationShip> tradingVenueRelations = ISUtilImpl.getInstance().getTradingVenueRelationShips( custOrg );
                        // any customer is supposed to be part of only one RiskNet and only one Clob
                        for ( TradingVenueRelationShip tradingVenueRelationship : tradingVenueRelations ){
                            if ( stop ){
                                break;
                            }
                            Organization tvOrg = tradingVenueRelationship.getTvOrg();
                            TradingVenueEntity tradingVenue = tvOrg.getTradingVenueOrgFunction().getTradingVenue();
                            Collection<CurrencyPair> currencyPairs = tradingVenue.getSupportedCurrencyPairs().getCurrencyPairs();
                            LegalEntity custCMLE =tradingVenueRelationship.getClearingMember();
                            for ( CurrencyPair currencyPair : currencyPairs ){
                                if ( myCount < 1 || stop ){
                                    stop = true;
                                    break;
                                }
                                User user = WarmUpTradeUtilC.getInstance().getUser( custOrg );
                                boolean status = false;
                                try{
                                    baseCurrency = currencyPair.getBaseCurrency().getName();
                                    log.info( new StringBuilder(400).append("DOWM:Starting directed order warmup for cust=").append(custOrg.getShortName()).append(" tv=").
                                            append(tradingVenue.getShortName()).append(" ccyPair=").append(currencyPair.getDisplayName()).toString() );
                                    termCurrency = currencyPair.getVariableCurrency().getName();

                                    if(tradingVenue.getTvType() == TradingVenueClassificationEnums.TvType.BILATERAL){
                                        // Bilateral Workflow
                                        Collection<LegalEntity> custLEs = custOrg.getLegalEntities();
                                        rejectNext = myCount < rejectionCount;
                                        SingleLegOrder orderRequest = warmUpOrderSubmission( tradingVenue, custOrg, getDefaultDealingEntity(user), user, currencyPair );
                                        if ( orderRequest == null ) continue;
                                        byte[] req = warmupOrderRequestSerializers( orderRequest, tvOrg, custOrg, user, currencyPair );
                                        warmupOrderResponseSerializers( orderRequest, tvOrg, custOrg, user, currencyPair );
                                        status = warmupOrderResponse( tradingVenue, custOrg, user, null, orderRequest, rejectNext, tuple );
                                        if ( warmup != null ) warmup.send( req );
                                    }
                                    else{
                                        Collection<LegalEntity> clearingMembers = tradingVenue.getClearingMembers();
                                        // iterating over rest of the customers in the same venue
                                        for(LegalEntity cptyCMLE : clearingMembers){
                                            if ( myCount < 1 ){
                                                stop = true;
                                                break;
                                            }
                                            if (!custCMLE.isSameAs(cptyCMLE) && !CounterpartyUtilC.isValidRelationBetweenLEs(custCMLE, cptyCMLE)) continue;
                                            Collection<LegalEntity> custLEs = custOrg.getLegalEntities();
                                            for(LegalEntity custLE : custLEs){
                                                try {
                                                    if (myCount < 1) {
                                                        stop = true;
                                                        break;
                                                    }

                                                    if (!CounterpartyUtilC.isValidRelationBetweenLEs(custLE, custCMLE)) continue;

                                                    rejectNext = myCount < rejectionCount;

                                                    log.info(new StringBuilder(400).append("DOWM:Warming up for combination: custOrg=").append(custOrg.getShortName()).append(", custLE=")
                                                            .append(custLE.getShortName()).append(", tv=").append(tradingVenue.getShortName()).append(", cptyCMLE=").append(cptyCMLE.getShortName())
                                                            .append(", custCMLE=").append(custCMLE.getShortName()).append(", ccyPair=").append(currencyPair.getDisplayName()).toString());
                                                    SingleLegOrder orderRequest = warmUpOrderSubmission(tradingVenue, custOrg, custLE, user, currencyPair);
                                                    if (orderRequest == null) continue;
                                                    byte[] req = warmupOrderRequestSerializers(orderRequest, tvOrg, custOrg, user, currencyPair);
                                                    warmupOrderResponseSerializers(orderRequest, tvOrg, custOrg, user, currencyPair);
                                                    status = warmupOrderResponse(tradingVenue, custOrg, user, cptyCMLE, orderRequest, rejectNext, tuple);
                                                    if (warmup != null) warmup.send(req);
                                                }
                                                catch ( Exception ex ){
                                                    log.warn( "DOWM:Error running the warmup for venue: " + tvOrg.getShortName() + " cptya: " + custOrg.getShortName() + ", basecurrency: " + baseCurrency + ", termcurrency" + termCurrency, ex );
                                                }
                                                finally{
                                                    //if ( status ){
                                                    //    myCount--;
                                                    //}
                                                }
                                            }
                                        }
                                    }
                                }
                                catch ( Exception ex ){
                                    log.warn( "DOWM:Error running the warmup for cptyb: " + tvOrg.getShortName() + " cptya: " + custOrg.getShortName() + ", basecurrency: " + baseCurrency + ", termcurrency" + termCurrency, ex );
                                }
                                finally{
                                    if ( status ){
                                        myCount--;
                                    }
                                }
                            }
                        }
                            log.info( "DOWM:Completed warmup for FI=" + custOrg.getShortName() );

                    }
                    stop = true;
                }
            }
            catch( Exception ex ){
                log.warn( "DOWARMUP-" + threadIndex + " interrupted stopping now", ex );
            }
            log.info( "DOWARMUP-" + threadIndex + " executed: " + ( count - myCount ) + " verification count: " +
                    tuple.getVerificationCount().get() + " rejection count: " + tuple.getRejectionCount().get() + " failures: " + tuple.getFailureCount() + " time taken in secs: " + ( System.currentTimeMillis() - startTime ) / 1000 );
            latch.countDown();
        }

        public void stop(){
            log.info("Stop warmup task DOWARMUP-" + threadIndex );
            stop = true;
        }
    }

    public LegalEntity getDefaultDealingEntity(User user){
        Counterparty cpty;
        cpty = user.getDefaultDealingEntity();
        if ( cpty != null ) {
            if ( log.isDebugEnabled() ) {
                log.debug( "getDefaultLegalEntity User default dealing entity is " + cpty.getShortName() + "" +
                        " for user " + user.getShortName() );
            }
        }
        else {
            log.warn( "getDefaultLegalEntity User default dealing entity is null " +
                    " for user " + user.getShortName() );
        }

        if ( cpty == null ) {
            //This is called only in non sales dealer so taking organization from user
            cpty = user.getOrganization().getDefaultDealingEntity();
            if ( cpty != null ) {
                if ( log.isDebugEnabled() ) {
                    log.debug( "getDefaultLegalEntity Organization default dealing entity is " + cpty.getShortName() + "" +
                            " for user " + user.getShortName() );
                }
            }
            else {
                log.warn( "getDefaultLegalEntity Organization default dealing entity is null " +
                        " for user " + user.getShortName() );
            }
        }

        if ( cpty != null && cpty.isActive() ) {
            return ( LegalEntity ) cpty;
        }
        else {
            log.warn( "getDefaultLegalEntity LegalEntity " + cpty + " is either null or INACTIVE " );
        }
        return null;
    }

    private byte[] warmupOrderRequestSerializers( SingleLegOrder orderRequest, Organization tradingVenue, Organization cptyAOrg, User user, CurrencyPair ccyPair ){
        NewOrderRequest newOrderRequest = getNewOrderRequest( orderRequest, tradingVenue, cptyAOrg, user, ccyPair );
        log.info( "DOWARMUP-" + " warmupOrderRequestSerializers for tv=" + tradingVenue.getShortName() + ", fi=" + cptyAOrg.getShortName() + ", ccyPair=" + ccyPair.getName() );
        return DirectedOrderSerializer.serialize( newOrderRequest, (byte)DirectedOrderCommonMBean.getInstance().getAPIVersion(tradingVenue.getShortName()) );
    }

    private OrderFillResponse warmupOrderResponseSerializers( SingleLegOrder orderRequest, Organization tradingVenue, Organization cptyAOrg, User user, CurrencyPair ccyPair ){
        OrderFillResponse orderFillResponse = getOrderFillResponse( orderRequest, tradingVenue, cptyAOrg, user, ccyPair );
        byte[] msg = DirectedOrderSerializer.serialize( orderFillResponse, (byte)DirectedOrderCommonMBean.getInstance().getAPIVersion(tradingVenue.getShortName())  );
        DirectedOrderSerializer.deserialize( msg,  (byte)DirectedOrderCommonMBean.getInstance().getAPIVersion(tradingVenue.getShortName())  );
        log.info( "DOWARMUP-" + " warmupOrderResponseSerializers for tv=" + tradingVenue.getShortName() + ", fi=" + cptyAOrg.getShortName() + ", ccyPair=" + ccyPair.getName() );
        return orderFillResponse;
    }

    private OrderFillResponse getOrderFillResponse( SingleLegOrder orderRequest, Organization tradingVenue, Organization cptyAOrg, User user, CurrencyPair ccyPair ){
        OrderFillResponse fillResponse = new OrderFillResponse();
        fillResponse.setCustomerLE( cptyAOrg.getDefaultDealingEntity() );
        fillResponse.setCounterPartyLE( cptyAOrg.getDefaultDealingEntity() );
        OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        fillResponse.setSide( DirectedOrderMessageTranslatorUtil.getSide( requestLeg ) );
        fillResponse.setOrderId( orderRequest.get_id() );
        fillResponse.setReferenceOrderId( String.valueOf(id.incrementAndGet()) );
        fillResponse.setVenue(tradingVenue);
        fillResponse.setInstrument(ccyPair);
        fillResponse.setFillId( "123" );
        fillResponse.setFillQty( requestLeg.getAmount() );
        fillResponse.setRate( requestLeg.getSpotRate() );
        fillResponse.setCumQty( requestLeg.getAmount() );
        fillResponse.setRemainingQty( 0.0 );
        fillResponse.setCptyOrderId( orderRequest.get_id() );
        fillResponse.setAggressor( true );
        fillResponse.setTerminal( true );
        fillResponse.setStatus(OrderStatus.PARTIALLY_FILLED);
        fillResponse.setTimestamp( System.currentTimeMillis() - 100 );
        fillResponse.setResponseType( OrderFillResponse.OrderResponseType.ORDER_FILL);
        long[] eventTimes = new long[4];
        for(int i=0;i<3;i++){
            eventTimes[i] = System.currentTimeMillis()+i*100;
        }
        eventTimes[3] = System.currentTimeMillis();
        fillResponse.setEventTimes( eventTimes );
        return fillResponse;
    }

    private NewOrderRequest getNewOrderRequest( SingleLegOrder orderRequest, Organization tradingVenue, Organization cptyAOrg, User user, CurrencyPair ccyPair ){
        NewOrderRequest newOrderRequest = new NewOrderRequest();
        OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        LegalEntity clearingMemberLE = null;
        newOrderRequest.setWarmupObject( true );
        newOrderRequest.setCcyPair( ccyPair );
        newOrderRequest.setCustomerLE( orderRequest.getLegalEntity() );
        newOrderRequest.setClearingMemberLE( clearingMemberLE );
        newOrderRequest.setClearingMemberOrg( clearingMemberLE != null ? clearingMemberLE.getOrganization() : null );
        newOrderRequest.setBroker(clearingMemberLE != null ? clearingMemberLE.getShortName() : orderRequest.getLegalEntity().getShortName());

        newOrderRequest.setOrderId( orderRequest.get_id() );
        newOrderRequest.setOrderQty( orderRequest.getOrderAmount() );
        newOrderRequest.setOrderType(DirectedOrderMessageTranslatorUtil.getOrderType(orderRequest));
        newOrderRequest.setSide( DirectedOrderMessageTranslatorUtil.getSide( requestLeg ) );
        newOrderRequest.setVenue( tradingVenue );
        newOrderRequest.setTif( DirectedOrderMessageTranslatorUtil.getTimeInForce(orderRequest.getTimeInForce()));

        long nowMillis = System.currentTimeMillis();
        newOrderRequest.setTimestamp( nowMillis );
        DirectedOrderInfo directedOrderInfo = new DirectedOrderInfo();
        directedOrderInfo.setRequestSentTime(nowMillis);
        return newOrderRequest;
    }

    private BidOfferMode getSide(OrderRequest.RequestLeg requestLeg) {
        switch (requestLeg.getBuySellMode()) {
            case BUY:
                return BidOfferMode.BID;
            case SELL:
                return BidOfferMode.OFFER;
            default:
                log.error("Can not derive side for order: " + requestLeg.getBuySellMode().name());
                return null;
        }
    }

    private void clearCache( String orderID ){
        try{
            Order order1 = FXOrderCache.get( orderID );
            if ( order1 != null ){
                OrderBookCacheC.getInstance().cancelOrder( order1, true );
                FXOrderCache.remove( orderID );
                FXESPWorkflowCache.removeMatchRequest(orderID);
            }
        }
        catch(Exception ex){
            log.info( "DirectedOrderWarmupManager.clearCache : Issue clearing order cache for orderID=" + orderID );
        }
    }

    protected SingleLegOrder warmUpOrderSubmission( TradingVenueEntity tradingVenue, Organization cptyAOrg, LegalEntity custLE, User user, CurrencyPair ccyPair ) {
        StringBuilder sb = new StringBuilder( 200 );
        sb.append( "DOWM:ordSub: " ).append( ", tv=" ).append( tradingVenue.getShortName() );
        sb.append( ", cust=" ).append( cptyAOrg.getShortName() );
        sb.append(", custLE=").append(custLE.getShortName());
        sb.append(", ccyPair=").append(ccyPair.getDisplayName());
        OMSConfigurationFactory.getOMSConfig();
        OrderConfiguration.getInstance();

        initializeUserSession(user);//initialize trading user session

        WorkflowMessage msg = MessageFactory.newWorkflowMessage();
        msg.setMessageId( 0 );
        msg.setSender(user);
        msg.setEvent( MessageEvent.CREATE );
        msg.setTopic( FixConstants.MSG_TOPIC_REQUEST );
        ISUtilImpl.getInstance().setAsWarmUpObject( msg );
        SingleLegOrder order = getSingleLegOrder( user, user.getOrganization(), ccyPair );
        // Directed order parameters
        order.getExecutionInstructions().getRoutingInstruction().setTradingVenue( tradingVenue.getShortName() );
        TradingVenueClassificationEnums.FixingType venueType = tradingVenue.getFixingType();
        if(venueType == TradingVenueClassificationEnums.FixingType.CLOB){
            order.setExecutionFlags( order.getExecutionFlags() | ExecutionFlags.CLOB_DIRECTED_ORDER);
        }else{
        order.setExecutionFlags( order.getExecutionFlags() | ExecutionFlags.RISKNET_DIRECTED_ORDER);
        }
        order.setType( OrderRequest.Type.MARKET );

        msg.setObject(order);
        msg.setParameterValue("messageHandler", new WarmupResponseHandler());
        msg.setParameterValue( ISCommonConstants.WF_PARAM_TradingVenue, tradingVenue.getShortName() );
        msg.setParameterValue( ISConstantsC.ACCEPTANCE_COUNTERPARTYA, custLE.getShortName());
        com.integral.is.finance.dealing.ServiceFactory.getOrderRequestService().process(msg, (MessageHandler)msg.getParameterValue("messageHandler"));
        if (msg.getReplyMessage().getStatus().equals( MessageStatus.FAILURE)) {
        	if(order.get_id()!=null)
        		FXOrderCache.remove( order.get_id() );
            return null;
        }
        sb.append(", orderID=").append( order.get_id() );
        log.info( sb.toString() );
        return order;
    }

    private boolean warmupOrderResponse( TradingVenueEntity tradingVenue, Organization cptyAOrg, User user, LegalEntity cmLegalEntity, SingleLegOrder orderRequest, boolean rejectNext, ResponseTuple counter ){
        try {
            log.info(new StringBuilder(200).append("DOWM:ordResp: tv= ").append(tradingVenue.getShortName()).
                    append(", cust= ").append(cptyAOrg.getShortName()).append(", user= ").append(user.getShortName()).append(", cptyCMLE=").
                    append(cmLegalEntity == null ? " " : cmLegalEntity.getShortName()).append(", reject=" + rejectNext ).toString());
            OrderMatchRequest activeMatchRequest = null;
            for( OrderMatchRequest matchRequest : orderRequest.getActiveMatchRequests() ){
                activeMatchRequest = matchRequest;
            }
            if( activeMatchRequest == null ){
                return false;
            }
            if (rejectNext) {
                OrderRejectResponse orderReject = new OrderRejectResponse();
                orderReject.setOrderId( activeMatchRequest.get_id() );
                orderReject.setReason( RejectReason.INTERNAL_REJECT );
                orderReject.getTimestamp();
                orderReject.setRejectType( OrderRejectResponse.RejectType.NEW_ORDER );
                //executeRejectionResponse( orderReject, orderRequest.get_id() );
                DirectedOrderResponseHandler.getInstance().handleRejectResponse( orderReject );
                counter.getRejectionCount().incrementAndGet();
            } else {
                OrderFillResponse orderFillResponse = new OrderFillResponse();
                orderFillResponse.setOrderId( activeMatchRequest.get_id() );
                orderFillResponse.setFillId( "1" + activeMatchRequest.get_id() );
                orderFillResponse.setCounterPartyFillId( "2" + activeMatchRequest.get_id() );
                orderFillResponse.setCounterPartyLE( activeMatchRequest.getLegalEntity() );
                orderFillResponse.setCptyClearingMemberLE( cmLegalEntity );
                orderFillResponse.setFillQty( orderRequest.getOrderAmount() );
                orderFillResponse.setAggressor(true);
                long[] timings = new long[4];
                timings[0] = System.currentTimeMillis() + 100;
                timings[1] = System.currentTimeMillis() + 200;
                timings[2] = System.currentTimeMillis() + 300;
                timings[3] = System.currentTimeMillis() + 400;
                orderFillResponse.setEventTimes( timings );
                DirectedOrderVerificationHandler.getInstance().handleFillResponse( orderFillResponse );
                //executeVerificationResponse( orderFillResponse, orderRequest.get_id() );
                counter.getVerificationCount().incrementAndGet();
            }
        }
        catch ( Exception ex )
        {
            log.error( "Error: happened in DOWM.ordResp orderid=" + orderRequest.get_id(), ex);
        }
        finally{
            log.info( "DOWM.ordResp : Clearing cache orderID=" + orderRequest.get_id() );
            clearCache( orderRequest.get_id() );
        }
        return true;
    }

    /**
     * Execute warmup response on TradeListenerThread
     * @param response
     */
    protected void executeVerificationResponse( final OrderFillResponse response, final String orderID ){
        ThreadPoolFactory.getInstance().getTradeListenerThreadPool().execute( new Runnable()
        {   public void run (){
                try{
                    DirectedOrderVerificationHandler.getInstance().handleFillResponse( response );
                }
                catch( Exception ex ){
                    log.error( "DirectedOrderWarmupManager.executeVerificationResponse : encountered exception while handling fill response for orderid= "
                    + orderID );
                }
                finally{
                    log.info( "DirectedOrderWarmup.executeVerificationResponse : Clearing cache for orderID=" + orderID );
                    clearCache( orderID );
                }
            }
        } );
    }

    /**
     * Execute warmup response on TradeListenerThread
     * @param response
     */
    protected void executeRejectionResponse( final OrderRejectResponse response, final String orderID ){
        ThreadPoolFactory.getInstance().getTradeListenerThreadPool().execute( new Runnable()
        {   public void run ()
            {
                try{
                    DirectedOrderResponseHandler.getInstance().handleRejectResponse( response );
                }
                catch( Exception ex ){
                    log.error( "DirectedOrderWarmupManager.executeVerificationResponse : encountered exception while handling fill response for orderid= "
                        + orderID );
                }
                finally{
                    log.info( "DirectedOrderWarmup.executeRejectionResponse : Clearing cache for orderID=" + orderID );
                    clearCache( orderID );
                }
            }
        } );
    }

    protected SingleLegOrder getSingleLegOrder( User user, Organization fi, CurrencyPair ccyPair ) {
        SingleLegOrder orderRequest = DealingModelFactory.getInstance().newOrderRequest();
        orderRequest.setNamespace( user.getNamespace() );
        orderRequest.setWarmUpObject( true );
        orderRequest.setUser( user );
        orderRequest.setCurrencyPair( ccyPair );
        orderRequest.setDealtCurrency( ccyPair.getBaseCurrency() );
        orderRequest.setType( com.integral.model.dealing.OrderRequest.Type.LIMIT );
        orderRequest.setChannel( "DNET" );
        orderRequest.setOrganization( fi );
        orderRequest.setTimeInForce( com.integral.model.dealing.TimeInForce.GTC );
        FXRateConvention fxRateConvention = QuoteConventionUtilC.getInstance().getDefault();
        FXRateBasis rateBasis = fxRateConvention.getFXRateBasis( orderRequest.getCurrencyPair() );
        orderRequest.setFxRateBasis( rateBasis );
        com.integral.model.dealing.OrderRequest.RequestLeg requestLeg = orderRequest.getRequestLeg();
        requestLeg.setBuySellMode( com.integral.model.dealing.OrderRequest.RequestLeg.BuySellMode.BUY );
        requestLeg.setTenor( FixConstants.SPOT_TENOR.getName() );
        requestLeg.setAmount( 1 );
        requestLeg.setSpotRate( 0.0 );
        
        long oid = id.incrementAndGet();
        orderRequest.set_id("WRMDO"+oid);
        orderRequest.setTransactionId("WRMDOR"+oid);
        
        return orderRequest;
    }

    private class WarmupResponseHandler implements MessageHandler{

        public Message handle(Message message) {
            log.info("DirectedOrderWarmupManager : Received message on warmup response handler.");
            return message;
        }
    }

    private List<List<Organization>> getOrganizationsPerThread( int numberThreads ) {
        final int finalCount = customerList.size() >= numberThreads ? numberThreads : customerList.size();
        List<List<Organization>> allThreadOrganizations = new ArrayList<List<Organization>>( finalCount );

        for ( int i = 0; i < finalCount; i++ ) {
            List<Organization> custOrgs = new ArrayList<Organization>();
            allThreadOrganizations.add( custOrgs );
        }

        int j = 0;
        for ( Organization customer : customerList ){
            allThreadOrganizations.get( j++ % finalCount ).add( customer );
        }
        return allThreadOrganizations;
    }

    @Override
    public void removeWarmupObjectsFromCache() {
        FXESPWorkflowCache.clearWarmUpObjects();
    }

    @Override
    public boolean initializeForTestTrade(){
        // do nothing
        return true;
    }

    private class ResponseTuple{
        private AtomicInteger verificationCount = new AtomicInteger(0);
        private AtomicInteger rejectionCount = new AtomicInteger(0);
        private AtomicInteger failureCount = new AtomicInteger(0);

        public AtomicInteger getVerificationCount() {
            return verificationCount;
        }

        public AtomicInteger getRejectionCount() {
            return rejectionCount;
        }

        public AtomicInteger getFailureCount() {
            return failureCount;
        }
    }

}
