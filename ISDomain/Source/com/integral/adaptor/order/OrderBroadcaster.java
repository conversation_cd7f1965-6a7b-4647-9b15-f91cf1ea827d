package com.integral.adaptor.order;

import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.log.RateLogger;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.order.configuration.OrderConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfigurationMBean;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.marketData.fx.FXMarketDataElement;
import com.integral.finance.price.fx.FXPrice;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ProviderQuoteIdFactory;
import com.integral.is.common.cache.MDSFactory;
import com.integral.is.common.util.ISCommonUtilC;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.finance.businessCenter.EndOfDayService;
import com.integral.is.log.MessageLogger;
import com.integral.is.message.MarketPrice;
import com.integral.is.message.MarketRate;
import com.integral.is.message.MessageFactory;
import com.integral.is.oms.Order;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.oms.OrderFactory;
import com.integral.is.oms.OrderFilter;
import com.integral.is.oms.calculator.OrderCalculatorFactory;
import com.integral.is.oms.filter.OrderFilterFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.FilterReferenceBand;
import com.integral.oms.spaces.fx.esp.calculator.FXOrderWorkflowCalculator;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.time.IdcDate;
import com.integral.user.Organization;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class OrderBroadcaster
{
	private static Log log = LogFactory.getLog(OrderBroadcaster.class);
	private static OrderConfigurationMBean commonMBean = OrderConfigurationFactory.getOrderConfigurationMBean();
	public static ScheduledThreadPoolExecutor schExeSrvc = new ScheduledThreadPoolExecutor(commonMBean.getOrderBroadcasterExecutorsCorePoolSize(), new OrderBroadcasterExecutionThreadFactory("OrderBroadcasters"));
	public static ConcurrentHashMap<String, PublisherC> frequencyBasedPublishers = new ConcurrentHashMap<String, OrderBroadcaster.PublisherC>();
	public static ConcurrentHashMap<String, OnEventPublisherC> eventPublishers = new ConcurrentHashMap<String, OrderBroadcaster.OnEventPublisherC>();
	static ServerRuntimeMBean serverRuntime = RuntimeFactory.getServerRuntimeMBean();
	static final ServerMBean serverMBean = ConfigurationFactory.getServerMBean();

	public static void startNew( String bookName, String orgName )
	{
		PublisherC publisher = new PublisherC(bookName, orgName);
		PublisherC old = frequencyBasedPublishers.putIfAbsent(bookName, publisher);
		if ( old == null )
		{
			publisher.start(false);
		}
	}
	
	public static void stop( String bookName )
	{
		PublisherC publisher = frequencyBasedPublishers.get( bookName );
		if( publisher != null )
		{
			publisher.stop();
		}
	}

	/**
	 * Schedules order book publish event.
	 * @param order
	 * @param force if true, then ensure event is always broadcasted
	 */
	public static void scheduleBroadcastEvent( Order order, boolean force )
	{
		try
		{
			if ( ISCommonUtilC.isWarmUpObject(order.getEntityDescriptor().getEntity()) )
			{
				return;
			}
			String bookName = order.getEntityDescriptor().getOrderBookName();
			_scheduleBroadcastEvent(bookName, order.getEntityDescriptor().getOrganization(), force);
		}
		catch ( Exception e )
		{
			log.error("OrderBroadcasterC.schedulePublishEvent - Failed to publish orderbook to server. Error follows - ", e);
		}
	}

	private static void _scheduleBroadcastEvent( String bookName, String orgName, boolean force )
	{
		try
		{
			OrderBook book = (OrderBook) OrderBookCacheC.getInstance().getOrderBooks().get(bookName);
			OnEventPublisherC event = new OnEventPublisherC(book, orgName);
			OnEventPublisherC old = eventPublishers.putIfAbsent(bookName, event);
			if ( old == null || force == true )
			{
				schExeSrvc.submit(event);
			}
			else
			{	
				log.debug("Broadcast event already running " + bookName);
			}
			//publisher.start(true);
			//else broadcast may not be enabled.
		}
		catch ( Exception e )
		{
			log.error("_schedulePublishEvent - Failed to publish orderbook to server. Error follows - ", e);
		}
	}
	
	private static boolean isOrderBroadCastingEnabled(OrderBook orderBook) {
		boolean isOrderBroadCastingEnabled = false;
		Organization org = ReferenceDataCacheC.getInstance().getOrganization(orderBook.getOrganization());
		if(org.isOrderBroadCastingEnabled() ) {
			if(orderBook.isOrderBroadCastingEnabled() && !orderBook.isVenueRoutingEnabled()) {
				isOrderBroadCastingEnabled = true;
			}
		}		
		return isOrderBroadCastingEnabled;
	}
	
	

	/*
	 * Publishes rate for only one organization from order book. 
	 * In case of SD book may have orders from multiple customer organizations.
	 */
	private static void publishRateForOrg( String orgName, OrderBook book )
	{		
		boolean isOrderBroadCastingEnabled = isOrderBroadCastingEnabled(book);
		if(isOrderBroadCastingEnabled) {
			FilterReferenceBand band = getFilterReferenceBand(book);
			List<Order> bidOrders = book.getBidBroadcastOrders(band);
			List<Order> offerOrders = book.getOfferBroadcastOrders(band);
			OrderFilter filter = OrderFilterFactory.newOrderFilter(OrderFilter.ORGANIZATION_FILTER);
			Map<String, Collection<Order>> org_bidOrder_Map = filter.getGroups(bidOrders);
			Map<String, Collection<Order>> org_offerOrder_Map = filter.getGroups(offerOrders);
			bidOrders = (List<Order>) org_bidOrder_Map.get(orgName);
			offerOrders = (List<Order>) org_offerOrder_Map.get(orgName);
			if ( !serverRuntime.isTradesEnabled() ||
					((bidOrders == null || bidOrders.size() == 0) &&
					(offerOrders == null || offerOrders.size() == 0)) )
			{
				publishStaleRate(book, orgName);
			}
			else
			{
				publishRate(book, bidOrders, offerOrders, orgName);
			}
		}
	}

	protected static ConcurrentMap<String, FilterReferenceBand> cachedFilterReferenceBands =
			new ConcurrentHashMap<String, FilterReferenceBand>();

	private static FilterReferenceBand getFilterReferenceBand(OrderBook book)
	{
		FilterReferenceBand band = null;

		if (OrderConfiguration.getInstance().isDisplayedOrdersSanityFilterEnabled(book.getCurrencyPair()))
		{
			band = cachedFilterReferenceBands.get( book.getBookName() );

			if (band == null) {
				CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString(book.getCurrencyPair());
				FXMarketDataElement mde = MDSFactory.getInstance().getStaticMDE(ccyPair.getBaseCurrency(),
						ccyPair.getVariableCurrency());
				FXPrice price = mde.getFXPrice();
				double midPoint;

				if (ccyPair.getBaseCurrency().equals(mde.getCurrencyPair().getBaseCurrency()))
				{
					midPoint = price.getMidFXRate().getSpotRate();
				}
				else
				{
					FXPrice invertedMidprice = price.getInverted();
					midPoint = invertedMidprice.getMidFXRate().getSpotRate();
				}

				double percentage = OrderConfiguration.getInstance().getDisplayedOrdersSanityFilterPercentage(book.getCurrencyPair());

				if (midPoint <= 0d || percentage <= 0d)
				{
					MessageLogger.getInstance().log("PriceBandRateFilter", "DisplayedOrder",
							"Midpoint for currency pair" + book.getCurrencyPair() + " is 0", null );
				}
				else
				{
					double delta = percentage * midPoint;
					double minPoint = midPoint - delta;
					double maxPoint = midPoint + delta;

					if (minPoint < 0d)
					{
						//Rates cant be negative.
						minPoint = 0d;
					}

					band = new FilterReferenceBand(book.getCurrencyPair(), minPoint, midPoint, maxPoint);
					FilterReferenceBand existing = cachedFilterReferenceBands.putIfAbsent(book.getBookName(), band);

					if (existing == null)
					{
						log.info("getFilterReferenceBand: " + band);
					}
					else
					{
						band = existing;
					}
				}
			}
		}

		return band;
	}

	/*
	 * Publishes entire order book.
	 */
	private static void publishOrderBook( OrderBook book )
	{
		boolean isOrderBroadCastingEnabled = isOrderBroadCastingEnabled(book);
		if(isOrderBroadCastingEnabled) {
			FilterReferenceBand band = getFilterReferenceBand(book);

			List<Order> bidOrders = book.getBidBroadcastOrders(band);
			List<Order> offerOrders = book.getOfferBroadcastOrders(band);
			OrderFilter filter = OrderFilterFactory.newOrderFilter(OrderFilter.ORGANIZATION_FILTER);
			Map<String, Collection<Order>> filteredBidOrders = filter.getGroups(bidOrders);
			Map<String, Collection<Order>> filteredOfferOrders = filter.getGroups(offerOrders);
			Set<String> uniqueOrgs = new HashSet<String>();
			uniqueOrgs.addAll(filteredBidOrders.keySet());
			uniqueOrgs.addAll(filteredOfferOrders.keySet());

			for (String anOrg : uniqueOrgs)
			{
				List<Order> orgBidOrders = (List<Order>) filteredBidOrders.get( anOrg );
				List<Order> orgOfferOrders = (List<Order>) filteredOfferOrders.get( anOrg );

				if ( !serverRuntime.isTradesEnabled() ||
						((orgBidOrders == null || orgBidOrders.size() == 0) &&
								(orgOfferOrders == null || orgOfferOrders.size() == 0)) )
				{
					publishStaleRate(book, anOrg);
				}
				else
				{
					publishRate(book, orgBidOrders, orgOfferOrders, anOrg);
				}
			}
		}
	}

	/*
	 * Publishes entire order book include stale rates
	 */
	private static void publishStaleOrderBook( OrderBook book, String orgName )
	{
		boolean isOrderBroadCastingEnabled = isOrderBroadCastingEnabled(book);
		if(isOrderBroadCastingEnabled) {
			if ( book.getOrganization().equals(orgName) )
			{
				publishStaleRate(book, orgName);
				return;
			}
			for ( String o : book.getMakerOrgs() )
			{
				if ( o.equals(orgName) )
				{
					publishStaleRate(book, o);
				}
			}
		}
	}

	private static void publishRate( OrderBook book, List<Order> bidOrders, List<Order> offerOrders, String orgName )
	{
		bidOrders = (bidOrders == null) ? Collections.EMPTY_LIST : bidOrders;
		offerOrders = (offerOrders == null) ? Collections.EMPTY_LIST : offerOrders;
		MarketRate rate = populateRate(bidOrders, offerOrders, book, orgName);
		AdaptorConfigurationFactory.getResponseHandlerInstance().rateRecieved(rate, ISCommonConstants.PROVIDER_TYPE_OA, (book.isOutright() ? 4 : 7));
		book.setLastPublishedRate(rate);
		RateLogger.logRate(rate);
        if(log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(300).append("OBCaster-RatePublished : ").append(rate.getProviderShortName()).append(' ').append(rate.getStreamId()).append(' ').append(rate.getQuoteId()).append(' ');
            sb.append("Bids [");
            for(MarketPrice price : rate.getBidPrices()) {
                sb.append(price.getRate()).append(' ').append(price.getLimitDouble()).append(' ');
                sb.append(price.isFlashOrderPrice()).append(' ').append(price.isRateSourceDisplayedOrder()).append(' ').append(price.isRateSourcePeggedOrder()).append(',');
            }
            sb.append("] Offers [");
            for(MarketPrice price : rate.getOfferPrices()) {
                sb.append(price.getRate()).append(' ').append(price.getLimitDouble()).append(' ');
                sb.append(price.isFlashOrderPrice()).append(' ').append(price.isRateSourceDisplayedOrder()).append(' ').append(price.isRateSourcePeggedOrder()).append(',');
            }
            sb.append(']');
            log.debug(sb.toString());
        }
	}

	//PQId is - BidOrder#OfferOrders#PendingTradeRequestsAgainstBidOrders#PendingTradeRequestsAgainstOfferOrders#TierNumber
	private static void publishStaleRate( OrderBook book, String orgName )
	{
		boolean isOrderBroadCastingEnabled = isOrderBroadCastingEnabled(book);
		if(isOrderBroadCastingEnabled) {
			MarketRate rate = populateRate(new ArrayList<Order>(), new ArrayList<Order>(), book, orgName);
			rate.setStale(true);
			rate.setProviderShortName(orgName);
			rate.setQuoteId(ProviderQuoteIdFactory.newQuoteIdWithProviderName(rate.getProviderShortName(), System.currentTimeMillis()));
			String dummyProviderQuoteID = System.currentTimeMillis() + "######0";
			rate.setProviderQuoteId(dummyProviderQuoteID, 0);
			book.setLastPublishedRate(rate);
			AdaptorConfigurationFactory.getResponseHandlerInstance().rateRecieved(rate, ISCommonConstants.PROVIDER_TYPE_OA, (book.isOutright() ? 4 : 7));
			RateLogger.logRate(rate);
		}
	}

	private static MarketRate populateRate( List<Order> bidOrders, List<Order> offerOrders, OrderBook book, String orgName )
	{
		int obdepth;
		MarketRate rate;
		int maxDepth = Math.max(bidOrders.size(), offerOrders.size());
		int tierLimit = OrderConfiguration.getInstance().getMaxBroadcastTiers(orgName);
		//Do not broadcast any orders
		if ( tierLimit == 0 || maxDepth == 0 )
		{
			obdepth = 0;
			rate = MessageFactory.newMarketRate(1, 1);
		}
		//Allow all the tiers
		else if ( tierLimit < 0 )
		{
			obdepth = maxDepth;
			rate = MessageFactory.newMarketRate(obdepth, obdepth);
		}
		//Broadcast tiers equal to tierLimit
		else
		{
			obdepth = (tierLimit < maxDepth) ? tierLimit : maxDepth;
			rate = MessageFactory.newMarketRate(obdepth, obdepth);
		}

		rate.setStale(false);
		String currencyPair = book.getCurrencyPair();
		rate.setBaseCcy(CurrencyFactory.getBaseCurrency(currencyPair));
		rate.setVarCcy(CurrencyFactory.getTermCurrency(currencyPair));
		rate.setLimitCcy(CurrencyFactory.getBaseCurrency(currencyPair));
		if ( book.isOutright() )
		{
			rate.setIsOutrightDisplayOrder(true);
			rate.setValueDate(book.getValueDate());
		}
		else
		{
			rate.setValueDate(getValueDate(rate.getBaseCcy(), rate.getVariableCcy()));
		}
		Iterator<Order> bidItr = bidOrders.iterator();
		Iterator<Order> offerItr = offerOrders.iterator();

		for ( int i = 0 ; i < obdepth ; i++ )
		{
			String bidId = "BID-";
			String offerId = "OFFER-";
			double bidLmt = 0.0;
			double offerLmt = 0.0;
			String bidPendingTradeRequests = "BTR-";
			String offerPendingTradeRequests = "OTR-";
			long bidUserObjectId = -1;
			long offerUserObjectId = -1;
			if ( i < bidOrders.size() )
			{
				Order order = ((Order) bidItr.next());
				double displayPrice = getDisplayPrice(order, book);
				double spotRate = order.getSpotRate();
				double forwardPoints = order.getForwardPoints();
				IdcDate fixingDate = order.getFixingDate();
				if ( !order.getEntityDescriptor().isDealingInTerm() )
				{
					bidLmt += order.getDiscloseAmount();
				}
				else
				{
					bidLmt += order.getDiscloseAmount() / displayPrice;
				}
				bidId = bidId + (order.isFlashPeriod() ? '*' + order.getOrderId() : order.getOrderId());
				String pendingIds = null;
				for ( String current : order.getPendingAmounts().keySet() )
				{
					Double d = order.getPendingAmounts().get(current);
					if ( d != null && d > 0.0 )
					{
						pendingIds = pendingIds + ',' + current;
					}
				}
				bidPendingTradeRequests = bidPendingTradeRequests + pendingIds;
				rate.setBidRate(displayPrice,i);
				rate.setOutrightBidFields(spotRate, forwardPoints, fixingDate, i);
				rate.setBidLimit((long) bidLmt, i);
				rate.getBidTier(i).setTimeEffective(order.getCreationTime());
                rate.getBidTier(i).setTTL(order.getOrderFlashTTL());
                rate.getBidTier(i).setIsFlashOrderPrice(order.isFlashPeriod());
                rate.getBidTier(i).setIsRateSourceDisplayedOrder(true);
                rate.getBidTier(i).setIsRateSourcePeggedOrder(order.getEntityDescriptor().isPegOrder());
				bidUserObjectId = order.getEntityDescriptor().getUserReference().getObjectId();
			}

			if ( i < offerOrders.size() )
			{
				Order order = ((Order) offerItr.next());
				double displayPrice = getDisplayPrice(order, book);
				double spotRate = order.getSpotRate();
				double forwardPoints = order.getForwardPoints();
				IdcDate fixingDate = order.getFixingDate();
				if ( !order.getEntityDescriptor().isDealingInTerm() )
				{
					offerLmt += order.getDiscloseAmount();
				}
				else
				{
					offerLmt += order.getDiscloseAmount() / displayPrice;
				}
				offerId = offerId + (order.isFlashPeriod() ? '*' + order.getOrderId() : order.getOrderId());
				String pendingIds = null;
				for ( String current : order.getPendingAmounts().keySet() )
				{
					Double d = order.getPendingAmounts().get(current);
					if ( d != null && d > 0.0 )
					{
						pendingIds = pendingIds + ',' + current;
					}
				}
				offerPendingTradeRequests = offerPendingTradeRequests + pendingIds;
				rate.setOfferRate(displayPrice, i);
				rate.setOutrightOfferFields(spotRate, forwardPoints, fixingDate, i);
				rate.setOfferLimit((long) offerLmt, i);
				rate.getOfferTier(i).setTimeEffective(order.getCreationTime());
                rate.getOfferTier(i).setTTL(order.getOrderFlashTTL());
                rate.getOfferTier(i).setIsFlashOrderPrice(order.isFlashPeriod());
                rate.getOfferTier(i).setIsRateSourceDisplayedOrder(true);
                rate.getOfferTier(i).setIsRateSourcePeggedOrder(order.getEntityDescriptor().isPegOrder());
				offerUserObjectId = order.getEntityDescriptor().getUserReference().getObjectId();
			}

			StringBuilder pqId = new StringBuilder(100);
			pqId.append(bidUserObjectId).append('#');
			pqId.append(offerUserObjectId).append('#');
			pqId.append(bidId).append('#');
			pqId.append(offerId).append('#');
			pqId.append(bidPendingTradeRequests).append('#');
			pqId.append(offerPendingTradeRequests).append('#');
			pqId.append(book.getIndex());
			rate.setProviderQuoteId(pqId.toString(), i);
		}
		long time = System.currentTimeMillis();
		rate.setProviderShortName(orgName);
		rate.setRateEffective(time);
		rate.setRateReceivedByAdapter(time);
		rate.setQuoteId(ProviderQuoteIdFactory.newQuoteIdWithProviderName(rate.getProviderShortName(), time));
		rate.setPriceType(Quote.PRICE_TYPE_ORDERS);
		rate.setBookname(book.getBookName());
		if ( serverMBean.isDoStreamEnabled() )
		{
			rate.setStreamId(ServerMBean.DEFAULT_DO_STREAM_NAME);
		}
		return rate;
	}

	private static double getDisplayPrice( Order order, OrderBook book )
	{
		FXOrderWorkflowCalculator owc = OrderCalculatorFactory.getInstance().getOrderWorkflowCalculator();
		if ( order.getEntityDescriptor().isSweepExcInst() && !order.getEntityDescriptor().isPegOrder() )
		{
			Double currentMarketVWAP = owc.getMarketVWAPPrice(order, book);
			return currentMarketVWAP != null ? currentMarketVWAP : order.getDisplayPrice();
		}
		else
		{
			return order.getDisplayPrice();
		}
	}

	private static IdcDate getValueDate( String baseccy, String variableccy )
	{
		FXRateConvention fxrateconv = null;
		FXRateBasis ratebasis = null;
		try
		{
			fxrateconv = QuoteConventionUtilC.getInstance().getFXRateConvention(ISCommonConstants.QUOTE_CONVENTION);
			ratebasis = fxrateconv.getFXRateBasis(baseccy, variableccy);
            return ratebasis.getSpotDate(EndOfDayService.getCurrentTradeDate());
		}
		catch ( Exception e )
		{
			if ( log.isDebugEnabled() )
			{
				log.debug("OrderBroadcasterC.getValueDate - Failed - ", e);
				log.debug("OrderBroadcasterC.getValueDate - Failed - BC=" + baseccy + " TC=" + variableccy);
				log.debug("OrderBroadcasterC.getValueDate - Failed - fxrateconv=" + fxrateconv + " ratebasis=" + ratebasis);
			}
		}
		return null;
	}

	private static class OrderBroadcasterExecutionThreadFactory implements ThreadFactory
	{
		AtomicInteger threadNumber = new AtomicInteger(1);
		ThreadGroup tg = null;

		public OrderBroadcasterExecutionThreadFactory( String name )
		{
			tg = new ThreadGroup(name);
		}

		public Thread newThread( Runnable runnable )
		{
			return new Thread(tg, runnable, "OrderBroadcaster-" + threadNumber.getAndIncrement());
		}
	}

	/*
	 * Regular publisher. Publishes entire book
	 */
	static class PublisherC implements Runnable
	{
		private String bookName;
		private String orgName;
		long lastPublishedTime = System.currentTimeMillis();
		ScheduledFuture taskHandle;

		public PublisherC( String bookName, String orgName )
		{
			this.bookName = bookName;
			this.orgName = orgName;
		}

		public void start( boolean useDelay )
		{
			if ( useDelay )
			{
				taskHandle = schExeSrvc.scheduleAtFixedRate(this, commonMBean.getOrderPublishingInterval(), commonMBean.getOrderPublishingInterval(), TimeUnit.MILLISECONDS);
			}
			else
			{
				taskHandle = schExeSrvc.scheduleAtFixedRate(this, 0, commonMBean.getOrderPublishingInterval(), TimeUnit.MILLISECONDS);
			}
		}

		public void suspend()
		{
			taskHandle.cancel(true);
		}
		
		public void stop()
		{
			schExeSrvc.remove( (Runnable) taskHandle);
			taskHandle.cancel(true);
		}

		public void run()
		{
			try
			{
				if ( serverRuntime.isTradesEnabled() )
				{
					publishRate();
				}
				else if ( log.isDebugEnabled() )
				{
					log.debug("PublisherC Trading is not enabled (ServerRuntimeMBean.isTradesEnabled).");
				}
			}
			catch ( Exception e )
			{
				log.debug("PublisherC - Exception - book : " + getBookName(), e);
			}
		}

		public void publishRate()
		{
			OrderBook book = (OrderBook) OrderFactory.getOrderBookCache().getOrderBooks().get(getBookName());
			publishOrderBook(book);
			lastPublishedTime = System.currentTimeMillis();
		}

		/* (non-Javadoc)
		 * @see java.lang.Object#toString()
		 */
		@Override
		public String toString()
		{
			return "PublisherC [bookName=" + getBookName() + ", lastPublishedTime=" + lastPublishedTime + ", taskHandle=" + taskHandle + "]";
		}

		public String getBookName()
		{
			return bookName;
		}

		public String getOrgName()
		{
			return orgName;
		}

	}

	/*
	 * Publishes only for an org.
	 */
	static class OnEventPublisherC implements Runnable
	{
		private OrderBook book;
		private String orgName;

		public OnEventPublisherC( OrderBook book, String orgName )
		{
			this.book = book;
			this.orgName = orgName;
		}

		public void run()
		{
			try
			{
				publishRateForOrg(orgName, book);
			}
			catch ( Exception e )
			{
				log.debug("OnEventPublisherC - Exception - book : " + book.getBookName() + " Org : " + orgName, e);
			}
			finally
			{
				eventPublishers.remove(book.getBookName());
			}
		}

		/* (non-Javadoc)
		 * @see java.lang.Object#toString()
		 */
		@Override
		public String toString()
		{
			return "OnEventPublisherC [book=" + book + ", orgName=" + orgName + "]";
		}

	}

	/**
	 * 
	 */
	public static void republishAll()
	{
		{
			log.info("Start repulishing all orderbook.");
			for ( PublisherC freqPublisher : frequencyBasedPublishers.values() )
			{
				_scheduleBroadcastEvent(freqPublisher.getBookName(), freqPublisher.getOrgName(), false);
			}
		}

		cachedFilterReferenceBands.clear();
	}

	public static void republishAll( String orgName, boolean isOrderExecutionEnabled )
	{
		log.info("Start repulishing all orderbook for orgName: " + orgName);
		List<OrderBook> books = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(orgName);
		if ( isOrderExecutionEnabled )
		{
			for ( OrderBook i : books )
			{
				publishOrderBook(i);
			}
		}
		else
		{
			for ( OrderBook i : books )
			{
				publishStaleOrderBook(i, orgName);
			}
		}
	}
}
