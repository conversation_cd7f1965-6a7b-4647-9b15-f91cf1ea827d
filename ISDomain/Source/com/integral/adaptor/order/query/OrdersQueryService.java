package com.integral.adaptor.order.query;

import com.integral.adaptor.order.CommonAdaptorFrameworkConstants;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.dealing.RequestC;
import com.integral.finance.dealing.RequestClassification;
import com.integral.finance.dealing.facade.DealingFacadeFactory;
import com.integral.finance.dealing.facade.config.RequestStateMBean;
import com.integral.finance.fx.FXSingleLegC;
import com.integral.finance.trade.Trade;
import com.integral.finance.trade.facade.TradeFacadeFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.persistence.NamedEntity;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.query.QueryCriteria;
import com.integral.query.QueryCriteriaBuilder;
import com.integral.query.QueryCriteriaBuilderC;
import com.integral.query.QueryFactory;
import com.integral.query.QueryService;
import com.integral.user.Organization;
import com.integral.workflow.State;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;

@SuppressWarnings("unchecked")
public class OrdersQueryService
{
	Log log = LogFactory.getLog(this.getClass());

	private static QueryService queryService = QueryFactory.getQueryService();

	public Collection getLimitRequest( String externalRequestId )
	{
		try
		{
			RequestClassification limitReqClassification = ISUtilImpl.getInstance().getRequestClassification(CommonAdaptorFrameworkConstants.MAKEPRICE_CREATE_TYPE);

			QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
			QueryCriteria criteria = buildr.get("");
			Expression exp = new ExpressionBuilder();
			Expression externalReqExp = exp.get("externalRequestId").equal(externalRequestId);
			Expression limitreqClsfExp = exp.get("requestClassification").equal(limitReqClassification);
			criteria.setExpression(externalReqExp.and(limitreqClsfExp));
			Collection result = queryService.findAll(RequestC.class, criteria);
			return result;
		}
		catch ( Exception e )
		{
			log.error("OrdersQueryService.findLimitRequest Exception in Query:", e);
			return null;
		}
	}

	public Collection findLimitRequest( TradingParty tp, Date fromDate, Date toDate )
	{
		try
		{
			RequestClassification limitReqClassification = ISUtilImpl.getInstance().getRequestClassification(CommonAdaptorFrameworkConstants.MAKEPRICE_CREATE_TYPE);

			QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
			QueryCriteria criteria = buildr.get("");
			Expression exp = new ExpressionBuilder();
			Expression userExp = exp.get("legalEntity").equal(tp.getLegalEntity());
			Expression tranExp = exp.get("requestClassification").equal(limitReqClassification);
			Expression fromDateExp = exp.get("createdDateTime").greaterThanEqual(fromDate);
			Expression toDateExp = exp.get("createdDateTime").lessThanEqual(toDate);
			criteria.setExpression(userExp.and(tranExp).and(fromDateExp).and(toDateExp));
			Collection result = queryService.findAll(RequestC.class, criteria);
			return result;
		}
		catch ( Exception e )
		{
			log.error("OrdersQueryService.findLimitRequest Exception in Query:", e);
			return null;
		}
	}

	public Collection findQuotedRequest( String attributeName, String attributeValue, boolean isRejectedNeeded )
	{
		try
		{
			RequestClassification quotedReqClassification = ISUtilImpl.getInstance().getRequestClassification(CommonAdaptorFrameworkConstants.ACCEPT_TYPE);
			RequestStateMBean requestStateMBean = DealingFacadeFactory.getRequestStateMBean();
			State declinedState = requestStateMBean.getDeclinedState();

			QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
			QueryCriteria criteria = buildr.get("");
			Expression exp = new ExpressionBuilder();
			Expression attrExp = exp.get(attributeName).equal(attributeValue);
			Expression reqClasfExp = exp.get("requestClassification").equal(quotedReqClassification);
			Expression stateExp = exp.get("workflowStateMapDependent").get("state").isNull().or(exp.get("workflowStateMapDependent").get("state").notEqual(declinedState));

			if ( isRejectedNeeded )
			{
				criteria.setExpression(attrExp.and(reqClasfExp));
			}
			else
			{
				criteria.setExpression(attrExp.and(reqClasfExp).and(stateExp));
			}

			Collection result = queryService.findAll(RequestC.class, criteria);
			return result;
		}
		catch ( Exception e )
		{
			log.error("OrdersQueryService.findLimitRequest Exception in Query:", e);
			return null;
		}
	}

	public Collection getAllActiveLimitRequest( Date fromDate, Date toDate, Collection<Organization> orgList )
	{
		try
		{
			RequestStateMBean requestStateMBean = DealingFacadeFactory.getRequestStateMBean();
			State activeState = requestStateMBean.getInitialState();
			State partialState = requestStateMBean.getPartialState();
			RequestClassification limitReqClassification = ISUtilImpl.getInstance().getRequestClassification(CommonAdaptorFrameworkConstants.MAKEPRICE_CREATE_TYPE);

			QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
			QueryCriteria criteria = buildr.get("");
			Expression exp = new ExpressionBuilder();
			Expression fromDateExp = exp.get("createdDateTime").greaterThanEqual(fromDate);
			Expression toDateExp = exp.get("createdDateTime").lessThanEqual(toDate);
			Expression limitreqClsfExp = exp.get("requestClassification").equal(limitReqClassification);
			Expression stateExp = exp.get("workflowStateMapDependent").get("state").isNull().or(exp.get("workflowStateMapDependent").get("state").equal(activeState)).or(exp.get("workflowStateMapDependent").get("state").equal(partialState));
			Expression orgListExp = getOrgListExpression(exp, orgList);

			criteria.setExpression(stateExp.and(fromDateExp).and(toDateExp).and(limitreqClsfExp).and(orgListExp));
			Collection result = queryService.findAll(RequestC.class, criteria);
			return result;
		}
		catch ( Exception e )
		{
			log.error("OrdersQueryService.findLimitRequest Exception in Query:", e);
			return null;
		}
	}

	public Collection findTrades( String orderId )
	{

		try
		{
			State VERIFIED_STATE = TradeFacadeFactory.getTradeStateMBean().getVerifiedState();
			State CONFIRMED_STATE = TradeFacadeFactory.getTradeStateMBean().getConfirmedState();
			QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
			QueryCriteria criteria = buildr.get("");
			Expression exp = new ExpressionBuilder();
			Expression stateExp = exp.get("workflowStateMap").get("state").equal(VERIFIED_STATE);
			Expression confirmStateExp = exp.get("workflowStateMap").get("state").equal(CONFIRMED_STATE);
			Expression requestExp = exp.get("request").get("orderId").equal(orderId);
			Expression makerOrderExp = exp.get("makerRequest").get("orderId").equal(orderId);

			criteria.setExpression((stateExp.or(confirmStateExp)).and((requestExp).or(makerOrderExp)));
			Collection result = queryService.findAll(Trade.class, criteria);
			return result;
		}
		catch ( Exception e )
		{
			log.error("OrdersQueryService.findTrades Exception in Query:", e);
			return null;
		}
	}

	private Expression getOrgListExpression( Expression exp, Collection<Organization> orgList )
	{
		Expression OrgListExp = null;
		for ( Organization org : orgList )
		{
			Expression tempExp = exp.get("organization").equal(org);
			if ( OrgListExp == null )
			{
				OrgListExp = tempExp;
			}
			else
			{
				OrgListExp = OrgListExp.or(tempExp);
			}
		}
		return OrgListExp;
	}

	public Collection findLimitRequestUsingReportQuery( TradingParty tp, Date fromDate, Date toDate ) throws PersistenceException
	{
		long currentTime = System.currentTimeMillis();
		RequestClassification limitReqClassification = ISUtilImpl.getInstance().getRequestClassification("LIMIT");

		QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
		QueryCriteria criteria = buildr.get("");
		ExpressionBuilder expBuildr = new ExpressionBuilder();
		Expression userExp = expBuildr.get("legalEntity").equal(tp.getLegalEntity());
		Expression tranExp = expBuildr.get("requestClassification").equal(limitReqClassification);
		Expression fromDateExp = expBuildr.get("createdDateTime").greaterThanEqual(fromDate);
		Expression toDateExp = expBuildr.get("createdDateTime").lessThanEqual(toDate);
		criteria.setExpression(userExp.and(tranExp).and(fromDateExp).and(toDateExp));

		ArrayList<ReportQueryResult> result = new ArrayList<ReportQueryResult>();
		ReportQuery query = new ReportQuery(expBuildr);

		query.useCollectionClass(ArrayList.class);
		query.setSelectionCriteria(criteria.getExpression());
		query.setReferenceClass(RequestC.class);

		query.addAttribute("timeInForce");
		query.addAttribute("transactionID");
		query.addAttribute("orderId");
		query.addAttribute("externalRequestId");
		query.addAttribute("dealtCurrencyProperty", expBuildr.anyOf("fxLegDealingPrices").get("dealtCurrencyProperty"));
		query.addAttribute("dealtAmount", expBuildr.anyOf("fxLegDealingPrices").get("dealtAmount"));
		query.addAttribute("bidOfferMode", expBuildr.anyOf("fxLegDealingPrices").get("bidOfferMode"));
		query.addAttribute("minDealtAmount", expBuildr.anyOf("fxLegDealingPrices").get("minDealtAmount"));
		query.addAttribute("dealtCurrencyShortName", expBuildr.anyOf("fxLegDealingPrices").get("dealtCurrency").get(NamedEntity.ShortName));
		query.addAttribute("settledCurrencyShortName", expBuildr.anyOf("fxLegDealingPrices").get("settledCurrency").get(NamedEntity.ShortName));
		//query.addAttribute("bidRate", expBuildr.anyOf("fxLegDealingPrices").get("priceElement").getField("IdcDealingPriceElement.bidRate"));
		//query.addAttribute("offerRate", expBuildr.anyOf("fxLegDealingPrices").get("priceElement").getField("IdcDealingPriceElement.offerRate"));
		query.addAttribute("bidRate", expBuildr.anyOf("fxLegDealingPrices").get("dependentPriceElement").get("price").get("bid").get("rate"));
		query.addAttribute("offerRate", expBuildr.anyOf("fxLegDealingPrices").get("dependentPriceElement").get("price").get("offer").get("rate"));
		query.addAttribute("stateShortName", expBuildr.get("workflowStateMapDependent").get("state").get(NamedEntity.ShortName));

		result = (ArrayList<ReportQueryResult>) PersistenceFactory.newSession().executeQuery(query);
		if ( log.isInfoEnabled() )
		{
			log.info("OrdersQueryService.findLimitRequestUsingReportQuery: Time taken to query Requests for tp:" + tp.getShortName() + " fromDate:" + fromDate + " toDate:" + toDate + " is:" + (System.currentTimeMillis() - currentTime));
		}
		return result;
	}

	public Collection findTradesUsingReportQuery( Object[] orderIds ) throws PersistenceException
	{

		State VERIFIED_STATE = TradeFacadeFactory.getTradeStateMBean().getVerifiedState();
		State CONFIRMED_STATE = TradeFacadeFactory.getTradeStateMBean().getConfirmedState();
		QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
		QueryCriteria criteria = buildr.get("");
		ExpressionBuilder expBuildr = new ExpressionBuilder();
		Expression verifiedStateExp = expBuildr.get("workflowStateMap").get("state").equal(VERIFIED_STATE);
		Expression confirmStateExp = expBuildr.get("workflowStateMap").get("state").equal(CONFIRMED_STATE);
		Expression requestExp = expBuildr.get("request").get("orderId").in(orderIds);
		Expression makerOrderExp = expBuildr.get("makerRequest").get("orderId").in(orderIds);

		Expression stateExp = verifiedStateExp.or(confirmStateExp);
		Expression reqExp = requestExp.or(makerOrderExp);
		criteria.setExpression(reqExp.and(stateExp));

		ArrayList<ReportQueryResult> result = new ArrayList<ReportQueryResult>();
		ReportQuery query = new ReportQuery(expBuildr);

		query.useCollectionClass(ArrayList.class);
		query.setSelectionCriteria(criteria.getExpression());
		query.setReferenceClass(FXSingleLegC.class);

		query.addAttribute("transactionID");
		query.addAttribute("orderId", expBuildr.get("request").get("orderId"));
		query.addAttribute("makerOrderId", expBuildr.get("makerRequest").get("orderId"));
		query.addAttribute("workflowCodeArgument", expBuildr.get("workflowStateMap").get("workflowCodeArgument"));
		query.addAttribute("isDealtCurrency1", expBuildr.get("fxLeg").getField("IDCTRDLEG.dealtCrnc1"));
		query.addAttribute("currency1Amount", expBuildr.get("fxLeg").getField("IDCTRDLEG.crnc1Amt"));
		query.addAttribute("currency2Amount", expBuildr.get("fxLeg").getField("IDCTRDLEG.crnc2Amt"));
		query.addAttribute("valueDate", expBuildr.get("fxLeg").getField("IDCTRDLEG.valueDate"));
		query.addAttribute("rate", expBuildr.get("fxLeg").getField("IDCTRDLEG.fxRateRate"));
		query.addAttribute("spotRate", expBuildr.get("fxLeg").getField("IDCTRDLEG.fxRateSpotRate"));
		query.addAttribute("currency1Id", expBuildr.get("fxLeg").getField("IDCTRDLEG.crnc1Id"));
		query.addAttribute("currency2Id", expBuildr.get("fxLeg").getField("IDCTRDLEG.crnc2Id"));

		result = (ArrayList<ReportQueryResult>) PersistenceFactory.newSession().executeQuery(query);
		return result;
	}

	public Collection findTradesUsingReportQuery( TradingParty tp, Date fromDate, Date toDate ) throws PersistenceException
	{
		long currentTime = System.currentTimeMillis();
		State VERIFIED_STATE = TradeFacadeFactory.getTradeStateMBean().getVerifiedState();
		State CONFIRMED_STATE = TradeFacadeFactory.getTradeStateMBean().getConfirmedState();
		QueryCriteriaBuilder buildr = new QueryCriteriaBuilderC();
		QueryCriteria criteria = buildr.get("");
		ExpressionBuilder expBuildr = new ExpressionBuilder();
		Expression verifiedStateExp = expBuildr.get("workflowStateMap").get("state").equal(VERIFIED_STATE);
		Expression confirmStateExp = expBuildr.get("workflowStateMap").get("state").equal(CONFIRMED_STATE);
		Expression fromDateExp = expBuildr.get("tradeDate").greaterThanEqual(fromDate);
		Expression toDateExp = expBuildr.get("tradeDate").lessThanEqual(toDate);
		Expression counterPartyAExp = expBuildr.get("counterpartyA").equal(tp.getLegalEntity());
		Expression counterPartyBExp = expBuildr.get("counterpartyB").equal(tp.getLegalEntity());

		Expression stateExp = verifiedStateExp.or(confirmStateExp);
		Expression counterPartyExp = counterPartyAExp.or(counterPartyBExp);

		criteria.setExpression(fromDateExp.and(toDateExp).and(counterPartyExp).and(stateExp));

		ArrayList<ReportQueryResult> result = new ArrayList<ReportQueryResult>();
		ReportQuery query = new ReportQuery(expBuildr);

		query.useCollectionClass(ArrayList.class);
		query.setSelectionCriteria(criteria.getExpression());
		query.setReferenceClass(FXSingleLegC.class);

		query.addAttribute("transactionID");
		query.addAttribute("tradeDate");
		query.addAttribute("counterpartyB", expBuildr.getField("IDCTRD.CptyBID"));
		query.addAttribute("orderId", expBuildr.get("request").get("orderId"));
		query.addAttribute("makerOrderId", expBuildr.getAllowingNull("makerRequest").get("orderId"));
		query.addAttribute("workflowCodeArgument", expBuildr.get("workflowStateMap").get("workflowCodeArgument"));
		query.addAttribute("isDealtCurrency1", expBuildr.get("fxLeg").getField("IDCTRDLEG.dealtCrnc1"));
		query.addAttribute("currency1Amount", expBuildr.get("fxLeg").getField("IDCTRDLEG.crnc1Amt"));
		query.addAttribute("currency2Amount", expBuildr.get("fxLeg").getField("IDCTRDLEG.crnc2Amt"));
		query.addAttribute("valueDate", expBuildr.get("fxLeg").getField("IDCTRDLEG.valueDate"));
		query.addAttribute("rate", expBuildr.get("fxLeg").getField("IDCTRDLEG.fxRateRate"));

		result = (ArrayList<ReportQueryResult>) PersistenceFactory.newSession().executeQuery(query);
		if ( log.isInfoEnabled() )
		{
			log.info("OrdersQueryService.findTradesUsingReportQuery: Time taken to query trades for tp:" + tp.getShortName() + " fromDate:" + fromDate + " toDate:" + toDate + " is:" + (System.currentTimeMillis() - currentTime));
		}
		return result;
	}

}
