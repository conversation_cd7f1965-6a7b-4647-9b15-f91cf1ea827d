/**
 * <AUTHOR>
 */
package com.integral.adaptor.order.cache;

import java.util.concurrent.ConcurrentHashMap;

import com.integral.message.WorkflowMessage;

/**
 * This cache holds replace request if cancellation is pending. 
 * <AUTHOR>
 *
 */
public class PendingRequestCache
{
	private static ConcurrentHashMap<String, WorkflowMessage> pendingReplaceRequests = new ConcurrentHashMap<String, WorkflowMessage>();

	private PendingRequestCache()
	{
	}

	public static WorkflowMessage storeReplaceRequest( String key, WorkflowMessage newOrderSingle )
	{
		return pendingReplaceRequests.putIfAbsent(key, newOrderSingle);
	}

	public static WorkflowMessage getReplaceRequest( String key )
	{
		return pendingReplaceRequests.get(key);
	}

	public static WorkflowMessage clearReplaceRequest( String key )
	{
		return pendingReplaceRequests.remove(key);
	}

	public static ConcurrentHashMap<String, WorkflowMessage> getCachedData()
	{
		return pendingReplaceRequests;
	}

}
