package com.integral.adaptor.order.configuration;

// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.

import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.order.OrderBroadcaster;
import com.integral.adaptor.order.handler.OAHandlerFactory;
import com.integral.adaptor.order.handler.UnsolicitedOrderCancelHandlerC;
import com.integral.adaptor.request.RequestHandlerFactoryRegistry;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.fix.client.mbean.FIXClientConfig;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.oms.OrderBook;
import com.integral.is.oms.OrderBookCacheC;
import com.integral.is.warmuptrade.WarmUpReferenceDataEntityUpdateHandlerC;
import com.integral.model.dealing.OrderRequest;
import com.integral.oms.spaces.fx.esp.netting.AggregatedFillPriceImprovementFeeType;
import com.integral.persistence.Namespace;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.server.VirtualServerType;
import com.integral.user.Organization;
import com.integral.util.IdcUtilC;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * This class is used to provide the configuration capabilities to the order management system.
 * <AUTHOR>
 */
public class OrderConfiguration extends AdaptorConfiguration implements OrderConfigurationMBean
{
	@SuppressWarnings({ "StaticNonFinalField" })
	private static OrderConfigurationMBean instance = createInstanceAndLoadProperties();

	// instance variables
	private int orderPublishingInterval;
	private int quotePublicationInterval;
	private int bestBidOfferPublicationInterval;
	private boolean intrafloorMatchingEnabled;
	//private String providerList;
	private int orderCancellationPeriod;
	private boolean cancelAllNonPersistentActiveOrders;
	private int orderStatusQueryPeriod;
	private boolean sendOrderMassStatusEnabled;
	private int orderExecutorsCorePoolSize;
	private int orderExecutorsMaxPoolSize;
	private int orderExecutorsPoolKeepAliveTime;
	private int quoteExecutorsCorePoolSize;
	private int quoteExecutorsMaxPoolSize;
	private int quoteExecutorsPoolKeepAliveTime;
	private String tradeRejectionReason;
	private int orderBroadcasterExecutorsCorePoolSize;
	private int orderBroadcasterExecutorsMaxPoolSize;
	private int orderBroadcasterExecutorsPoolKeepAliveTime;
	private boolean directOASetup;
	private boolean broadcastOnlySetup;
	private boolean rematchAfterDelayedRejection;
	private String rematchAfterDelayedRejectionOrderTypes;
	private Collection<String> rematchAfterDelayedRejectionOrderClassifications;
	private double rematchAfterDelayedRejectionMaxSlippage;
	private boolean orderBroadcastEnabled;
	private String oMSLogCategory;
	private String orderAdaptorProviderList;
	private double orderMinimumMarketRateAmount;
	private int VWAPExecutionNumberOfBands;
	private double VWAPExecutionBandFloor;
	private double VWAPExecutionMinimumBandSize;
	private int takerOrderExecutorsCorePoolSize;
	private int takerOrderExecutorsMaxPoolSize;
	private int takerOrderExecutorsPoolKeepAliveTime;
	private Map<String, List<Organization>> orderAdaptorHiddenProvidersMap = new ConcurrentHashMap<String, List<Organization>>();
	private Map<String, List<Organization>> orderAdaptorUnSpreadedProvidersMap = new ConcurrentHashMap<String, List<Organization>>();
	private Map<String, List<String>> orderAdaptorExcludedProvidersMap;
	private boolean hiddenProvidersEnabled = false;
	private boolean unSpreadedProvidersEnabled = false;
	private boolean spreadedProviderProperTierAssociationEnabled = false;
	private boolean excludedProvidersEnabled = false;
	private Collection<String> orderProvidersList = new HashSet<String>();
	private Collection<Organization> orderProviderOrgList = new HashSet<Organization>();
	private Collection<Organization> extendedOrderProviderOrgsList = new HashSet<Organization>();
	private boolean isCreditCheckEnabled = true;
	private boolean useQuoteExecutors = false;
    private boolean useExecutorsForRouting = false;
	private boolean useExecutorsForCheckingMarketability = false;
	private List<OrderRequest.Type> routeOrderToOMS;
	private boolean omsCustomerOrderAdditionalStates;
	private Map<String, Boolean> omsCustomerOrderAdditionalStatesMap;
	private boolean omsCustomerOrderPendingNewWithOrderId;
	private Map<String, Boolean> omsCustomerOrderPendingNewWithOrderIdMap;
	private Long omsManualOrderAcceptanceExpiryTime;
	private boolean routeLinkedOrderToOMS;
	private Map<String, Boolean> routeLinkedOrderToOMSMap;
	private boolean routeOrderToOMSWithAuto;
	private Map<String, Boolean> routeOrderToOMSWithAutoMap;
	private Map<String, List<OrderRequest.Type>> routeOrderToOMSMap;
	private Map<String, Long> omsManualOrderAcceptanceExpiryTimeMap;
	private Map<String, Double> maxShowAmountsMap = new ConcurrentHashMap<String, Double>();
	private boolean opAdjustmentEnabled = false;
	private Map<String, Double> opAdjustmentPipsMap = new ConcurrentHashMap<String, Double>();
	private List<String> opAdjustmenntAllowedChannels;
	private List<String> opAdjustmenntAllowedOrderTypes;
	private boolean isCircuitBreakerEnabled;
	private Map<String, Integer> circuitBreakerThresholds = new ConcurrentHashMap<String, Integer>();
	private int orderMatchDelay;
	private Map<String, Integer> orderMatchDelayMap = new HashMap<String, Integer>();
	private Collection<String> flashOrderTypes = new HashSet<String>();
	private Map<String, Collection<String>> flashOrderTypesMap = new HashMap<String, Collection<String>>();
	private Collection<String> flashOrderTIFs = new HashSet<String>();
	private Map<String, Collection<String>> flashOrderTIFsMap = new HashMap<String, Collection<String>>();
	private Double flashOrderMinSize;
	private Map<String, Double> flashOrderMinSizeMap = new HashMap<String, Double>();
	private Double flashOrderMaxSize;
	private Map<String, Double> flashOrderMaxSizeMap = new HashMap<String, Double>();
	private int flashLiftTimeout;
	private Map<String, Integer> flashLiftTimeoutMap = new HashMap<String, Integer>();
	private Double customerRegularSize;
	private Map<String, Double> customerRegularSizeMap = new HashMap<String, Double>();
	private int orderSliceTTL;
	private int orderMinSliceInterval;
	private boolean flashUpdatePriceOnMatch;
	private HashSet<Namespace> makerTakerNSs;
	private int iocExpiryTime;
	private Map<String, Integer> iocExpiryTimeMap = new HashMap<String, Integer>();
	private Map<String, Integer> iocExpiryTimeForBrokerOrgMap = new HashMap<String, Integer>();
	private String iocExpiryTimeEnabledForChannel;
	private Map<String, String> iocExpiryTimeEnabledForChannelMap = new HashMap<String, String>();
	private Map<String, Boolean> iocExpiryTimeOnMatchMap = new HashMap<String, Boolean>();
	private Map<String, Boolean> iocExpiryTimeOnMatchForBrokerOrgMap = new HashMap<String, Boolean>();

	boolean isSetOrderRateToFillRate = false;
	private Map<String, Boolean> orderRateToFillRateMap = new HashMap<String, Boolean>();
	private Map<String, Boolean> orderRateToFillRateBrokerOrgMap = new HashMap<String, Boolean>();

	private boolean iocExpiryOnMatch = false;
	private Map<String, Double> defaultMKTRangeMap = new HashMap<String, Double>();
	private Double defaultMKTRange;

    private Map<String, Double> defaultPercentageMKTRangeMap = new HashMap<String, Double>();
    private Double defaultPercentageMKTRange;

	private boolean isOptFOKExecEnabled = false;
	private Map<String, Boolean> optFOKExecMap = new HashMap<String, Boolean>();
	private boolean isSendVWAPPriceOptimizedFOKExecution = false;

	private int defaultProviderSuspensionPeriod = 0;
	private Map<String, Integer> providerSuspensionPeriodMap = new HashMap<String, Integer>();

	//1 for TOB 2 for Sweep
	private boolean isFillAtMarketExecInstTOB = false;
	private Map<String, Boolean> fillAtMarketExecInstTOBMap = new HashMap<String, Boolean>();

	private boolean isOrderEffectiveTimeEnabled = false;
	private Map<String, Boolean> orderEffectiveTimeEnabledMap = new HashMap<String, Boolean>();

	private long orderEffectiveTimeToleranceDefault = 0;
	private Map<String, Integer> orderEffectiveTimeToleranceMap = new HashMap<String, Integer>();

	private int maxBroadcastTiersDefault = 0;
	private Map<String, Integer> maxBroadcastTiersMap = new HashMap<String, Integer>();

	private boolean rejectAdditionalPrecisionOrders = true; // default = true
	
	private boolean isDirectOrderToBAEnabled = false;
	private Map<String, Boolean> directOrderToBAEnabledMap = new HashMap<String, Boolean>();

	private boolean isDirectOrderToLpEnabled = false;
	private Map<String, Boolean> directOrderToLpEnabledMap = new HashMap<String, Boolean>();

    private int defaultProviderQuotesCacheSize;
    private Map<String, Integer> providerQuotesCacheSizeMap = new HashMap<String, Integer>();

    private int defaultProviderQuoteHistoryMatchDepth;
    private Map<String, Integer> providerQuoteHistoryMatchDepthMap = new HashMap<String, Integer>();

    private int flashExpiryPeriod;
    private Map<String,Integer> flashExpiryPeriodMap = new HashMap<String,Integer>();

    private boolean termCurrencyBroadcastEnabled = false;
    private Map<String, Boolean> termCurrencyBroadcastEnabledMap = new HashMap<String, Boolean>();

    private int algoFillAtMktExpiryInterval;

    private List<String> displayOrderProvidersBlackList;

    private boolean peristentOrderMigrationToSpacesEnabled;
    private Map<String, Double> twapRegularSizeMap = new HashMap<String, Double>();
    private Double defaultTwapRegularSize;

    private long directedOrderSubmitAlertTime;

	private boolean defaultDOSanityFilterEnabled = false;
	private double defaultDOThresholdPercentage = 1d;
	private Map<String, Boolean> isDOEnabledForCurrencyMap = new HashMap<String, Boolean>();
	private Map<String, Double> doThresholdPercentage = new HashMap<String, Double>();

    private long fixingTwapStartInterval;
    
    private Collection<Organization> mdfEnabledOrgs;

    private String defaultAggregatedFillPriceImprovementAlgorithm;
    private Map<String,String> aggregatedFillPriceImprovementAlgorithmMap = new HashMap<String, String>();

    private String defaultAggregatedFillPriceImprovementFeeTypeToCustomer;
    private Map<String,String> aggregatedFillPriceImprovementFeeTypeToCustomerMap = new HashMap<String, String>();
    
    private double defaultAggregatedFillPriceImprovementPercentageToCustomer;
    private Map<String,Double> aggregatedFillPriceImprovementPercentageToCustomerMap = new HashMap<String, Double>();
    
    private double defaultAggregatedFillPriceImprovementFixedFeeToCustomer;
    private Map<String,Double> aggregatedFillPriceImprovementFixedFeeToCustomerMap = new HashMap<String, Double>();

    private boolean routeUnmatchedAmountToClobEnabled;
    private Map<String,Boolean> routeUnmatchedAmountToClobEnabledMap = new HashMap<String,Boolean>();

	private boolean routeTermCCYAmountToClobEnabled;
	private Map<String,Boolean> routeTermCCYAmountToClobEnabledMap = new HashMap<String,Boolean>();
    
    private int routeUnmatchedAmountToClobMaxNoOfTimes;
    private Map<String,Integer> routeUnmatchedAmountToClobMaxNoOfTimesMap = new HashMap<String,Integer>();

    
	private boolean routePartiallyFilledUnmatchedAmountToClobEnabled;
	private Map<String,Boolean> routePartiallyFilledUnmatchedAmountToClobEnabledMap = new HashMap<String,Boolean>();

	private boolean checkMarketabilityOfVenueRoutedOrdersEnabled;
	private Map<String,Boolean> checkMarketabilityOfVenueRoutedOrdersEnabledMap = new HashMap<String, Boolean>();
	private boolean venueReRoutingOrdersEnabled;
	private Map<String,Boolean> venueReRoutingOrdersEnabledMap = new HashMap<String, Boolean>();

    private int minRestingTimeInRiskNet;
    private Map<String,Integer> minRestingTimeInRiskNetMap = new HashMap<String,Integer>();

    private boolean isMarketSnapshotTruncateEnabled = true;

	private boolean smartSlicingEnabled;	
	private Map<String,Boolean> smartSlicingEnabledMap;

	private int smartSlicingMaxSliceInterval;
	private Map<String, Integer> smartSlicingMaxSliceIntervalMap;

	private int smartSlicingMinSliceInterval;
	private Map<String, Integer> smartSlicingMinSliceIntervalMap;

	private int smartSlicingOptimalSliceInterval;
	private Map<String, Integer> smartSlicingOptimalSliceIntervalMap;

	private int smartSlicingOptimalSliceSize;
	private Map<String, Integer> smartSlicingOptimalSliceSizeMap;

	private double smartSlicingSliceIntervalRandomization;
	private Map<String, Double> smartSlicingSliceIntervalRandomizationMap;

	private double smartSlicingSliceSizeCeilingFactor;
	private Map<String, Double> smartSlicingSliceSizeCeilingFactorMap;

	private double smartSlicingSliceSizeRandomization;
	private Map<String, Double> smartSlicingSliceSizeRandomizationMap;

	private double smartSlicingTIFForTWAP;
	private Map<String, Double> smartSlicingTIFForTWAPMap;

	private double smartSlicingTriggerAmount;
	private Map<String, Double> smartSlicingTriggerAmountMap;
	
	
	
	private int switchAlgoAbsoluteAggressionInterval;
	private Map<String, Integer> switchAlgoAbsoluteAggressionIntervalMap;
	
	private double switchAlgoPercentageAggressionInterval;
	private Map<String, Double> switchAlgoPercentageAggressionIntervalMap;
	
	private boolean smartAdjustingTWAPEnabled;
	private Map<String, Boolean> smartAdjustingTWAPEnabledMap;
	
	
	private double faStreamTolerance;
	private Map<String, Double> faStreamToleranceMap;

	private boolean allowFAMatchIfNonFANotAvailable;
	private Map<String,Boolean> allowFAMatchIfNonFANotAvailableMap;
	private boolean sortQuotes;
	private Collection<String> marketOrderRoutedToClob;
	private Map<String, Collection<String>> marketOrderRoutedToClobMap;
	private Map<String, Boolean> isV4EMSEnabledMap;
	private boolean isV4EMSEnabled;

	private boolean isLocalV4EMSEnabled;
	private Map<String, Boolean> isLocalV4EMSEnabledMap;

	private byte directedOrderResponseMaxStringLength;

	private boolean routeFixingOrderToOMSWithAutoAccept;
	private Map<String, Boolean> routeFixingOrderToOMSWithAutoAcceptMap;
	private Map<String, Boolean> showRejectReasonMap;

	private  Boolean showRejectReasonDefault;

	private Map<String,Integer> zeroLiquidityActiveQuotesInOrderBookFlagsMap;
	private int zeroLiquidityActiveQuotesInOrderBookFlags;
	private int loginChannelNotificationMode;
	private Map<String, Boolean> exDestinationOrderAmendEnabledMap;
	private boolean exDestinationOrderAmendEnabled;

	private Map<String,Boolean> linkedOCOCancelEnabledMap;
	private boolean linkedOCOCancelEnabled;

	private boolean omsOrderCreditEnabled;
	private Map<String, Boolean> omsOrderCreditEnabledMap;
	private boolean undisclosedProviderMatchDisabled;
	private Map<String, Boolean> undisclosedProviderMatchDisabledMap;

	private OrderConfiguration()
	{
		super("com.integral.adaptor.order.configuration.OrderConfigurationMBean");
	}

	/**
	 * Returns a singleton instance.
	 *
	 * @return a singleton instance of <code>OrderConfigurationMBean</code>
	 */
	public static OrderConfigurationMBean getInstance()
	{
		return instance;
	}

	@Override
	public void initialize()
	{
		super.initialize();
		loadProperties();
	}

	public boolean isDisplayedOrdersSanityFilterEnabled(String ccyPair) {
		Boolean retVal = isDOEnabledForCurrencyMap == null ? null : isDOEnabledForCurrencyMap.get(ccyPair);

		return retVal == null ? defaultDOSanityFilterEnabled : retVal;
	}

	public double getDisplayedOrdersSanityFilterPercentage(String ccyPair) {
		Double retVal = doThresholdPercentage == null ? null : doThresholdPercentage.get( ccyPair );

		return retVal == null ? defaultDOThresholdPercentage : retVal;
	}

	public int getOrderPublishingInterval()
	{
		return orderPublishingInterval;
	}

	public int getQuotePublicationInterval( String organization )
	{
		final String key = getKey(QUOTE_PUBLICATION_INTERVAL, organization);
		return getIntProperty(key, quotePublicationInterval);
	}

	public int getQuotePublicationInterval()
	{
		return quotePublicationInterval;
	}

	public int getBestBidOfferPublicationInterval( String organization )
	{
		final String key = getKey(BEST_BIDOFFER_PUBLICATION_INTERVAL, organization);
		return getIntProperty(key, bestBidOfferPublicationInterval);
	}

	public int getBestBidOfferPublicationInterval()
	{
		return bestBidOfferPublicationInterval;
	}

	public boolean isIntrafloorMatchingEnabled()
	{
		return intrafloorMatchingEnabled;
	}

	public int getOrderCancellationPeriod()
	{
		return orderCancellationPeriod;
	}

	public boolean cancelAllNonPersistentActiveOrders()
	{
		return cancelAllNonPersistentActiveOrders;
	}

	public int getOrderStatusQueryPeriod()
	{
		return orderStatusQueryPeriod;
	}

	public boolean isSendOrderMassStatusEnabled()
	{
		return sendOrderMassStatusEnabled;
	}

	public int getOrderExecutorsCorePoolSize()
	{
		return orderExecutorsCorePoolSize;
	}

	public int getOrderExecutorsMaxPoolSize()
	{
		return orderExecutorsMaxPoolSize;
	}

	public int getOrderExecutorsPoolKeepAliveTime()
	{
		return orderExecutorsPoolKeepAliveTime;
	}

	public int getQuoteExecutorsCorePoolSize()
	{
		return quoteExecutorsCorePoolSize;
	}

	public int getQuoteExecutorsMaxPoolSize()
	{
		return quoteExecutorsMaxPoolSize;
	}

	public int getQuoteExecutorsPoolKeepAliveTime()
	{
		return quoteExecutorsPoolKeepAliveTime;
	}

	protected String getKey( String propertyName, String detail )
	{
		final String key;
		if ( detail == null )
		{
			key = propertyName;
		}
		else
		{
			StringBuilder sb = new StringBuilder(53).append(propertyName).append('.').append(detail);
			key = sb.toString();
		}
		return key;
	}

	public String getTradeRejectionReason()
	{
		return tradeRejectionReason;
	}

	/**
	 * {@inheritDoc}
	 * <p/>
	 * This implementation delegates cancelling an order to  <code>UnsolicitedOrderCancelHandlerC</code>.
	 *
	 * @see UnsolicitedOrderCancelHandlerC
	 */
	public final boolean cancelOrder( final String orderID )
	{
		final UnsolicitedOrderCancelHandlerC cancelHandlerC = new UnsolicitedOrderCancelHandlerC();
		return cancelHandlerC.cancelOrder(orderID);
	}

	public boolean isOrderBroadcastEnabled()
	{
		return orderBroadcastEnabled;
	}

	public String getOMSLogCategory()
	{
		return oMSLogCategory;
	}

	public String getOrderAdaptorProviderList()
	{
		return orderAdaptorProviderList;
	}

	public double getOrderMinimumMarketRateAmount()
	{
		return orderMinimumMarketRateAmount;
	}

	public int getOrderBroadcasterExecutorsCorePoolSize()
	{
		return orderBroadcasterExecutorsCorePoolSize;
	}

	public int getOrderBroadcasterExecutorsMaxPoolSize()
	{
		return orderBroadcasterExecutorsMaxPoolSize;
	}

	public int getOrderBroadcasterExecutorsPoolKeepAliveTime()
	{
		return orderBroadcasterExecutorsPoolKeepAliveTime;
	}

	public boolean isDirectOASetup()
	{
		return directOASetup;
	}

	public boolean isBroadcastOnlySetup()
	{
		return broadcastOnlySetup;
	}

	public boolean isRematchAfterDelayedRejection()
	{
		return rematchAfterDelayedRejection;
	}

	public String getRematchAfterDelayedRejectionOrderTypes()
	{
		return rematchAfterDelayedRejectionOrderTypes;
	}

	public Collection<String> getRematchAfterDelayedRejectionOrderClassifications()
	{
		return rematchAfterDelayedRejectionOrderClassifications;
	}

	public double getRematchAfterDelayedRejectionMaxSlippage()
	{
		return rematchAfterDelayedRejectionMaxSlippage;
	}

	public int getVWAPExecutionNumberOfBands()
	{
		return VWAPExecutionNumberOfBands;
	}

	public double getVWAPExecutionBandFloor()
	{
		return VWAPExecutionBandFloor;
	}

	public double getVWAPExecutionMinimumBandSize()
	{
		return VWAPExecutionMinimumBandSize;
	}

	public int getTakerOrderExecutorsCorePoolSize()
	{
		return takerOrderExecutorsCorePoolSize;
	}

	public int getTakerOrderExecutorsMaxPoolSize()
	{
		return takerOrderExecutorsMaxPoolSize;
	}

	public int getTakerOrderExecutorsPoolKeepAliveTime()
	{
		return takerOrderExecutorsPoolKeepAliveTime;
	}

	/**
	 * Load properties from the database/property files.
	 */
	private void loadProperties()
	{
        this.mdfEnabledOrgs = getMDFEnabledOrganizations();

		this.orderPublishingInterval = getIntProperty(ORDER_BOOK_PUBLISHING_INTERVAL, 5000);
		this.quotePublicationInterval = getIntProperty(QUOTE_PUBLICATION_INTERVAL, 250);
		this.bestBidOfferPublicationInterval = getIntProperty(BEST_BIDOFFER_PUBLICATION_INTERVAL, 50);
		this.intrafloorMatchingEnabled = getBooleanProperty(ORDER_INTRAFLOOR_MATCHING_ENABLED, false);
		//this.providerList = getStringProperty( ORDER_PROVIDERS_LIST_SHORTNAME, "" );
		this.orderCancellationPeriod = getIntProperty(ORDER_CANCELLATION_PERIOD, 2);
		this.cancelAllNonPersistentActiveOrders = getBooleanProperty( IDC_OA_CANCEL_ALL_NONPERSISTENT_ACTIVE_ORDERS, false);
		this.orderStatusQueryPeriod = getIntProperty(ORDER_STATUS_QUERY_PERIOD, 2);
		this.sendOrderMassStatusEnabled = getBooleanProperty(ORDER_MASS_STATUS_REQUEST_ENABLED, true);

		this.orderExecutorsCorePoolSize = getIntProperty(ORDER_EXECUTORS_POOL_CORE_SIZE, 50);
		this.orderExecutorsMaxPoolSize = getIntProperty(ORDER_EXECUTORS_POOL_MAX_SIZE, 500);
		this.orderExecutorsPoolKeepAliveTime = getIntProperty(ORDER_EXECUTORS_POOL_KEEP_ALIVE_TIME, 60);

		this.quoteExecutorsCorePoolSize = getIntProperty(QUOTE_EXECUTORS_POOL_CORE_SIZE, 50);
		this.quoteExecutorsMaxPoolSize = getIntProperty(QUOTE_EXECUTORS_POOL_MAX_SIZE, 500);
		this.quoteExecutorsPoolKeepAliveTime = getIntProperty(QUOTE_EXECUTORS_POOL_KEEP_ALIVE_TIME, 60);

		this.tradeRejectionReason = getStringProperty(ORDER_FILLED_TRADE_REJECTION_REASON, "Price no longer valid..");

		this.orderBroadcasterExecutorsCorePoolSize = getIntProperty(ORDERBROADCASTER_EXECUTORS_POOL_CORE_SIZE, 5);
		this.orderBroadcasterExecutorsMaxPoolSize = getIntProperty(ORDERBROADCASTER_EXECUTORS_POOL_MAX_SIZE, 5);
		this.orderBroadcasterExecutorsPoolKeepAliveTime = getIntProperty(ORDERBROADCASTER_EXECUTORS_POOL_KEEP_ALIVE_TIME, 60);

		this.broadcastOnlySetup = getBooleanProperty(BROADCAST_ORDER_ADAPTOR_SETUP, false);
		this.directOASetup = !broadcastOnlySetup;
		this.rematchAfterDelayedRejection = getBooleanProperty(REMATCH_AFTER_DELAYED_REJECTION, false);
		this.rematchAfterDelayedRejectionOrderTypes = getStringProperty(REMATCH_AFTER_DELAYED_REJECTION_ORDER_TYPES, null);
		this.rematchAfterDelayedRejectionOrderClassifications = initRematchAfterDelayedRejectionOrderClassifications();
		this.rematchAfterDelayedRejectionMaxSlippage = Double.parseDouble(getStringProperty(REMATCH_AFTER_DELAYED_REJECTION_MAX_SLIPPAGE, "-1.0"));
		this.orderBroadcastEnabled = getBooleanProperty(IDC_IS_ORDER_BROADCAST_ENABLED, false);
		this.oMSLogCategory = getStringProperty(IDC_IS_OMS_LOGF4J_CATEGORY, "com.integral.is.oms");
		this.orderAdaptorProviderList = getStringProperty(IDC_IS_ORDER_PROVIDERS_LIST_SHORTNAME, "");
		this.orderMinimumMarketRateAmount = new Double(getStringProperty(IDC_IS_ORDER_MINIMUM_MARKETRATE_AMOUNT, "1000"));
		this.VWAPExecutionNumberOfBands = getIntProperty(VWAP_EXECUTION_BANDS_NUMBER, 10);
		this.VWAPExecutionBandFloor = Double.parseDouble(getStringProperty(VWAP_EXECUTION_BAND_FLOOR, VWAP_EXECUTION_BAND_FLOOR_DEFAULT));
		this.VWAPExecutionMinimumBandSize = Double.parseDouble(getStringProperty(VWAP_EXECUTION_MINIMUM_BAND_SIZE, VWAP_EXECUTION_MINIMUM_BAND_SIZE_DEFAULT));
		this.takerOrderExecutorsCorePoolSize = getIntProperty(TAKER_ORDER_EXECUTORS_POOL_CORE_SIZE, 10);
		this.takerOrderExecutorsMaxPoolSize = getIntProperty(TAKER_ORDER_EXECUTORS_POOL_MAX_SIZE, 500);
		this.takerOrderExecutorsPoolKeepAliveTime = getIntProperty(TAKER_ORDER_EXECUTORS_POOL_KEEP_ALIVE_TIME, 60);
		this.hiddenProvidersEnabled = getBooleanProperty(IDC_IS_ORDER_HIDDEN_PROVIDERS_ENABLED, false);
		this.unSpreadedProvidersEnabled = getBooleanProperty(IDC_IS_ORDER_UNSPREADED_PROVIDERS_ENABLED, false);
		this.spreadedProviderProperTierAssociationEnabled = getBooleanProperty(IDC_IS_ORDER_SPREADED_PROVIDERS_ASSOCIATE_PROPER_TIER, true);
		this.excludedProvidersEnabled = getBooleanProperty(IDC_IS_ORDER_EXCLUDED_PROVIDERS_ENABLED, false);
		this.isCreditCheckEnabled = getBooleanProperty(IDC_IS_ORDER_MATCH_CREDIT_CHECK_ENBLED, true);
		this.useQuoteExecutors = getBooleanProperty(IDC_IS_ORDER_USE_QUOTEEXECUTORS, false);
        this.useExecutorsForRouting = getBooleanProperty(IDC_IS_ORDER_USE_EXECUTORS_FOR_ROUTING, false);
		this.useExecutorsForCheckingMarketability = getBooleanProperty(IDC_IS_ORDER_USE_EXECUTORS_FOR_CHECKING_MARKETABILITY, false);
		this.routeOrderToOMS = convertToEnum(getStringProperty(ROUTE_ORDER_TO_OMS , null));
		this.routeOrderToOMSMap = convertToEnumMap(initMultipleSuffixStringPropertyMap(ROUTE_ORDER_TO_OMS + ".", null));		
		this.omsManualOrderAcceptanceExpiryTime = getLongProperty(OMS_MANUAL_ORDER_ACCEPTANCE_EXPIRY_TIME, 60000);
		this.omsManualOrderAcceptanceExpiryTimeMap = initSingleSuffixLongPropertyMap(OMS_MANUAL_ORDER_ACCEPTANCE_EXPIRY_TIME + ".", null);		
		this.routeLinkedOrderToOMS = getBooleanProperty(ROUTE_LINKED_ORDER_TO_OMS , false);
		this.routeLinkedOrderToOMSMap = initMultipleSuffixBooleanPropertyMap(ROUTE_LINKED_ORDER_TO_OMS + ".", null, false);
		this.omsCustomerOrderAdditionalStates = getBooleanProperty(OMS_CUSTOMER_ORDER_ADDITIONAL_STATES , true);
		this.omsCustomerOrderAdditionalStatesMap = initMultipleSuffixBooleanPropertyMap(OMS_CUSTOMER_ORDER_ADDITIONAL_STATES + ".", null, omsCustomerOrderAdditionalStates);
		this.omsCustomerOrderPendingNewWithOrderId = getBooleanProperty(OMS_CUSTOMER_ORDER_PENDING_NEW_WITH_ORDERID , true);
		this.omsCustomerOrderPendingNewWithOrderIdMap = initMultipleSuffixBooleanPropertyMap(OMS_CUSTOMER_ORDER_PENDING_NEW_WITH_ORDERID + ".", null, omsCustomerOrderPendingNewWithOrderId);
		this.routeOrderToOMSWithAuto = getBooleanProperty(ROUTE_ORDER_TO_OMS_WITH_AUTO , false);
		this.routeOrderToOMSWithAutoMap = initMultipleSuffixBooleanPropertyMap(ROUTE_ORDER_TO_OMS_WITH_AUTO_PREFIX, null, false);
		this.routeFixingOrderToOMSWithAutoAccept = getBooleanProperty(ROUTE_FIXING_ORDER_TO_OMS_WITH_AUTO_ACCEPT , true);
		this.routeFixingOrderToOMSWithAutoAcceptMap = initMultipleSuffixBooleanPropertyMap(ROUTE_FIXING_ORDER_TO_OMS_WITH_AUTO_ACCEPT_PREFIX, null, null);
		this.isLocalV4EMSEnabled = getBooleanProperty(LOCAL_V4_ENABLED,false);
		this.directedOrderResponseMaxStringLength = (byte)getIntProperty(DIRECTEDORDER_RESPONSE_MAXSTRING_LENGTH,50);
		this.omsOrderCreditEnabled = getBooleanProperty(OMS_ORDER_CREDIT_ENABLED , false);
		this.omsOrderCreditEnabledMap = initMultipleSuffixBooleanPropertyMap(OMS_ORDER_CREDIT_ENABLED_PREFIX, null, omsOrderCreditEnabled);


		//clear the map so that next call of getOrderAdaptorHiddenProviderList will regenerate it from property
		if ( orderAdaptorHiddenProvidersMap == null )
		{
			orderAdaptorHiddenProvidersMap = new ConcurrentHashMap<String, List<Organization>>();
		}
		orderAdaptorHiddenProvidersMap.clear();

		if ( orderAdaptorUnSpreadedProvidersMap == null )
		{
			orderAdaptorUnSpreadedProvidersMap = new ConcurrentHashMap<String, List<Organization>>();
		}
		orderAdaptorUnSpreadedProvidersMap.clear();

		orderAdaptorExcludedProvidersMap = initPrefixedPropertyStringMap(IDC_IS_ORDER_EXCLUDED_PROVIDERS_PREFIX);

		maxShowAmountsMap = initPrefixedPropertyDoubleMap(IDC_IS_ORDER_MAX_DISPLAY_AMOUNT_PREFIX);

		// initialises the order provider lists
		resetOrderProviderOrgsList(false);

		//OP Adjustment property
		opAdjustmentEnabled = getBooleanProperty(IDC_IS_ORDER_OPADJUSTMENT_ENABLED, false);

		opAdjustmentPipsMap = initOPAdjustmentPipsMap(IDC_IS_ORDER_OPADJUSTMENT_PREFIX);

		opAdjustmenntAllowedChannels = initCommaSperatedPropertyList(IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_CHANNELS, IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_CHANNELS_DEFAULT);

		opAdjustmenntAllowedOrderTypes = initCommaSperatedPropertyList(IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_ORDERTYPES, IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_ORDERTYPES_DEFAULT);

		isCircuitBreakerEnabled = getBooleanProperty(IDC_ORDERADAPTOR_CIRCUIT_BREAKERS_ENABLED, false);
		circuitBreakerThresholds = initCircuitBreakerThreshold(IDC_ORDERADAPTOR_REJECTION_DISABLEPROVIDER);

		// initialize the flash order related properties.
        initOrderFlashCacheMaps();

        String custRegSizeProp = getStringProperty(IDC_CUSTOMER_REGULAR_SIZE, null);
		customerRegularSize = custRegSizeProp != null && custRegSizeProp.trim().length() > 0 ? getDoubleProperty(IDC_CUSTOMER_REGULAR_SIZE, 0.0) : null;
		customerRegularSizeMap = initSingleSuffixDoublePropertyMap(IDC_CUSTOMER_REGULAR_SIZE_PREFIX, null);
		orderSliceTTL = getIntProperty(IDC_ORDER_STRATEGY_TTL, 0);
		orderMinSliceInterval = getIntProperty(IDC_ORDER_STRATEGY_MINSLICEINTERVAL, 0);
		makerTakerNSs = new HashSet<Namespace>();
		initMakerTakerNamespaces();
		initIOCExpiryTimes();
		initSetOrderRateToFillRate();
		defaultMKTRangeMap = initMultipleSuffixDoublePropertyMap(IDC_ORDER_DEFAULTMKTRANGE_PREFIX, null);
		this.defaultMKTRange = getDoubleProperty(IDC_ORDER_DEFAULTMKTRANGE, 0);

        defaultPercentageMKTRangeMap = initMultipleSuffixDoublePropertyMap(IDC_ORDER_PERCENTAGE_MKTRANGE_PREFIX, null);
        this.defaultPercentageMKTRange = getDoubleProperty(IDC_ORDER_PERCENTAGE_MKTRANGE, 0);

		initOptimizedFOKExecutionParameters();

		defaultProviderSuspensionPeriod = getIntProperty(IDC_OA_REJ_DisableProvider_TimePeriod, 0);
		providerSuspensionPeriodMap = initSingleSuffixIntegerPropertyMap(IDC_OA_REJ_DisableProvider_TimePeriod_prefix, null);

		isFillAtMarketExecInstTOB = getBooleanProperty(IDC_OA_FAM_EXECINST_TOB, false);
		fillAtMarketExecInstTOBMap = initSingleSuffixBooleanPropertyMap(IDC_OA_FAM_EXECINST_TOB_PREFIX, null);

		initEffectiveTimePropertyMaps();

		maxBroadcastTiersDefault = getIntProperty(IDC_DO_MAX_NUM_TIER, 10);
		maxBroadcastTiersMap = initSingleSuffixIntegerPropertyMap(IDC_DO_MAX_NUM_TIER_PREFIX, null);

		rejectAdditionalPrecisionOrders = getBooleanProperty(IDC_ORDERADAPTOR_REJECTADDITIONALPRECISIONORDERS, true);
		initDirectOrderToBAMaps();
		initDirectOrderToLpMaps();

        initProviderQuotesCacheMaps();
	    termCurrencyBroadcastEnabled = getBooleanProperty( IDC_DO_TERM_CURRENCY_BROADCAST_ENABLED, false );
        termCurrencyBroadcastEnabledMap = initSingleSuffixBooleanPropertyMap( IDC_DO_TERM_CURRENCY_BROADCAST_ENABLED_PREFIX, null );

        algoFillAtMktExpiryInterval = getIntProperty(IDC_ALGO_FAM_EXPIRY_INTERVAL, 30000);
        displayOrderProvidersBlackList = initCommaSperatedPropertyList(IDC_IS_DISPLAY_ORDER_PROVIDERS_BLACK_LIST, "");

        peristentOrderMigrationToSpacesEnabled = getBooleanProperty(IDC_OA_PERSISTENT_ORDER_MIGRATION_ENABLED,false);

        defaultTwapRegularSize = getDoubleProperty(IDC_TWAP_REGULAR_SIZE, 100000.0d); // default value is 100K
        twapRegularSizeMap = initSingleSuffixDoublePropertyMap(IDC_TWAP_REGULAR_SIZE_PREFIX, null);
        this.directedOrderSubmitAlertTime = getLongProperty( IDC_TRADING_VENUE_ORDER_ACK_ALERT_TIME,5000 );
        this.minRestingTimeInRiskNetMap = initSingleSuffixIntegerPropertyMap(IDC_RISKNET_DIRECTED_ORDER_MIN_RESTING_TIME_PREFIX, null);
        this.minRestingTimeInRiskNet = getIntProperty(IDC_RISKNET_DIRECTED_ORDER_MIN_RESTING_TIME, 3000);

		this.defaultDOSanityFilterEnabled = getBooleanProperty( IDC_DISPLAY_ORDER_SANITY_FILTER_ENABLED, false );
		this.defaultDOThresholdPercentage = getDoubleProperty( IDC_DISPLAY_ORDER_SANITY_FILTER_PERCENTAGE, 1d );
		this.isDOEnabledForCurrencyMap = initSingleSuffixBooleanPropertyMap(IDC_DISPLAY_ORDER_SANITY_FILTER_ENABLED, null);
		this.doThresholdPercentage = initSingleSuffixDoublePropertyMap(IDC_DISPLAY_ORDER_SANITY_FILTER_PERCENTAGE, null);
        this.fixingTwapStartInterval = getLongProperty(IDC_ORDER_FIXING_TWAP_START_INTERVAL, 150000);
        initAggregatedFillPriceImprovementMaps();
		initVenueRoutingEnabledMaps();
        isMarketSnapshotTruncateEnabled = getBooleanProperty(IDC_ORDERADAPTOR_MARKET_SNAPSHOT_TRUNCATE_ENABLED, true);
        
        smartSlicingEnabled = getBooleanProperty(SmartSlicing_Enabled, false);
        smartSlicingEnabledMap = initSingleSuffixBooleanPropertyMap(SmartSlicing_Enabled_prefix, null);
        
        smartSlicingMaxSliceInterval = getIntProperty(SmartSlicing_MaxSliceInterval, 500);
        smartSlicingMaxSliceIntervalMap = initSingleSuffixIntegerPropertyMap(SmartSlicing_MaxSliceInterval_prefix, null);
     
        smartSlicingMinSliceInterval = getIntProperty(SmartSlicing_MinSliceInterval, 500);
    	smartSlicingMinSliceIntervalMap = initSingleSuffixIntegerPropertyMap(SmartSlicing_MinSliceInterval_prefix, null);

    	smartSlicingOptimalSliceInterval = getIntProperty(SmartSlicing_OptimalSliceInterval, 500);
    	smartSlicingOptimalSliceIntervalMap = initSingleSuffixIntegerPropertyMap(SmartSlicing_OptimalSliceInterval_prefix, null);

    	smartSlicingOptimalSliceSize = getIntProperty(SmartSlicing_OptimalSliceSize, 500000);
    	smartSlicingOptimalSliceSizeMap =initSingleSuffixIntegerPropertyMap(SmartSlicing_OptimalSliceSize_prefix, null );

    	smartSlicingSliceIntervalRandomization = getDoubleProperty(SmartSlicing_SliceIntervalRandomization, 0.15);
    	smartSlicingSliceIntervalRandomizationMap = initSingleSuffixDoublePropertyMap(SmartSlicing_SliceIntervalRandomization_prefix, null);

    	smartSlicingSliceSizeCeilingFactor = getDoubleProperty(SmartSlicing_SliceSizeCeilingFactor, 25000);
    	smartSlicingSliceSizeCeilingFactorMap = initSingleSuffixDoublePropertyMap(SmartSlicing_SliceSizeCeilingFactor_prefix, null);

    	smartSlicingSliceSizeRandomization = getDoubleProperty(SmartSlicing_SliceSizeRandomization, 0.15);
    	smartSlicingSliceSizeRandomizationMap = initSingleSuffixDoublePropertyMap(SmartSlicing_SliceSizeRandomization_prefix,null);

    	smartSlicingTIFForTWAP = getDoubleProperty(SmartSlicing_TIFForTWAP, 0.8);
    	smartSlicingTIFForTWAPMap = initSingleSuffixDoublePropertyMap(SmartSlicing_TIFForTWAP_prefix, null);

    	smartSlicingTriggerAmount = getDoubleProperty(SmartSlicing_TriggerAmount, 5000000);
    	smartSlicingTriggerAmountMap = initSingleSuffixDoublePropertyMap(SmartSlicing_TriggerAmount_prefix,null);
    	
    	switchAlgoAbsoluteAggressionInterval = getIntProperty(SwitchAlgo_AggressiveInterval_Absolute, 3000);
    	switchAlgoAbsoluteAggressionIntervalMap = initSingleSuffixIntegerPropertyMap(SwitchAlgo_AggressiveInterval_Absolute_Prefix, null);
    	
    	switchAlgoPercentageAggressionInterval = getDoubleProperty(SwitchAlgo_AggressiveInterval_Percentage, 0.6);
    	switchAlgoPercentageAggressionIntervalMap = initSingleSuffixDoublePropertyMap(SwitchAlgo_AggressiveInterval_Percentage_Prefix, null);
    	
    	smartAdjustingTWAPEnabled = getBooleanProperty(Smart_Adjusting_TWAP, false);
    	smartAdjustingTWAPEnabledMap = initSingleSuffixBooleanPropertyMap(Smart_Adjusting_TWAP_Prefix, null);
    	
    	faStreamTolerance = getDoubleProperty(FULL_AMOUNT_STREAM_TOLERANCE, 0.0);
    	faStreamToleranceMap = initMultipleSuffixDoublePropertyMap(FULL_AMOUNT_STREAM_TOLERANCE_prefix, null);

		allowFAMatchIfNonFANotAvailable = getBooleanProperty(FULL_AMOUNT_STREAM_AllowMatchIfNonFANotAvailable,false);
		Map<String,Boolean> bMap = initSingleSuffixBooleanPropertyMap(FULL_AMOUNT_STREAM_AllowMatchIfNonFANotAvailable_Prefix,null);
		if( bMap != null && !bMap.isEmpty()) {
			allowFAMatchIfNonFANotAvailableMap = bMap;
		}
		else {
			allowFAMatchIfNonFANotAvailableMap = null;
		}
    	sortQuotes = getBooleanProperty(SORT_MARKET_SNAPSHOT,false);
		marketOrderRoutedToClob = getStringCollectionProperty(ORDER_TYPES_TO_CLOB, null);
        marketOrderRoutedToClobMap = initMultipleSuffixStringCollectionPropertyMap(ORDER_TYPES_TO_CLOB + '.', null);
		this.isV4EMSEnabledMap = initSingleSuffixBooleanPropertyMap(V4_ENABLED,null,false);
		this.isV4EMSEnabled = getBooleanProperty(V4_ENABLED ,false);
		this.isLocalV4EMSEnabledMap = initSingleSuffixBooleanPropertyMap(LOCAL_V4_ENABLED,null,false);
		this.showRejectReasonDefault = getBooleanProperty(SHOW_REJECT_REASON,false);
		this.showRejectReasonMap = initSingleSuffixBooleanPropertyMap(SHOW_REJECT_REASON,null,showRejectReasonDefault);
		Map<String,Integer> iMap = initMultipleSuffixIntegerPropertyMap(ZERO_LIQUIDITY_ACTIVE_QUOTES_IN_ORDER_BOOK_FLAGS+".",null);
		if( iMap == null || iMap.isEmpty()){
			this.zeroLiquidityActiveQuotesInOrderBookFlagsMap = null;
		}
		else{
			this.zeroLiquidityActiveQuotesInOrderBookFlagsMap = iMap;
		}
		zeroLiquidityActiveQuotesInOrderBookFlags = getIntProperty(ZERO_LIQUIDITY_ACTIVE_QUOTES_IN_ORDER_BOOK_FLAGS,-1);
		loginChannelNotificationMode = getIntProperty(LOGIN_CHANNEL_NOTIFICATION_MODE,0);
		exDestinationOrderAmendEnabled = getBooleanProperty(EXTERNALDESTINATION_ORDERAMEND_ENABLED,false);
		bMap = initSingleSuffixBooleanPropertyMap(EXTERNALDESTINATION_ORDERAMEND_ENABLED,null);
		if( bMap != null && !bMap.isEmpty()) {
			exDestinationOrderAmendEnabledMap = bMap;
		}
		else {
			exDestinationOrderAmendEnabledMap = null;
		}
		bMap = initSingleSuffixBooleanPropertyMap(LINKED_OCO_CANCEL_ENABLED,null);
		if( bMap != null && !bMap.isEmpty()) {
			linkedOCOCancelEnabledMap = bMap;
		}
		else {
			linkedOCOCancelEnabledMap = null;
		}
		linkedOCOCancelEnabled = getBooleanProperty(LINKED_OCO_CANCEL_ENABLED,false);
		undisclosedProviderMatchDisabled = getBooleanProperty(UNDISCLOSED_PROVIDER_MATCH_DISABLED, false);
		undisclosedProviderMatchDisabledMap = initMultipleSuffixBooleanPropertyMap(UNDISCLOSED_PROVIDER_MATCH_DISABLED_PREFIX, null,false);
	}

	public List<OrderRequest.Type> convertToEnum(String value){
		List<OrderRequest.Type> orderTypes = new ArrayList<OrderRequest.Type>();
		if(value != null){
			for(String orderType : Arrays.asList(value.split(","))){
				if(orderType.equalsIgnoreCase(OrderRequest.Type.LIMIT.toString())){
					orderTypes.add(OrderRequest.Type.LIMIT);
				}
				else if(orderType.equalsIgnoreCase(OrderRequest.Type.STOP.toString())){
					orderTypes.add(OrderRequest.Type.STOP);
				}
				else if(orderType.equalsIgnoreCase(OrderRequest.Type.STOPLIMIT.toString())){
					orderTypes.add(OrderRequest.Type.STOPLIMIT);
				}
			}
		}
		
		return orderTypes;
	}
	
	public Map<String, List<OrderRequest.Type>> convertToEnumMap(Map<String, String> values){
		Map<String, List<OrderRequest.Type>> orderTypesMap = new HashMap<String, List<OrderRequest.Type>>();
		for(Map.Entry<String, String> entry : values.entrySet()){
			orderTypesMap.put(entry.getKey(), convertToEnum(entry.getValue()));
		}		
		return orderTypesMap;
	}
	
	public List<OrderRequest.Type> getOrderTypeRouteToOMS(String brokerName, String fiName){
		List<OrderRequest.Type> orderType = routeOrderToOMSMap.get(brokerName + "." + fiName);
		if(orderType == null){
			orderType = routeOrderToOMSMap.get(brokerName);
			if(orderType == null){
				orderType = routeOrderToOMS;
			}
		}
		return orderType;
	}

	public boolean getOmsOrderCreditEnabled(String brokerName, String fiName) {
		Boolean value = omsOrderCreditEnabledMap.get(brokerName + "." + fiName);
		if(value == null){
			value = omsOrderCreditEnabledMap.get(brokerName);
			if(value == null){
				return omsOrderCreditEnabled;
			}
		}
		return value.booleanValue();
	}
	
	public boolean isLinkedOrderRouteToOMS(String brokerName, String fiName){
		Boolean value = routeLinkedOrderToOMSMap.get(brokerName + "." + fiName);
		if(value == null){
			value = routeLinkedOrderToOMSMap.get(brokerName);
			if(value == null){
				return routeLinkedOrderToOMS;
			}
		}
		return value.booleanValue();
	}

	public boolean isOrderRouteToOMSWithAuto(String brokerName, String fiName){
		Boolean value = routeOrderToOMSWithAutoMap.get(brokerName + "." + fiName);
		if(value == null){
			value = routeOrderToOMSWithAutoMap.get(brokerName);
			if(value == null){
				return routeOrderToOMSWithAuto;
			}
		}
		return value.booleanValue();
	}

	@Override
	public boolean isRouteFixingOrderToOMSWithAutoAccept(String brokerName, String fiName){
		Boolean value = routeFixingOrderToOMSWithAutoAcceptMap.get(brokerName + "." + fiName);
		if(value == null){
			value = routeFixingOrderToOMSWithAutoAcceptMap.get(brokerName);
			if(value == null){
				return routeFixingOrderToOMSWithAutoAccept;
			}
		}
		return value.booleanValue();
	}

	public boolean isOmsCustomerOrderAdditionalStates(String brokerName, String fiName){
		Boolean value = omsCustomerOrderAdditionalStatesMap.get(brokerName + "." + fiName);
		if(value == null){
			value = omsCustomerOrderAdditionalStatesMap.get(brokerName);
			if(value == null){
				return omsCustomerOrderAdditionalStates;
			}
		}
		return value.booleanValue();
	}

	public boolean isOmsCustomerOrderPendingNewWithOrderId(String brokerName, String fiName){
		Boolean value = omsCustomerOrderPendingNewWithOrderIdMap.get(brokerName + "." + fiName);
		if(value == null){
			value = omsCustomerOrderPendingNewWithOrderIdMap.get(brokerName);
			if(value == null){
				return omsCustomerOrderPendingNewWithOrderId;
			}
		}
		return value;
	}

	public Long getOMSManualOrderAcceptanceExpiryTime(String brokerName){
		if (brokerName != null) {
			Long value = omsManualOrderAcceptanceExpiryTimeMap.get(brokerName);
			if (value != null) {
				return value;
			}
		}
		return omsManualOrderAcceptanceExpiryTime;
	}
	
	private Collection<Organization> getMDFEnabledOrganizations() 
	{
		List<Organization> orgs = new ArrayList<Organization>(25);
		if(RuntimeFactory.getServerRuntimeMBean().isOCXDeploymentEnabled() 
	       && VirtualServerType.MarketDataFeedServer.equals( ConfigurationFactory.getServerMBean().getVirtualServerType() ) )
		{
			for( Organization org :  ReferenceDataCacheC.getInstance().getOrgMap().values() )
			{
				if( org.isMarketDataFeedServerEnabled() )
				{
					orgs.add(org);
				}
			}
		}
		return orgs;
	}

	private void initEffectiveTimePropertyMaps()
	{
		isOrderEffectiveTimeEnabled = getBooleanProperty(IDC_OA_ET_ENABLED, true);
		orderEffectiveTimeEnabledMap = initSingleSuffixBooleanPropertyMap(IDC_OA_ET_ENABLED_PREFIX, null);
		orderEffectiveTimeToleranceDefault = getLongProperty(IDC_OA_ET_TOLERANCE_DEFAULT, 0);
		orderEffectiveTimeToleranceMap = initSingleSuffixIntegerPropertyMap(IDC_OA_ET_TOLERANCE_PREFIX, null);
	}
	
	private void initDirectOrderToBAMaps()
	{
		isDirectOrderToBAEnabled = getBooleanProperty(IDC_OA_DIRECT_ORDER_BA_ENABLED, false);
		directOrderToBAEnabledMap = initSingleSuffixBooleanPropertyMap(IDC_OA_DIRECT_ORDER_BA_PREFIX, null);
	}

	private void initDirectOrderToLpMaps()
	{
		isDirectOrderToLpEnabled = getBooleanProperty(IDC_OA_DIRECT_ORDER_LP_ENABLED, false);
		directOrderToLpEnabledMap = initMultipleSuffixBooleanPropertyMap(IDC_OA_DIRECT_ORDER_LP_PREFIX, null,false);
	}

    private void initProviderQuotesCacheMaps() {
        defaultProviderQuotesCacheSize = getIntProperty(IDC_OA_PROVIDER_QUOTE_CACHE_SIZE, 10);
        if(defaultProviderQuotesCacheSize <= 0) {
            log.info( "OrderConfiguration.initProviderQuotesCacheSizeMaps() : OA Historical Quote cache size cannot be <= 0. Setting cache size as 1." );
            defaultProviderQuotesCacheSize = 1;
        }
        providerQuotesCacheSizeMap = initSingleSuffixIntegerPropertyMap(IDC_OA_PROVIDER_QUOTE_CACHE_SIZE_PREFIX, null);

        defaultProviderQuoteHistoryMatchDepth = getIntProperty(IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH, 0);
        providerQuoteHistoryMatchDepthMap = initSingleSuffixIntegerPropertyMap( IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH_PREFIX, null);
    }

	private void initVenueRoutingEnabledMaps() {
		this.routeUnmatchedAmountToClobEnabledMap = initSingleSuffixBooleanPropertyMap(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX, null);
		this.routeUnmatchedAmountToClobEnabled = getBooleanProperty(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED, false);
		this.routeTermCCYAmountToClobEnabledMap= initSingleSuffixBooleanPropertyMap(IDC_ORDER_ROUTE_TERMCURRENCY_AMOUNT_ENABLED_PREFIX, null);
		this.routeTermCCYAmountToClobEnabled = getBooleanProperty(IDC_ORDER_ROUTE_TERMCURRENCY_AMOUNT_ENABLED, false);
		
		this.routeUnmatchedAmountToClobMaxNoOfTimesMap = initSingleSuffixIntegerPropertyMap(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_MAX_NO_OF_TIMES, null);
		this.routeUnmatchedAmountToClobMaxNoOfTimes = getIntProperty(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_MAX_NO_OF_TIMES, 1000);
		
		
		this.routePartiallyFilledUnmatchedAmountToClobEnabledMap = initSingleSuffixBooleanPropertyMap(IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX, null);
		this.routePartiallyFilledUnmatchedAmountToClobEnabled = getBooleanProperty(IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED, false);
		this.checkMarketabilityOfVenueRoutedOrdersEnabledMap = initSingleSuffixBooleanPropertyMap(IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED_PREFIX, null);
		this.checkMarketabilityOfVenueRoutedOrdersEnabled = getBooleanProperty(IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED, false);

		this.venueReRoutingOrdersEnabled = getBooleanProperty(IDC_MV_VENUE_RE_ROUTING_ORDERS_ENABLED, false);
		this.venueReRoutingOrdersEnabledMap = initMultipleSuffixBooleanPropertyMap(IDC_MV_VENUE_RE_ROUTING_ORDERS_ENABLED_PREFIX, null, venueReRoutingOrdersEnabled);

	}

    private void initOrderFlashCacheMaps() {
        orderMatchDelay = getIntProperty(IDC_ORDER_MATCH_DELAY, -1);
        orderMatchDelayMap = initSingleSuffixIntegerPropertyMap(IDC_ORDER_MATCH_DELAY_PREFIX, null);
        flashOrderTypes = initCommaSperatedPropertyList(IDC_FLASH_ORDER_TYPES, IDC_FLASH_ORDER_TYPES_DEFAULT);
        flashOrderTypesMap = initSingleSuffixStringCollectionPropertyMap(IDC_FLASH_ORDER_TYPES_PREFIX, null);
        flashOrderTIFs = initCommaSperatedPropertyList(IDC_FLASH_ORDER_TIFS, IDC_FLASH_ORDER_TIFS_DEFAULT);
        flashOrderTIFsMap = initSingleSuffixStringCollectionPropertyMap(IDC_FLASH_ORDER_TIFS_PREFIX, null);
        String minSizeProp = getStringProperty(IDC_FLASH_MIN_SIZE, null);
        flashOrderMinSize = minSizeProp != null && minSizeProp.trim().length() > 0 ? getDoubleProperty(IDC_FLASH_MIN_SIZE, 0.0) : null;
        flashOrderMinSizeMap = initSingleSuffixDoublePropertyMap(IDC_FLASH_MIN_SIZE_PREFIX, null);
        String maxSizeProp = getStringProperty(IDC_FLASH_MAX_SIZE, null);
        flashOrderMaxSize = maxSizeProp != null && maxSizeProp.trim().length() > 0 ? getDoubleProperty(IDC_FLASH_MAX_SIZE, 0.0) : null;
        flashOrderMaxSizeMap = initSingleSuffixDoublePropertyMap(IDC_FLASH_MAX_SIZE_PREFIX, null);
        flashLiftTimeout = getIntProperty(IDC_FLASH_LIFT_TIMEOUT, IDC_FLASH_LIFT_TIMEOUT_DEFAULT);
        flashLiftTimeoutMap = initSingleSuffixIntegerPropertyMap(IDC_FLASH_LIFT_TIMEOUT_PREFIX, null);
        flashUpdatePriceOnMatch = getBooleanProperty(IDC_FLASH_UPDATE_PRICE_ON_MATCH, true);
        flashExpiryPeriod = getIntProperty(IDC_FLASH_EXPIRY, 0);
        if(flashExpiryPeriod < 0) {
            flashExpiryPeriod = 0;
        }
        flashExpiryPeriodMap = initSingleSuffixIntegerPropertyMap(IDC_FLASH_EXPIRY_PREFIX, null);
    }

	/**
	 * 
	 */
	private void initOptimizedFOKExecutionParameters()
	{
		isOptFOKExecEnabled = getBooleanProperty(IDC_FCS_OPT_FOK_EXEC, false);
		optFOKExecMap = initSingleSuffixBooleanPropertyMap(IDC_FCS_OPT_FOK_EXEC_PREFIX, null);
		isSendVWAPPriceOptimizedFOKExecution = getBooleanProperty(IDC_FCS_OPT_FOK_EXEC_PRICETYPE_VWAP, false);
	}

	private void initIOCExpiryTimes()
	{
		iocExpiryTime = getIntProperty(IDC_ORDER_IOC_EXPIRYTIME, 0);
		iocExpiryTimeMap = initIOCExpiryTimeMap(IDC_ORDER_IOC_EXPIRYTIME_PREFIX, IDC_ORDER_IOC_EXPIRYTIME_EXCLUSION_PATTERN);
		iocExpiryTimeForBrokerOrgMap = initSingleSuffixIntegerPropertyMap(IDC_ORDER_IOC_EXPIRYTIME_BRKRCUST_PREFIX, null);
		String iocExpiryTimeEnabledForChannelTemp = getStringProperty(IDC_ORDER_IOC_EXPIRYTIME_CHANNELS, null);
		iocExpiryTimeEnabledForChannel = getRegExpression(iocExpiryTimeEnabledForChannelTemp);
		Map<String, String> iocExpiryTimeEnabledForChannelMapTemp = initSingleSuffixStringPropertyMap(IDC_ORDER_IOC_EXPIRYTIME_CHANNELS_PREFIX, null);
		for ( Map.Entry<String, String> entry : iocExpiryTimeEnabledForChannelMapTemp.entrySet() )
		{
			entry.setValue(getRegExpression(entry.getValue()));
		}
		iocExpiryTimeEnabledForChannelMap = iocExpiryTimeEnabledForChannelMapTemp;

		iocExpiryOnMatch = getBooleanProperty(IDC_ORDER_IOC_EXPIRYTIMEONMATCH, false);
		iocExpiryTimeOnMatchMap = initIOCExpiryOnMatchMap(IDC_ORDER_IOC_EXPIRYTIMEONMATCH_PREFIX, IDC_ORDER_IOC_EXPIRYTIMEONMATCH_EXCLUSION_PATTERN);
		iocExpiryTimeOnMatchForBrokerOrgMap = initSingleSuffixBooleanPropertyMap(IDC_ORDER_IOC_EXPIRYTIMEONMATCH_BRKRCUST_PREFIX, null);
	}

	private void initSetOrderRateToFillRate()
	{
		isSetOrderRateToFillRate = getBooleanProperty(IDC_ORDER_SETORDERRATE_TO_FILLRATE, false);
		orderRateToFillRateMap = initOrderRateToFillRateMap(IDC_ORDER_SETORDERRATE_TO_FILLRATE_PREFIX, IDC_ORDER_SETORDERRATE_TO_FILLRATE_EXCLUSION_PATTERN);
		orderRateToFillRateBrokerOrgMap = initSingleSuffixBooleanPropertyMap(IDC_ORDER_SETORDERRATE_TO_FILLRATE_BRKRCUST_PREFIX, null);
	}

    private void initAggregatedFillPriceImprovementMaps() {
        this.aggregatedFillPriceImprovementAlgorithmMap = initSingleSuffixStringPropertyMap(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_ALGORITHM_PREFIX, null);
        this.defaultAggregatedFillPriceImprovementAlgorithm = getStringProperty(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_ALGORITHM, "COR");

        this.defaultAggregatedFillPriceImprovementPercentageToCustomer = getDoubleProperty(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_PERCENTAGE_TO_CUSTOMER, 1);
        this.aggregatedFillPriceImprovementPercentageToCustomerMap = initSingleSuffixDoublePropertyMap(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_PERCENTAGE_TO_CUSTOMER_PREFIX, null);
        
        this.aggregatedFillPriceImprovementFeeTypeToCustomerMap = initSingleSuffixStringPropertyMap(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FEE_TYPE_PREFIX, null);
        this.defaultAggregatedFillPriceImprovementFeeTypeToCustomer = getStringProperty(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FEE_TYPE, AggregatedFillPriceImprovementFeeType.PERCENTAGE.getId());
        
        this.defaultAggregatedFillPriceImprovementFixedFeeToCustomer = getDoubleProperty(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FIXED_FEE_TO_CUSTOMER, 1);
        this.aggregatedFillPriceImprovementFixedFeeToCustomerMap = initSingleSuffixDoublePropertyMap(IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FIXED_FEE_TO_CUSTOMER_PREFIX, null);
    
    }

	private String getRegExpression( String iocExpiryTimeEnabledForChannelTemp )
	{
		if ( iocExpiryTimeEnabledForChannelTemp != null )
		{
			iocExpiryTimeEnabledForChannelTemp = iocExpiryTimeEnabledForChannelTemp.replaceAll("ALL_DNET", "DNET/.*");
			iocExpiryTimeEnabledForChannelTemp = iocExpiryTimeEnabledForChannelTemp.replaceAll("ALL_PNET", "PNET/.*");
			iocExpiryTimeEnabledForChannelTemp = iocExpiryTimeEnabledForChannelTemp.replaceAll("ALL_FIX", "FIX/.*");
			iocExpiryTimeEnabledForChannelTemp = iocExpiryTimeEnabledForChannelTemp.replaceAll(",", "|");
		}
		return iocExpiryTimeEnabledForChannelTemp;
	}

	private void initMakerTakerNamespaces()
	{
		try
		{
			Collection<Organization> makers = getExtendedOrderProviderOrgsList();
			for ( Organization maker : makers )
			{
				makerTakerNSs.add(maker.getNamespace());
				Collection<Organization> takers = maker.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
				if ( takers != null )
				{
					for ( Organization taker : takers )
					{
						makerTakerNSs.add(taker.getNamespace());
					}
				}
			}
			WarmUpReferenceDataEntityUpdateHandlerC.setAdditionalNamespaces(makerTakerNSs);
		}
		catch ( Exception e )
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	/**
	 * Creates an instance of OrderConfiguration and loads all properties.
	 *
	 * @return an instance of OrderConfiguration with all properties loaded.
	 */
	private static OrderConfiguration createInstanceAndLoadProperties()
	{
		final OrderConfiguration result = new OrderConfiguration();
		result.loadProperties();
		return result;
	}

	/**
	 * Initialises the order type collection which will be rematched on delayed rejection.
	 *
	 * @return order types
	 */
	private Collection<String> initRematchAfterDelayedRejectionOrderClassifications()
	{
		Collection<String> requestClsfs = new ArrayList<String>();
		String orderTypes = getRematchAfterDelayedRejectionOrderTypes();
		if ( orderTypes != null )
		{
			requestClsfs = IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(orderTypes, ","));
		}
		return requestClsfs;
	}

	public void setProperty( String key, String value, int scope, String oldValue )
	{
		try
		{
			super.setProperty(key, value, scope, oldValue);
			if ( IDC_IS_ORDER_PROVIDERS_LIST_SHORTNAME.equals(key) )
			{
				handleOrderProviderListUpdate(key, value, oldValue);
				FIXClientConfig.getInstance().resetAllowedOrganizationsList();
				return;
			}

			if ( value == null || !value.equals(oldValue) )
			{
				if ( key.startsWith(QUOTE_PUBLICATION_INTERVAL) || key.startsWith(BEST_BIDOFFER_PUBLICATION_INTERVAL) )
				{
					//updatePublisherManager(key, value);
					return;
				}

				if ( key.startsWith(IDC_DO_MAX_NUM_TIER) || key.startsWith(SANITY_FILTER_PREFIX) )
				{
					OrderBroadcaster.republishAll();
					return;
				}

				if(key.endsWith( IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH )) {
					updateProviderQuoteHistoryMatchDepth(key, value);
					return;
				}

				if( key.startsWith( IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH_PREFIX ) ) {
					updateOrgLevelProviderQuoteHistoryMatchDepth(key, value);
                    return;
                }

				if(key.endsWith(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED)) {
					updateVenueRoutingFlag(key, value);
					return;
				}

				if(key.startsWith(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_MAX_NO_OF_TIMES_PREXFIX)) {
				  updateVenueRoutingMaxNoOfTimes(key, value);
          return;
        }
				
				
				if(key.startsWith(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX)) {
					updateOrgLevelVenueRoutingFlag(key, value);
					return;
				}

				if(key.endsWith(IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED)) {
					updateVenueRoutingFlagForPartials(key, value);
					return;
				}

				if(key.startsWith(IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX)) {
					updateOrgLevelVenueRoutingFlagForPartials(key, value);
					return;
				}

				if(key.endsWith(IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED)) {
					updateOrderMarketabilityCheckFlag(key, value);
					return;
				}

				if(key.startsWith(IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED_PREFIX)) {
					updateOrgLevelOrderMarketabilityCheckFlag(key, value);
				}


			}
		}
		catch ( Exception e )
		{
			log.error("OrderConfiguration.setProperty :: error while setting the property " + key + " to " + value + " scope" + scope, e);
		}
	}

    private void updateOrgLevelProviderQuoteHistoryMatchDepth( String key, String value )
    {
        int newValue = 0;
        if(value != null) {
            newValue = Integer.valueOf( value );
            if(newValue < 0) {
                newValue = 0;
            }
        }

        String fiOrgName = key.substring( IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH_PREFIX.length() );
        List<OrderBook> orderBooks = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(fiOrgName);
        for (OrderBook i : orderBooks) {
            i.setProviderQuoteHistoryMatchDepth( newValue );
        }
    }

    private void updateProviderQuoteHistoryMatchDepth(String key, String value) {
        int newValue = 0;
        if(value != null) {
            newValue = Integer.valueOf( value );
            if(newValue < 0) {
                newValue = 0;
            }
        }

        Set<String> fiOrgs = providerQuoteHistoryMatchDepthMap.keySet();
        Collection<OrderBook> orderBooks = OrderBookCacheC.getInstance().getOrderBooks().values();
        for(OrderBook orderBook : orderBooks) {
            String fiOrgName = orderBook.getOrganization();
            if(!fiOrgs.contains( fiOrgName )) {
                orderBook.setProviderQuoteHistoryMatchDepth( newValue );
            }
        }
    }
    
    private void updateVenueRoutingMaxNoOfTimes(String key, String value) {
      int newValue = Integer.valueOf(value); 
      Set<String> fiOrgs = routeUnmatchedAmountToClobMaxNoOfTimesMap.keySet();
      Collection<OrderBook> orderBooks = OrderBookCacheC.getInstance().getOrderBooks().values();
      for(OrderBook orderBook : orderBooks) {
        String fiOrgName = orderBook.getOrganization();
        if(fiOrgs.contains( fiOrgName )) {
          orderBook.setMaxNoOfTimesVenueRoutingAllowed(newValue);
        }
      }
    }

	private void updateVenueRoutingFlag(String key, String value) {
		boolean newValue = Boolean.valueOf(value);
		Set<String> fiOrgs = routeUnmatchedAmountToClobEnabledMap.keySet();
		Collection<OrderBook> orderBooks = OrderBookCacheC.getInstance().getOrderBooks().values();
		for(OrderBook orderBook : orderBooks) {
			String fiOrgName = orderBook.getOrganization();
			if(!fiOrgs.contains( fiOrgName )) {
				orderBook.setVenueRoutingEnabled(newValue);
			}
		}
	}

	private void updateOrgLevelVenueRoutingFlag(String key, String value) {
		String fiOrgName = key.substring(IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX.length());
		List<OrderBook> orderBooks = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(fiOrgName);
		for (OrderBook i : orderBooks) {
			i.setVenueRoutingEnabled( Boolean.valueOf(value) );
		}
	}

	private void updateVenueRoutingFlagForPartials(String key, String value) {
		boolean newValue = Boolean.valueOf(value);
		Set<String> fiOrgs = routePartiallyFilledUnmatchedAmountToClobEnabledMap.keySet();
		Collection<OrderBook> orderBooks = OrderBookCacheC.getInstance().getOrderBooks().values();
		for(OrderBook orderBook : orderBooks) {
			String fiOrgName = orderBook.getOrganization();
			if(!fiOrgs.contains( fiOrgName )) {
				orderBook.setVenueRoutingEnabledForPartials(newValue);
			}
		}
	}

	private void updateOrgLevelVenueRoutingFlagForPartials(String key, String value) {
		String fiOrgName = key.substring(IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX.length());
		List<OrderBook> orderBooks = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(fiOrgName);
		for (OrderBook i : orderBooks) {
			i.setVenueRoutingEnabledForPartials( Boolean.valueOf(value) );
		}
	}

	private void updateOrderMarketabilityCheckFlag(String key, String value) {
		boolean newValue = Boolean.valueOf(value);
		Set<String> fiOrgs = checkMarketabilityOfVenueRoutedOrdersEnabledMap.keySet();
		Collection<OrderBook> orderBooks = OrderBookCacheC.getInstance().getOrderBooks().values();
		for(OrderBook orderBook : orderBooks) {
			String fiOrgName = orderBook.getOrganization();
			if(!fiOrgs.contains( fiOrgName )) {
				orderBook.setOrderMarketabilityCheckFlagEnabled(newValue);
			}
		}
	}

	private void updateOrgLevelOrderMarketabilityCheckFlag(String key, String value) {
		String fiOrgName = key.substring(IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED_PREFIX.length());
		List<OrderBook> orderBooks = OrderBookCacheC.getInstance().findOrderBooksByOrganizationShortname(fiOrgName);
		for (OrderBook i : orderBooks) {
			i.setOrderMarketabilityCheckFlagEnabled( Boolean.valueOf(value) );
		}
	}


	private void handleOrderProviderListUpdate( String key, String newValue, String oldValue )
	{
		List<String> oldLpList = convertToList(oldValue);
		List<String> newLpList = convertToList(newValue);
		List<String> addedLPs = new ArrayList<String>();
		List<String> removedLPs = new ArrayList<String>();
		processLPListDifferences(addedLPs, removedLPs, oldLpList, newLpList);
		for ( String lp : addedLPs )
		{
			Organization org = ISUtilImpl.getInstance().getOrg(lp);
			if ( org != null )
			{
				UpdateAdaptorCommonCache(org);
				for ( Organization cust : org.getActiveCustomerOrganizations () )
				{
					UpdateAdaptorCommonCache(cust);
				}
			}
			else
			{
				log.warn("OrderConfiguration.handleOrderProviderListUpdate.WARN : Org not found with shortname=" + lp);
			}
		}
	}

	protected void processLPListDifferences( List<String> addedLps, List<String> removedLps, List<String> oldList, List<String> newList )
	{
		Iterator<String> oldListItr = oldList.iterator();
		while ( oldListItr.hasNext() )
		{
			String lpName = oldListItr.next();
			if ( !newList.contains(lpName) )
			{
				removedLps.add(lpName);
			}
		}
		Iterator<String> newListItr = newList.iterator();
		while ( newListItr.hasNext() )
		{
			String lpName = newListItr.next();
			if ( !oldList.contains(lpName) )
			{
				addedLps.add(lpName);
			}
		}
	}

	protected List<String> convertToList( String value )
	{
		if ( value == null )
		{
			return new ArrayList<String>();
		}
		StringTokenizer st = new StringTokenizer(value, ",");
		List<String> list = new ArrayList<String>(st.countTokens());
		while ( st.hasMoreTokens() )
		{
			list.add(st.nextToken());
		}
		return list;
	}

	public List<Organization> getOrderAdaptorHiddenProviderList( String fiOrg )
	{
		if ( fiOrg == null )
		{
			return new ArrayList<Organization>();
		}
		else
		{
			List<Organization> list = orderAdaptorHiddenProvidersMap.get(fiOrg);

			if ( list == null )
			{
				updateHiddenProvidersList(fiOrg);
				return orderAdaptorHiddenProvidersMap.get(fiOrg);
			}
			else
			{
				return list;
			}
		}
	}

	public boolean isHiddenProvider( String providerName, String fiOrg )
	{
		if ( hiddenProvidersEnabled )
		{
			List<Organization> list = getOrderAdaptorHiddenProviderList(fiOrg);
			for ( Organization org : list )
			{
				if ( org.getShortName().equals(providerName) )
				{
					return true;
				}
			}

			return false;
		}
		else
		{
			return false;
		}
	}

	private void updateHiddenProvidersList( String fiOrg )
	{
		String keyModified = new StringBuilder(150).append(IDC_IS_ORDER_HIDDEN_PROVIDERS_LIST).append('.').append(fiOrg).toString();
		String orderAdaptorHiddenProviderListStr = getStringProperty(keyModified, "");
		String[] providers;
		List<Organization> orderAdaptorHiddenProviderList = new ArrayList<Organization>();
		if ( orderAdaptorHiddenProviderListStr != null && orderAdaptorHiddenProviderListStr.trim().length() > 0 )
		{
			providers = orderAdaptorHiddenProviderListStr.split(",");
			for ( String provider : providers )
			{
				Organization org = ISUtilImpl.getInstance().getOrg(provider);
				if ( org == null )
				{
					log.error("OrderConfiguration.loadProperties : Unable to add Provider : " + provider + " in Hidden Provider list. Organization is coming null.");
				}
				else
				{
					orderAdaptorHiddenProviderList.add(org);
				}
			}
		}
		orderAdaptorHiddenProvidersMap.put(fiOrg, orderAdaptorHiddenProviderList);
	}

	public List<Organization> getOrderAdaptorUnSpreadedProviderList( String fiOrg )
	{
		if ( fiOrg == null )
		{
			return new ArrayList<Organization>();
		}
		else
		{
			List<Organization> list = orderAdaptorUnSpreadedProvidersMap.get(fiOrg);
			if ( list == null )
			{
				updateUnSpreadedProvidersList(fiOrg);
				return orderAdaptorUnSpreadedProvidersMap.get(fiOrg);
			}
			else
			{
				return list;
			}
		}
	}

	public boolean isUnSpreadedProvider( String providerName, String fiOrg )
	{
		if ( unSpreadedProvidersEnabled )
		{
			List<Organization> list = getOrderAdaptorUnSpreadedProviderList(fiOrg);
			for ( Organization org : list )
			{
				if ( org.getShortName().equals(providerName) )
				{
					return true;
				}
			}

			return false;
		}
		else
		{
			return false;
		}
	}

	private void updateUnSpreadedProvidersList( String fiOrg )
	{
		String keyModified = new StringBuilder(150).append(IDC_IS_ORDER_UNSPREADED_PROVIDERS_LIST).append('.').append(fiOrg).toString();
		String orderAdaptorUnSpreadedProviderListStr = getStringProperty(keyModified, "");
		String[] providers;
		List<Organization> orderAdaptorUnSpreadedProviderList = new ArrayList<Organization>();
		if ( orderAdaptorUnSpreadedProviderListStr != null && orderAdaptorUnSpreadedProviderListStr.trim().length() > 0 )
		{
			providers = orderAdaptorUnSpreadedProviderListStr.split(",");
			for ( String provider : providers )
			{
				Organization org = ISUtilImpl.getInstance().getOrg(provider);
				if ( org == null )
				{
					log.error("OrderConfiguration.loadProperties : Unable to add Provider : " + provider + " in Hidden Provider list. Organization is coming null.");
				}
				else
				{
					orderAdaptorUnSpreadedProviderList.add(org);
				}
			}
		}
		orderAdaptorUnSpreadedProvidersMap.put(fiOrg, orderAdaptorUnSpreadedProviderList);
	}

	public List<String> getOrderAdaptorExcludedProviderList( String fiOrg )
	{
		if ( fiOrg == null )
		{
			return null;
		}
		else
		{
			return excludedProvidersEnabled ? orderAdaptorExcludedProvidersMap.get(fiOrg) : null;
		}
	}

	private Map<String, List<String>> initPrefixedPropertyStringMap( String propertyPrefix )
	{
		Map<String, List<String>> map = new HashMap<String, List<String>>();
		Properties props = getPropertiesWithPrefix(propertyPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;
				//this is to exclude enable excluded providers property
				if ( IDC_IS_ORDER_EXCLUDED_PROVIDERS_ENABLED.equals(keyStr) )
				{
					continue;
				}
				String prop = props.getProperty(keyStr);
				List<String> orderAdaptorExcludedProviderList = new ArrayList<String>();
				try
				{
					String[] providers = prop.split(",");
					for ( String provider : providers )
					{
						orderAdaptorExcludedProviderList.add(provider);
					}

					map.put(keyStr.substring(propertyPrefix.length()), orderAdaptorExcludedProviderList);
				}
				catch ( Exception e )
				{
					log.error("OrderConfiguration.initPrefixedPropertyStringMap.ERROR : Exception in parsing properties with prefix=" + propertyPrefix, e);
				}
			}
		}

		return map;
	}

	public boolean isUnSpreadedProvidersEnabled()
	{
		return unSpreadedProvidersEnabled;
	}

	public boolean isHiddenProvidersEnabled()
	{
		return hiddenProvidersEnabled;
	}

	public boolean isSpreadedProviderProperTierAssociationEnabled()
	{
		return spreadedProviderProperTierAssociationEnabled;
	}

	public boolean isExcludedProvidersEnabled()
	{
		return excludedProvidersEnabled;
	}

	public Collection<String> getOrderProvidersList()
	{
		return orderProvidersList;
	}

	public Collection<Organization> getOrderProviderOrgsList()
	{
		return orderProviderOrgList;
	}

	public Collection<Organization> getExtendedOrderProviderOrgsList()
	{
		if ( OrderServiceMBeanC.getInstance().isOrderProvidersListEnabled() )
		{
			return extendedOrderProviderOrgsList;
		}
		else if( RuntimeFactory.getServerRuntimeMBean().isOCXDeploymentEnabled() 
	             && 
	         	 VirtualServerType.MarketDataFeedServer.equals( ConfigurationFactory.getServerMBean().getVirtualServerType() ) )
	            {		
			return mdfEnabledOrgs;
		}
		else if( VirtualServerType.RFSServer.equals( ConfigurationFactory.getServerMBean().getVirtualServerType() ) ) {
			return ReferenceDataCacheC.getInstance().getOrgMap().values();
		}
		else
		{
			return getVirtualServerOrgs();
		}
	}

	private Collection<Organization> getVirtualServerOrgs()
	{
		return RuntimeFactory.getServerRuntimeMBean().getVirtualServer().getActiveOrganizations ();
	}

    public Collection<Organization> getExtendedOrderProviderOrgsListForHeartbeat()
	{
		if ( OrderServiceMBeanC.getInstance().isOrderProvidersListEnabled() && OrderServiceMBeanC.getInstance().isOrderProvidersListEnabledForHeartbeat() )
		{
			return extendedOrderProviderOrgsList;
		}
		else
		{
			return getVirtualServerOrgs ();
		}
	}

	public boolean isOrderProvider( Organization org )
	{
		Collection<Organization> ops = getExtendedOrderProviderOrgsList();
		for ( Organization op : ops )
		{
			if ( op.isSameAs(org) )
			{
				return true;
			}
		}
		return false;
	}

	public void resetOrderProviderOrgsList( boolean updateAdaptorCommonCache )
	{
		this.orderProvidersList = initOrderProvidersList();
		this.orderProviderOrgList = initOrderProviderOrgsList(this.orderProvidersList);
		this.extendedOrderProviderOrgsList = initExtendedOrderProviderOrgsList(this.orderProviderOrgList);
		if ( updateAdaptorCommonCache )
		{
			for ( Organization org : this.extendedOrderProviderOrgsList )
			{
				UpdateAdaptorCommonCache(org);
			}
		}
	}

	/**
	 * Returns the collection of order provider short names.
	 *
	 * @return order provider list
	 */
	private Collection<String> initOrderProvidersList()
	{
		Collection<String> list = new HashSet<String>();
		String listStr = orderAdaptorProviderList;
		if ( listStr != null )
		{
			list = IdcUtilC.arrayAsArrayList(IdcUtilC.getSubstring(listStr, ","));
		}
		return list;
	}

	/**
	 * Returns the collection of order provider organizations.
	 *
	 * @param providersList collection of providers
	 * @return order provider organization list
	 */
	private Collection<Organization> initOrderProviderOrgsList( Collection<String> providersList )
	{
		Collection<Organization> list = new HashSet<Organization>();
		if ( providersList != null )
		{
			for ( String provider : providersList )
			{
				Organization org = ISUtilImpl.getInstance().getOrg(provider);
				if ( org != null )
				{
					list.add(org);
				}
				else
				{
					log.warn("OrderConfiguration.initOrderProviderOrgsList.WARN : No org found with shortname=" + provider);
				}
			}
		}
		return list;
	}

	/**
	 * Returns the list of extended order providers which includes the configured order providers as well as their customer orgs.
	 *
	 * @param providerOrgs order providers
	 * @return order providers
	 */
	private Collection<Organization> initExtendedOrderProviderOrgsList( Collection<Organization> providerOrgs )
	{
		Collection<Organization> list = new HashSet<Organization>();
		if ( providerOrgs != null )
		{
			for ( Organization provider : providerOrgs )
			{
				list.add(provider);
				list.addAll(provider.getActiveCustomerOrganizations ());
			}
		}

        // update the order service mbean list as well
		Collection<Organization> orderProvidersList = list;
		if( RuntimeFactory.getServerRuntimeMBean().isOCXDeploymentEnabled() &&
				VirtualServerType.MarketDataFeedServer.equals( ConfigurationFactory.getServerMBean().getVirtualServerType() ) )
		{
			orderProvidersList = mdfEnabledOrgs;
		}
		else if ( !OrderServiceMBeanC.getInstance().isOrderProvidersListEnabled() )
		{
			orderProvidersList = getVirtualServerOrgs();
		}

		OrderServiceMBeanC.getInstance().setProvisionedOrderProviders( orderProvidersList );
		return list;
	}

	/**
	 * Updates RequestHandlerFactoryRegistry and HeartBeatDestination cache for given organization.
	 *
	 * @param org org
	 */
	public void UpdateAdaptorCommonCache( Organization org )
	{
		AdaptorConfigurationFactory.getResponseHandlerInstance().reloadHeartBeatDestinations(org.getShortName());
		RequestHandlerFactoryRegistry.register(ISCommonConstants.PROVIDER_TYPE_OA, org.getShortName(), OAHandlerFactory.getInstance());
		makerTakerNSs.add(org.getNamespace());
		WarmUpReferenceDataEntityUpdateHandlerC.setAdditionalNamespaces(makerTakerNSs);
	}

	public boolean isCreditCheckEnabled()
	{
		return this.isCreditCheckEnabled;
	}

	public boolean isUseQuoteExecutors()
	{
		return useQuoteExecutors;
	}

    public boolean isUseExecutorsForRouting() {
        return useExecutorsForRouting;
    }

	public boolean isUseExecutorsForCheckingMarketability() {
		return useExecutorsForCheckingMarketability;
	}

    public Double getMaxShowAmount( String ccyPair )
	{
		String ccypKey = new StringBuilder(50).append(IDC_IS_ORDER_MAX_DISPLAY_AMOUNT_PREFIX).append('.').append(ccyPair).toString();
		Double val = maxShowAmountsMap.get(ccypKey);
		return val != null ? val : maxShowAmountsMap.get(IDC_IS_ORDER_MAX_DISPLAY_AMOUNT_PREFIX);
	}

	public Integer getCircuitBreakerThreshold( String orderPlacingOrg )
	{
		String orgKey = new StringBuilder(50).append(IDC_ORDERADAPTOR_REJECTION_DISABLEPROVIDER).append('.').append(orderPlacingOrg).toString();
		Integer val = circuitBreakerThresholds.get(orgKey);
		return val != null ? val : circuitBreakerThresholds.get(IDC_ORDERADAPTOR_REJECTION_DISABLEPROVIDER);
	}

	public int getOrderMatchDelay()
	{
		return orderMatchDelay;
	}

	public int getOrderMatchDelay( Organization org )
	{
		Integer flashMode = orderMatchDelayMap.get(org.getShortName());
		return flashMode != null ? flashMode : orderMatchDelay;
	}

    public int getOrderMatchDelay( String  orgName )
    {
        Integer flashMode = orderMatchDelayMap.get(orgName);
        return flashMode != null ? flashMode : orderMatchDelay;
    }


    public Collection<String> getFlashOrderTypes()
	{
		return flashOrderTypes;
	}

	public Collection<String> getFlashOrderTypes( Organization org )
	{
		Collection<String> types = flashOrderTypesMap.get(org.getShortName());
		return types != null ? types : flashOrderTypes;
	}

	public Collection<String> getFlashOrderTIFs()
	{
		return flashOrderTIFs;
	}

	public Collection<String> getFlashOrderTIFs( Organization org )
	{
		Collection<String> types = flashOrderTIFsMap.get(org.getShortName());
		return types != null ? types : flashOrderTIFs;
	}

	public Double getFlashOrderMinSize( Organization org, CurrencyPair ccyPair )
	{
		Double value = flashOrderMinSizeMap.get(ccyPair.getName());
		return value != null ? value : flashOrderMinSize;
	}

	public Double getFlashOrderMaxSize( Organization org, CurrencyPair ccyPair )
	{
		Double ccypVal = flashOrderMaxSizeMap.get(ccyPair.getName());
		if ( ccypVal != null )
		{
			return ccypVal;
		}
		return flashOrderMaxSize;
	}

	public int getFlashLiftTimeout()
	{
		return flashLiftTimeout;
	}

	public int getFlashLiftTimeout( Organization org )
	{
		Integer liftTimeoutDelay = flashLiftTimeoutMap.get(org.getShortName());
		return liftTimeoutDelay != null ? liftTimeoutDelay : flashLiftTimeout;
	}

	public Double getCustomerRegularSize()
	{
		return customerRegularSize;
	}

	public Double getCustomerRegularSize( Organization org )
	{
		return getCustomerRegularSize(org.getShortName());
	}

	public Double getCustomerRegularSize( String orgShortName )
	{
		Double val = customerRegularSizeMap.get(orgShortName);
		if ( val != null )
		{
			return val;
		}
		return customerRegularSize;
	}

	public int getOrderSliceTTL()
	{
		return orderSliceTTL;
	}

	/**
	 * Initializes the property map with org, currency pair keys
	 *
	 * @param propertyPrefix property prefix for a holding value of double type.
	 * @return map
	 */
	private Map<String, Double> initPrefixedPropertyDoubleMap( String propertyPrefix )
	{
		Map<String, Double> map = new HashMap<String, Double>();
		Properties props = getPropertiesWithPrefix(propertyPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;
				String prop = props.getProperty(keyStr);
				try
				{
					map.put(keyStr, Double.parseDouble(prop));
				}
				catch ( Exception e )
				{
					log.error("OrderConfiguration.initPrefixedPropertyDoubleMap.ERROR : Exception in parsing properties with prefix=" + propertyPrefix, e);
				}
			}
		}
		return map;
	}

	/**
	 * Initializes the property map with org keys
	 *
	 * @param propertyPrefix property prefix for a holding value of integer type.
	 * @return map
	 */
	private Map<String, Integer> initCircuitBreakerThreshold( String propertyPrefix )
	{
		Map<String, Integer> map = new HashMap<String, Integer>();
		Properties props = getPropertiesWithPrefix(propertyPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;

				if ( keyStr.contains(OrderConfigurationMBean.IDC_ORDERADAPTOR_REJECTION_DISABLEPROVIDER_EXCLUSION_PETTERN) )
				{
					continue;
				}

				String prop = props.getProperty(keyStr);
				try
				{
					map.put(keyStr, Integer.parseInt(prop));
				}
				catch ( Exception e )
				{
					log.error("OrderConfiguration.initPrefixedPropertyIntegerMap.ERROR : Exception in parsing properties with prefix=" + propertyPrefix, e);
				}
			}
		}
		return map;
	}

	/**
	 * Initializes the property map with org, currency pair keys
	 *
	 * @param propertyPrefix property prefix for a holding value of double type.
	 * @return map
	 */
	private Map<String, Double> initOPAdjustmentPipsMap( String propertyPrefix )
	{
		Map<String, Double> map = new HashMap<String, Double>();
		Properties props = getPropertiesWithPrefix(propertyPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;
				String varStr = keyStr.substring(propertyPrefix.length());
				if ( varStr.trim().length() > 0 )
				{
					String prop = props.getProperty(keyStr);
					try
					{
						map.put(varStr, Double.parseDouble(prop));
					}
					catch ( Exception e )
					{
						log.error("OrderConfiguration.initOPAdjustmentPipsMap.ERROR : Exception in parsing properties with prefix=" + propertyPrefix, e);
					}
				}
				else
				{
					log.error("OrderConfiguration.initOPAdjustmentPipsMap.ERROR : Key : " + keyStr + " is not proper.");
				}
			}
		}
		return map;
	}

	public boolean isOPAdjustmentEnabled()
	{
		return opAdjustmentEnabled;
	}

	public Double getOPAdjustmentPips( String orgName, String ccyPair )
	{
		if ( opAdjustmentEnabled )
		{
			if ( orgName == null )
			{
				return null;
			}
			else if ( ccyPair == null )
			{
				return opAdjustmentPipsMap.get(orgName);
			}
			Double tempPips = opAdjustmentPipsMap.get(new StringBuilder(50).append(orgName).append('.').append(ccyPair).toString());
			if ( tempPips == null )
			{
				tempPips = opAdjustmentPipsMap.get(orgName);
			}
			return tempPips;
		}
		else
		{
			return null;
		}
	}

	public List<String> getOPAdjustmentAllowedChannels()
	{
		return opAdjustmenntAllowedChannels;
	}

	public List<String> getOPAdjustmentAllowedOrderTypes()
	{
		return opAdjustmenntAllowedOrderTypes;
	}

	public boolean isCircuitBreakersEnabled()
	{
		return isCircuitBreakerEnabled;
	}

	public int getOrderMinSliceInterval()
	{
		return orderMinSliceInterval;
	}

	public boolean isFlashUpdatePriceOnMatch()
	{
		return flashUpdatePriceOnMatch;
	}

	public int getIOCExpiryTime()
	{
		return iocExpiryTime;
	}

	public int getIOCExpiryTime( String custOrgName )
	{
		Integer iocExpiryTimeForCustomer = iocExpiryTimeMap.get(custOrgName);
		return (iocExpiryTimeForCustomer == null) ? 0 : iocExpiryTimeForCustomer;
	}

	public int getIOCExpiryTimeForBrokerCustomers( String brokerOrgName )
	{
		Integer iocExpiryTimeForBrokerCustomer = iocExpiryTimeForBrokerOrgMap.get(brokerOrgName);
		return (iocExpiryTimeForBrokerCustomer == null) ? iocExpiryTime : iocExpiryTimeForBrokerCustomer;
	}

	public boolean isExpiryTimeEnabledForChannel( String channelName )
	{
		return (iocExpiryTimeEnabledForChannel == null) || channelName.matches(iocExpiryTimeEnabledForChannel);
	}

	public boolean isExpiryTimeEnabledForChannel( String channelName, String custOrgName )
	{
		String iocExpiryTimeEnabledForChannel = iocExpiryTimeEnabledForChannelMap.get(channelName);
		return (iocExpiryTimeEnabledForChannel == null) ? isExpiryTimeEnabledForChannel(channelName) : channelName.matches(iocExpiryTimeEnabledForChannel);
	}

	public boolean isExpiryOnMatch()
	{
		return iocExpiryOnMatch;
	}

	public Boolean isExpiryOnMatch( String custOrgName )
	{
		return iocExpiryTimeOnMatchMap.get(custOrgName);
	}

	public Boolean isExpiryOnMatchForBroker( String brokerOrgName )
	{
		return iocExpiryTimeOnMatchForBrokerOrgMap.get(brokerOrgName);
	}

	protected Map<String, Integer> initIOCExpiryTimeMap( String propPrefix, String exclusionValue )
	{
		Map<String, Integer> map = new HashMap<String, Integer>();
		Properties props = getPropertiesWithPrefix(propPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;
				if ( exclusionValue != null && keyStr.matches(exclusionValue) )
				{
					continue;
				}
				String prop = props.getProperty(keyStr);
				try
				{
					String suffix = keyStr.substring(keyStr.lastIndexOf('.') + 1);
					if ( suffix != null )
					{
						try
						{
							if ( prop != null && prop.trim().length() > 0 )
							{
								int val = Integer.parseInt(prop.trim());
								val = (val == 0) ? -1 : val;
								map.put(suffix, val);
							}
							else
							{
								map.put(suffix, Integer.valueOf(-1));
							}
						}
						catch ( Exception e )
						{
							log.warn("OC.initIOCExpiryTimeMap : Exception parsing the property key=" + keyStr + ",val=" + prop, e);
						}
					}
					else
					{
						log.warn("OC.initIOCExpiryTimeMap : Invalid property name=" + keyStr);
					}
				}
				catch ( Exception e )
				{
					log.error("OC.initIOCExpiryTimeMap : Exception initialising the properties", e);
				}
			}
		}
		return map;
	}

	protected Map<String, Boolean> initIOCExpiryOnMatchMap( String propPrefix, String exclusionValue )
	{
		Map<String, Boolean> map = new HashMap<String, Boolean>();
		Properties props = getPropertiesWithPrefix(propPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;
				if ( exclusionValue != null && keyStr.matches(exclusionValue) )
				{
					continue;
				}
				String prop = props.getProperty(keyStr);
				try
				{
					String suffix = keyStr.substring(keyStr.lastIndexOf('.') + 1);
					if ( suffix != null )
					{
						try
						{
							map.put(suffix, Boolean.parseBoolean(prop));
						}
						catch ( Exception e )
						{
							log.warn("OC.initIOCExpiryOnMatchMap : Exception parsing the property key=" + keyStr + ",val=" + prop, e);
						}
					}
					else
					{
						log.warn("OC.initIOCExpiryOnMatchMap : Invalid property name=" + keyStr);
					}
				}
				catch ( Exception e )
				{
					log.error("OC.initIOCExpiryOnMatchMap : Exception initialising the properties", e);
				}
			}
		}
		return map;
	}

	protected Map<String, Boolean> initOrderRateToFillRateMap( String propPrefix, String exclusionValue )
	{
		Map<String, Boolean> map = new HashMap<String, Boolean>();
		Properties props = getPropertiesWithPrefix(propPrefix);
		if ( props != null )
		{
			for ( Object key : props.keySet() )
			{
				String keyStr = (String) key;
				if ( exclusionValue != null && keyStr.matches(exclusionValue) )
				{
					continue;
				}
				String prop = props.getProperty(keyStr);
				try
				{
					String suffix = keyStr.substring(keyStr.lastIndexOf('.') + 1);
					if ( suffix != null )
					{
						try
						{
							map.put(suffix, Boolean.parseBoolean(prop));
						}
						catch ( Exception e )
						{
							log.warn("OC.initIOCExpiryOnMatchMap : Exception parsing the property key=" + keyStr + ",val=" + prop, e);
						}
					}
					else
					{
						log.warn("OC.initIOCExpiryOnMatchMap : Invalid property name=" + keyStr);
					}
				}
				catch ( Exception e )
				{
					log.error("OC.initIOCExpiryOnMatchMap : Exception initialising the properties", e);
				}
			}
		}
		return map;
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#isSetOrderRateToFillRate(java.lang.String)
	 */
	public Boolean isSetOrderRateToFillRate( String orgName )
	{
		return orderRateToFillRateMap.get(orgName);
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#isSetOrderRateToFillRateForBroker(java.lang.String)
	 */
	public Boolean isSetOrderRateToFillRateForBroker( String brokerOrgName )
	{
		return orderRateToFillRateBrokerOrgMap.get(brokerOrgName);
	}

	public boolean isSetOrderRateToFillRate()
	{
		return isSetOrderRateToFillRate;
	}

	public double getDefaultMKTRange( Organization org, String ccyPair )
	{
		if ( org == null || ccyPair == null )
		{
			return 0;
		}
		else
		{
			Double tempMktRange = defaultMKTRangeMap.get(ccyPair + '.' + org.getShortName());
			if ( tempMktRange == null )
			{
				tempMktRange = defaultMKTRangeMap.get(ccyPair);
				if ( tempMktRange == null )
				{
					return defaultMKTRange;
				}
				else
				{
					return tempMktRange;
				}
			}
			else
			{
				return tempMktRange;
			}
		}
	}

    public double getPercentageDefaultMKTRange( Organization org, String ccyPair )
    {
        Double tempMktRange = defaultPercentageMKTRangeMap.get(ccyPair + '.' + org.getShortName());
        if ( tempMktRange == null )
        {
            tempMktRange = defaultPercentageMKTRangeMap.get(ccyPair);
            if ( tempMktRange == null )
            {
                tempMktRange = defaultPercentageMKTRange;
            }
        }
        return tempMktRange;
    }

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#isOptimizedFOKExecutionEnabled(com.integral.user.Organization)
	 */
	@Override
	public boolean isOptimizedFOKExecutionEnabled( Organization org )
	{
		Boolean val = optFOKExecMap.get(org.getShortName());
		return val != null ? val : isOptFOKExecEnabled;
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#isSendVWAPPriceOptimizedFOKExecution()
	 */
	@Override
	public boolean isSendVWAPPriceOptimizedFOKExecution()
	{
		return isSendVWAPPriceOptimizedFOKExecution;
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#getProviderSuspensionPeriod()
	 */
	@Override
	public int getProviderSuspensionPeriod( Organization org )
	{
		Integer val = providerSuspensionPeriodMap.get(org.getShortName());
		return val != null ? val : defaultProviderSuspensionPeriod;
	}

	public boolean getFillAtMarketExecInst( Organization org )
	{
		Boolean val = fillAtMarketExecInstTOBMap.get(org.getShortName());
		return val != null ? val : isFillAtMarketExecInstTOB;
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#isEffectiveTimeEnabled(com.integral.user.Organization)
	 */
	@Override
	public boolean isEffectiveTimeEnabled( Organization org )
	{
		Boolean val = orderEffectiveTimeEnabledMap.get(org.getShortName());
		return val != null ? val : isOrderEffectiveTimeEnabled;
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#getEffectiveTimeTolerance(com.integral.user.Organization)
	 */
	@Override
	public long getEffectiveTimeTolerance( Organization org )
	{
		Integer val = orderEffectiveTimeToleranceMap.get(org.getShortName());
		return val != null ? val : orderEffectiveTimeToleranceDefault;
	}

	/* (non-Javadoc)
	 * @see com.integral.adaptor.order.configuration.OrderConfigurationMBean#getMaxBroadcastTiers(java.lang.String)
	 */
	public int getMaxBroadcastTiers( String org )
	{
		Integer val = maxBroadcastTiersMap.get(org);
		return val != null ? val : maxBroadcastTiersDefault;
	}

	public boolean getRejectAdditionalPrecisionOrders()
	{
		return rejectAdditionalPrecisionOrders;
	}
	
	public boolean isDirectOrderToBroker( Organization org )
	{
		Boolean flag = null;
		if ( org != null )
		{
			flag = directOrderToBAEnabledMap.get ( org.getShortName () );
			if ( flag == null )
			{
				flag = org.getBrokerOrganization () != null ? directOrderToBAEnabledMap.get ( org.getBrokerOrganization ().getShortName () ) : null;
			}
		}
		return flag != null ? flag : isDirectOrderToBAEnabled;
	}

	public boolean isDirectOrderToLP( Organization org, Organization lpOrg )
	{
		Map<String,Boolean> bMap = directOrderToLpEnabledMap;
		Boolean flag = null;
		if ( org != null && lpOrg != null )
		{
			flag = bMap.get ( org.getShortName () +"."+ lpOrg.getShortName () );
			if( flag != null )
			{
				return flag;
			}
			flag = bMap.get ( org.getShortName ());
		}
		return flag != null ? flag : isDirectOrderToLpEnabled;
	}

    public int getProviderQuoteCacheSize() {
        return defaultProviderQuotesCacheSize;
    }

    public int getProviderQuoteCacheSize( String providerOrg ) {
        Integer val = providerQuotesCacheSizeMap.get(providerOrg);
        return val != null ? (val <= 0 ? 1 : val) : defaultProviderQuotesCacheSize;
    }

    public int getProviderQuoteHistoryMatchDepth() {
        return defaultProviderQuoteHistoryMatchDepth;
    }

    public int getProviderQuoteHistoryMatchDepth( String fiOrg ) {
        Integer val = providerQuoteHistoryMatchDepthMap.get(fiOrg);
        return val != null ? (val < 0 ? 0 : val) : defaultProviderQuoteHistoryMatchDepth;
    }

    public boolean isTermCurrencyBroadcastEnabled( String org )
    {
        Boolean val = termCurrencyBroadcastEnabledMap.get(org);
        return val != null ? val : termCurrencyBroadcastEnabled;
    }

    public int getAlgoFAMExpiryInterval() {
        return algoFillAtMktExpiryInterval;
    }

    public int getOrderFlashExpiryPeriod() {
        return flashExpiryPeriod;
    }

    public int getOrderFlashExpiryPeriod(String fiOrg) {
        Integer val = flashExpiryPeriodMap.get(fiOrg);
        return val != null ? ( val < 0 ? 0 : val ) : flashExpiryPeriod;
    }

    public List<String> getDisplayOrderProvidersBlackList() {
        return displayOrderProvidersBlackList;
    }

    public boolean isPersistentOrderMigrationToSpacesEnabled() {
        return peristentOrderMigrationToSpacesEnabled;
    }

    public double getTwapRegularSize(String fiOrg) {
        Double twapSize = twapRegularSizeMap.get(fiOrg);
        return (twapSize != null) ? twapSize : defaultTwapRegularSize;
    }

    public long getDirectedOrderAlertTimeForSubmit(){
        return directedOrderSubmitAlertTime;
    }

    public long getFixingTwapStartInterval() {
        return fixingTwapStartInterval;
    }

	public void resetMDFServerEnabledList() {
		mdfEnabledOrgs = getMDFEnabledOrganizations();
	}

    public String getAggregatedFillPriceImprovementAlgorithm(String fiOrg) {
        String value = aggregatedFillPriceImprovementAlgorithmMap.get(fiOrg);
        return value != null ? value : defaultAggregatedFillPriceImprovementAlgorithm;
    }

    public double getAggregatedFillPriceImprovementPercentage(String fiOrg) {
        Double value = aggregatedFillPriceImprovementPercentageToCustomerMap.get(fiOrg);
        return value != null ? value : defaultAggregatedFillPriceImprovementPercentageToCustomer;
    }

    public String getAggregatedFillPriceImprovementFeeType(String fiOrg) {
        String value = aggregatedFillPriceImprovementFeeTypeToCustomerMap.get(fiOrg);
        return value != null ? value : defaultAggregatedFillPriceImprovementFeeTypeToCustomer;
    }
    
    public double getAggregatedFillPriceImprovementFixedFee(String fiOrg) {
        Double value = aggregatedFillPriceImprovementFixedFeeToCustomerMap.get(fiOrg);
        return value != null ? value : defaultAggregatedFillPriceImprovementFixedFeeToCustomer;
    }
    
    public boolean routeUnmatchedAmountToClobEnabled(String fiOrg) {
        Boolean value = routeUnmatchedAmountToClobEnabledMap.get(fiOrg);
        return value != null ? value : routeUnmatchedAmountToClobEnabled;
    }

	public boolean routeTermCCYAmountToClobEnabled(String fiOrg) {
		Boolean value = routeTermCCYAmountToClobEnabledMap.get(fiOrg);
		return value != null ? value : routeTermCCYAmountToClobEnabled;
	}


    @Override
    public int getMaxNoOfTimesToRouteToClob(String fiOrg) {
      Integer value =  routeUnmatchedAmountToClobMaxNoOfTimesMap.get(fiOrg);
      return value != null ? value : routeUnmatchedAmountToClobMaxNoOfTimes;
    }
    
	public boolean routePartialsUnmatchedAmountToClobEnabled(String fiOrg) {
		Boolean value = routePartiallyFilledUnmatchedAmountToClobEnabledMap.get(fiOrg);
		return value != null ? value : routePartiallyFilledUnmatchedAmountToClobEnabled;
	}

    @Override
    public int getMinRestingTimeInRiskNet(String fiOrg) {
        Integer value =  minRestingTimeInRiskNetMap.get(fiOrg);
        return value != null ? value : minRestingTimeInRiskNet;
    }

    public boolean isMarketSnapshotTruncateEnabled(){
        return isMarketSnapshotTruncateEnabled;
    }

	public boolean doMarketabilityCheckOnRoutedOrders(String fiOrg) {
		Boolean value = checkMarketabilityOfVenueRoutedOrdersEnabledMap.get(fiOrg);
		return value != null ? value : checkMarketabilityOfVenueRoutedOrdersEnabled;
	}

	public boolean getSmartSlicingEnabled(Organization org) {
		Boolean value = smartSlicingEnabledMap.get(org.getShortName());
		return value != null ? value.booleanValue() : smartSlicingEnabled ;
	}

	public int getSmartSlicingMaxSliceInterval(Organization org) {
		Integer value = smartSlicingMaxSliceIntervalMap.get(org.getShortName());
		return value != null ? value.intValue() : smartSlicingMaxSliceInterval;
	}

	public int getSmartSlicingMinSliceInterval(Organization org) {
		Integer value = smartSlicingMinSliceIntervalMap.get(org.getShortName());
		return value != null ? value.intValue() : smartSlicingMinSliceInterval;
	}

	public int getSmartSlicingOptimalSliceInterval(Organization org) {
		Integer value = smartSlicingOptimalSliceIntervalMap.get(org.getShortName());
		return value != null ? value.intValue() : smartSlicingOptimalSliceInterval;
	}

	public int getSmartSlicingOptimalSliceSize(Organization org) {
		Integer value = smartSlicingOptimalSliceSizeMap.get(org.getShortName());
		return value != null ? value.intValue() : smartSlicingOptimalSliceSize;
	}

	public double getSmartSlicingSliceIntervalRandomization(Organization org) {
		Double value = smartSlicingSliceIntervalRandomizationMap.get(org.getShortName());
		return value != null ? value.doubleValue() : smartSlicingSliceIntervalRandomization;
	}

	public double getSmartSlicingSliceSizeCeilingFactor(Organization org) {
		Double value = smartSlicingSliceSizeCeilingFactorMap.get(org.getShortName());
		return value != null ? value.doubleValue() : smartSlicingSliceSizeCeilingFactor;
	}

	public double getSmartSlicingSliceSizeRandomization(Organization org) {
		Double value = smartSlicingSliceSizeRandomizationMap.get(org.getShortName());
		return value != null ? value.doubleValue() : smartSlicingSliceSizeRandomization;
	}

	public double getSmartSlicingTIFForTWAP(Organization org) {
		Double value = smartSlicingTIFForTWAPMap.get(org.getShortName());
		return value != null ? value.doubleValue() : smartSlicingTIFForTWAP;
	}

	public double getSmartSlicingTriggerAmount(Organization org) {
		Double value = smartSlicingTriggerAmountMap.get(org.getShortName());
		return value != null ? value.doubleValue() : smartSlicingTriggerAmount;
	}

	
	@Override
	public int getSwitchAlgoAbsoluteAggrressionInterval()
	{
		return switchAlgoAbsoluteAggressionInterval;
	}

	@Override
	public double getSwitchAlgoPercentageAggrressionInterval()
	{
		return switchAlgoPercentageAggressionInterval;
	}
	
	@Override
	public Integer getSwitchAlgoAbsoluteAggrressionInterval(Organization org)
	{
		Integer absoluteAggressionInterval = switchAlgoAbsoluteAggressionIntervalMap.get(org.getShortName());
		return absoluteAggressionInterval!=null ? absoluteAggressionInterval :switchAlgoAbsoluteAggressionInterval;
	}

	@Override
	public Double getSwitchAlgoPercentageAggrressionInterval(Organization org)
	{

		Double percentageAggressionInterval = switchAlgoPercentageAggressionIntervalMap.get(org.getShortName());
		return percentageAggressionInterval!=null ? percentageAggressionInterval :switchAlgoPercentageAggressionInterval;
	
	}

	@Override
	public boolean isSmartAdjustingTWAPEnabled( String org )
	{
		Boolean value = smartAdjustingTWAPEnabledMap.get(org);
		return value != null ? value : smartAdjustingTWAPEnabled;
	}
	
	@Override
	public double getFAStreamTolerance(String fiOrg, String user)
	{
		Double faStreamTolerance = faStreamToleranceMap.get(fiOrg+"."+user);
		if( faStreamTolerance != null ){
			return faStreamTolerance;
		}
		faStreamTolerance = faStreamToleranceMap.get(fiOrg);
		return faStreamTolerance!=null ? faStreamTolerance :getFAStreamTolerance();	
	}
	
	public double getFAStreamTolerance(){
		return faStreamTolerance;
	}

	@Override
	public boolean allowFAMatchWhenNonFAVwapNotAvailable(String fiOrg) {
		Map<String,Boolean> bMap = allowFAMatchIfNonFANotAvailableMap;
		if( bMap != null ){
			Boolean v = bMap.get(fiOrg);
			if( v != null){
				return v;
			}
		}
		return allowFAMatchIfNonFANotAvailable;
	}

	@Override
	public boolean isSortQuotesInSnapShot() {
		// TODO Auto-generated method stub
		return sortQuotes;
	}

	@Override
	public Collection<String> orderTypesSupportedInClob(String org, String ccyPair){
		Collection<String> collection = null;
		if(org != null && ccyPair != null){
				collection = marketOrderRoutedToClobMap.get(org + '.' + ccyPair);
			}
		if(collection == null && org != null){
				collection = marketOrderRoutedToClobMap.get(org);
			}
		if(collection == null){
				collection = marketOrderRoutedToClob;
			}
		return collection;
	}

	public boolean isV4Enabled(String orgName){
		Boolean flag = isV4EMSEnabledMap.get(orgName);
		flag = flag!=null?flag:isV4EMSEnabled;
		return flag;
	}
	public boolean isLocalV4Enabled(){
		return this.isLocalV4EMSEnabled;
	}

	public boolean isLocalV4Enabled(String orgName){
		if(orgName == null) return false;
		Boolean flag = isLocalV4EMSEnabledMap.get(orgName);
		flag = flag!=null?flag:false;
		return flag;
	}
	public byte getDirectedOrderResponseMaxStringLength(){
		return directedOrderResponseMaxStringLength;
	}

	public boolean ShowRejectReason(String fiOrg){
		if(fiOrg == null) return false;
		Boolean flag = showRejectReasonMap.get(fiOrg);
		flag = flag!=null?flag:showRejectReasonDefault;
		return flag;
	}

	@Override
	public int getZeroLiquidityActiveQuotesInOrderBookFlags(Organization customerOrg) {
		Map<String,Integer> bMap = zeroLiquidityActiveQuotesInOrderBookFlagsMap;
		if( bMap != null ){
			Integer val = bMap.get(customerOrg.getShortName());
			if( val != null ){
				return val;
			}
			if( customerOrg.getBrokerOrganization() != null ){
				String key = "BrokerCustomers."+customerOrg.getBrokerOrganization().getShortName();
				val = bMap.get(key);
				if( val != null ){
					return val;
				}
			}
		}
		return zeroLiquidityActiveQuotesInOrderBookFlags;
	}

	@Override
	public int getLoginChannelNotificationMode() {
		return loginChannelNotificationMode;
	}

	public boolean isExtDestinationOrderAmendEnabled(String orgShortName){
		Map<String,Boolean> bMap = exDestinationOrderAmendEnabledMap;
		if( bMap != null ){
			Boolean val = bMap.get(orgShortName);
			if( val != null ){
				return val;
			}
		}
		return exDestinationOrderAmendEnabled;

	}

	@Override
	public boolean isLinkedOCOCancellationEnabled(String userOrgShortName) {
		Map<String,Boolean> bMap = linkedOCOCancelEnabledMap;
		if( bMap != null ){
			Boolean val = bMap.get(userOrgShortName);
			if( val != null ){
				return val;
			}
		}
		return linkedOCOCancelEnabled;

	}

	@Override
	public boolean isUndisclosedProviderMatchDisabled(Organization org, String orderType) {
		Map<String,Boolean> bMap = undisclosedProviderMatchDisabledMap;
		if( bMap != null ){
			Boolean val = bMap.get(org.getShortName() + "." + orderType);
			if( val != null ){
				return val;
			}
			val = bMap.get(org.getShortName());
			if( val != null ){
				return val;
			}
			val = bMap.get("*." + orderType);
			if( val != null ){
				return val;
			}
		}
		return undisclosedProviderMatchDisabled;
	}


	public boolean isVenueReRoutingOrdersEnabled( String orgShortName, String ccyPair )
	{
		Map<String,Boolean> bMap = venueReRoutingOrdersEnabledMap;
		Boolean flag = null;
		if ( orgShortName != null && ccyPair != null )
		{
			flag = bMap.get ( orgShortName +"."+ ccyPair );
			if( flag != null )
			{
				return flag;
			}
			flag = bMap.get ( orgShortName);
		}
		return flag != null ? flag : venueReRoutingOrdersEnabled;
	}
}