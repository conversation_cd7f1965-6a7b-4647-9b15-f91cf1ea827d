package com.integral.adaptor.order.configuration;

// Copyright (c) 1999-2002 Integral Development Corp. All rights reserved.

import com.integral.adaptor.config.AdaptorConfigurationMBean;
import com.integral.finance.currency.CurrencyPair;
import com.integral.model.dealing.OrderRequest;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.List;

/**
 * Provides configuration capabilities for the order management system.
 */
public interface OrderConfigurationMBean extends AdaptorConfigurationMBean
{
	public final static String ORDER_BOOK_PUBLISHING_INTERVAL = "IDC.IS.ORDERBOOK.PUBLISHING.INTERVAL";
	public final static String QUOTE_PUBLICATION_INTERVAL = "IDS.IS.ORDER.Quote.Publication.Interval";
	public final static String BEST_BIDOFFER_PUBLICATION_INTERVAL = "IDS.IS.ORDER.Quote.BestBidOffer.Publication.Interval";
	public static final String ORDER_INTRAFLOOR_MATCHING_ENABLED = "IDC.IS.ORDER.INTRAFLOOR_MATCHING_ENABLED";
	public static final String ORDER_REGULARSIZEBUGFIX_ENABLED = "IDC.IS.ORDER.REGULARSIZEBUGFIX_ENABLED";
	//Below constant is @deprecated use IDC_IS_ORDER_PROVIDERS_LIST_SHORTNAME
	//public static final String ORDER_PROVIDERS_LIST_SHORTNAME = "IDC.IS.ORDER.Providers.list.ShortName";
	public static final String ORDER_CANCELLATION_PERIOD = "IDC.IS.ORDER.CANCELLATION.PERIOD";
	public static final String IDC_OA_CANCEL_ALL_NONPERSISTENT_ACTIVE_ORDERS = "Idc.OrderAdaptor.Startup.CancelNonPersistentActiveOrders";
	public static final String ORDER_STATUS_QUERY_PERIOD = "IDC.IS.ORDER.STATUS.PERIOD";
	public static final String ORDER_MASS_STATUS_REQUEST_ENABLED = "IDC.IS.ORDER.MASS.STATUS.ENABLED";

	public static final String ORDER_EXECUTORS_POOL_KEEP_ALIVE_TIME = "IDS.IS.ORDER.OrderExecutorsPool.KeepAliveTime";
	public static final String ORDER_EXECUTORS_POOL_MAX_SIZE = "IDS.IS.ORDER.OrderExecutorsPool.Max.Size";
	public static final String ORDER_EXECUTORS_POOL_CORE_SIZE = "IDS.IS.ORDER.OrderExecutorsPool.Initial.Size";

	public static final String ORDERBROADCASTER_EXECUTORS_POOL_KEEP_ALIVE_TIME = "IDS.IS.ORDER.OrderBroadcasterExecutorsPool.KeepAliveTime";
	public static final String ORDERBROADCASTER_EXECUTORS_POOL_MAX_SIZE = "IDS.IS.ORDER.OrderBroadcasterExecutorsPool.Max.Size";
	public static final String ORDERBROADCASTER_EXECUTORS_POOL_CORE_SIZE = "IDS.IS.ORDER.OrderBroadcasterExecutorsPool.Initial.Size";

	public static final String QUOTE_EXECUTORS_POOL_KEEP_ALIVE_TIME = "IDS.IS.ORDER.QuoteExecutorsPool.KeepAliveTime";
	public static final String QUOTE_EXECUTORS_POOL_MAX_SIZE = "IDS.IS.ORDER.QuoteExecutorsPool.Max.Size";
	public static final String QUOTE_EXECUTORS_POOL_CORE_SIZE = "IDS.IS.ORDER.QuoteExecutorsPool.Initial.Size";

	public static final String ORDER_FILLED_TRADE_REJECTION_REASON = "IDC.IS.ORDER.TradeRejectionReason";

	public static final String BROADCAST_ORDER_ADAPTOR_SETUP = "IDC.OrderAdaptor.BroadcastOrdersOnly";
	public static final String REMATCH_AFTER_DELAYED_REJECTION = "IDC.Orders.RematchAfterDelayedRejection";
	public static final String REMATCH_AFTER_DELAYED_REJECTION_ORDER_TYPES = "IDC.Orders.RematchAfterDelayedRejection.OrderTypes";
	public static final String REMATCH_AFTER_DELAYED_REJECTION_MAX_SLIPPAGE = "IDC.Orders.RematchAfterDelayedRejection.MaxSlippage";

	//Moved from ISMbean to here
	public static final String IDC_IS_ORDER_BROADCAST_ENABLED = "IDC.IS.ORDER.BROADCAST.ENABLED";
	public static final String IDC_IS_ORDER_MINIMUM_MARKETRATE_AMOUNT = "IDC.IS.ORDER.MINIMUM.MARKETRATE.AMOUNT";
	public static final String IDC_IS_OMS_LOGF4J_CATEGORY = "IDC.IS.OMS.LOGF4J.CATEGORY";
	public static final String IDC_IS_CLIENTCROSSING_ENABLED = "IDC.IS.CLIENTCROSSING_ENABLED";
	public static final String IDC_IS_ORDER_INTRAFLOOR_MATCHING_ENABLED = "IDC.IS.ORDER.INTRAFLOOR_MATCHING_ENABLED";
	public static final String IDC_IS_ORDER_PROVIDERS_LIST_SHORTNAME = "IDC.IS.ORDER.Providers.list.ShortName";

	public static final String IDC_IS_ORDER_HIDDEN_PROVIDERS_LIST = "IDC.ORDER.Providers.Hidden";
	public static final String IDC_IS_ORDER_UNSPREADED_PROVIDERS_LIST = "IDC.ORDER.Providers.Spreaded";
	public static final String IDC_IS_ORDER_EXCLUDED_PROVIDERS_PREFIX = "IDC.ORDER.Providers.Excluded.";
	public static final String IDC_IS_ORDER_HIDDEN_PROVIDERS_ENABLED = "IDC.ORDER.Providers.Hidden.Enabled";
	public static final String IDC_IS_ORDER_UNSPREADED_PROVIDERS_ENABLED = "IDC.ORDER.Providers.Spreaded.Enabled";
	public static final String IDC_IS_ORDER_EXCLUDED_PROVIDERS_ENABLED = "IDC.ORDER.Providers.Excluded.Enabled";
	public static final String IDC_IS_ORDER_SPREADED_PROVIDERS_ASSOCIATE_PROPER_TIER = "IDC.ORDER.Spreaded.Providers.Associate_Proper_Tier";

	// VWAP execution band related properties
	public static final String VWAP_EXECUTION_BANDS_NUMBER = "Idc.Orders.VWAPExecution.NumBands";
	public static final String VWAP_EXECUTION_BAND_FLOOR = "Idc.Orders.VWAPExecution.BandFloor";
	public static final String VWAP_EXECUTION_MINIMUM_BAND_SIZE = "Idc.Orders.VWAPExecution.MinBandSize";
	public static final String VWAP_EXECUTION_BAND_FLOOR_DEFAULT = "10000.0";
	public static final String VWAP_EXECUTION_MINIMUM_BAND_SIZE_DEFAULT = "10000.0";

	public static final String TAKER_ORDER_EXECUTORS_POOL_KEEP_ALIVE_TIME = "IDS.IS.ORDER.TakerOrderExecutorsPool.KeepAliveTime";
	public static final String TAKER_ORDER_EXECUTORS_POOL_MAX_SIZE = "IDS.IS.ORDER.TakerOrderExecutorsPool.Max.Size";
	public static final String TAKER_ORDER_EXECUTORS_POOL_CORE_SIZE = "IDS.IS.ORDER.TakerOrderExecutorsPool.Initial.Size";

	public static final String IDC_IS_ORDER_MATCH_CREDIT_CHECK_ENBLED = "IDC.IS.ORDER.MATCH.CREDIT.CHECK.ENABLED";
	public static final String IDC_IS_ORDER_USE_QUOTEEXECUTORS = "IDC.IS.ORDER.USE.QUOTEEXECUTORS";
    public static final String IDC_IS_ORDER_USE_EXECUTORS_FOR_ROUTING = "IDC.IS.ORDER.USE.EXECUTORS.FOR.ROUTING";
	public static final String IDC_IS_ORDER_USE_EXECUTORS_FOR_CHECKING_MARKETABILITY = "IDC.IS.ORDER.USE.EXECUTORS.FOR.CHECKING.MARKETABILITY";

	public static final String IDC_IS_ORDER_MAX_DISPLAY_AMOUNT_PREFIX = "IDC.IS.ORDER.MAX_DISPLAY_AMOUNT";

	public static final String IDC_IS_ORDER_OPADJUSTMENT_ENABLED = "IDC.IS.ORDER.PRICEADJUSTMENT.ENABLED";
	public static final String IDC_IS_ORDER_OPADJUSTMENT_PREFIX = "IDC.IS.ORDER.PRICEADJUSTMENT.PIPS.";
	public static final String IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_CHANNELS = "IDC.IS.ORDER.PRICEADJUSTMENT.SUPPORTED.CHANNELS";
	public static final String IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_ORDERTYPES = "IDC.IS.ORDER.PRICEADJUSTMENT.SUPPORTED.ORDERTYPES";
	public static final String IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_CHANNELS_DEFAULT = "DNET/PD,DNET/PP,PNET/PD,PNET/PP";
	public static final String IDC_IS_ORDER_OPADJUSTMENT_SUPPORTED_ORDERTYPES_DEFAULT = "LIMIT,MarketRangeOrder";
	/**
	 * Top level flag to enable/disable circuit breakers.
	 */
	public static final String IDC_ORDERADAPTOR_CIRCUIT_BREAKERS_ENABLED = "Idc.OrderAdaptor.Circuit.Breakers.Enabled";
	public static final String IDC_ORDERADAPTOR_REJECTION_DISABLEPROVIDER = "Idc.OrderAdaptor.Rejection.DisableProvider";

	/**
	 * Properties for flash orders
	 */
	public static final String IDC_ORDER_MATCH_DELAY = "Idc.OrderAdaptor.OrderMatch.Delay";
	public static final String IDC_ORDER_MATCH_DELAY_PREFIX = "Idc.OrderAdaptor.OrderMatch.Delay.";
	public static final String IDC_FLASH_ORDER_TYPES = "Idc.OrderAdaptor.Flash.OrderTypes";
	public static final String IDC_FLASH_ORDER_TYPES_PREFIX = "Idc.OrderAdaptor.Flash.OrderTypes.";
	public static final String IDC_FLASH_ORDER_TIFS = "Idc.OrderAdaptor.Flash.OrderTIF";
	public static final String IDC_FLASH_ORDER_TIFS_PREFIX = "Idc.OrderAdaptor.Flash.OrderTIF.";
	public static final String IDC_FLASH_MIN_SIZE = "Idc.OrderAdaptor.Flash.MinSize";
	public static final String IDC_FLASH_MIN_SIZE_PREFIX = "Idc.OrderAdaptor.Flash.MinSize.";
	public static final String IDC_FLASH_MAX_SIZE = "Idc.OrderAdaptor.Flash.MaxSize";
	public static final String IDC_FLASH_MAX_SIZE_PREFIX = "Idc.OrderAdaptor.Flash.MaxSize.";
	public static final int IDC_FLASH_MATCH_THEN_BROADCAST = -1;
	public static final int IDC_FLASH_BROADCAST_AND_MATCH = 0;
	public static final int IDC_FLASH_MATCH_TOB_THEN_BROADCAST = -2;
	public static final String IDC_FLASH_ORDER_TYPES_DEFAULT = "LIMIT,MARKET,STOP,STOPLIMIT,TLSTOP";
	public static final String IDC_FLASH_ORDER_TIFS_DEFAULT = "IOC,FOK,GTD,GTC,DAY";
	public static final String IDC_FLASH_LIFT_TIMEOUT = "Idc.OrderAdaptor.Flash.LiftTimeout";
	public static final String IDC_FLASH_LIFT_TIMEOUT_PREFIX = "Idc.OrderAdaptor.Flash.LiftTimeout.";
	public static final int IDC_FLASH_LIFT_TIMEOUT_DEFAULT = 0;

	public static final String IDC_CUSTOMER_REGULAR_SIZE = "Idc.CustomerRegularSize";
	public static final String IDC_CUSTOMER_REGULAR_SIZE_PREFIX = "Idc.CustomerRegularSize.";
	public static final String IDC_ORDER_STRATEGY_TTL = "Idc.Order.Strategy.SliceTTL";
	public static final String IDC_ORDER_STRATEGY_MINSLICEINTERVAL = "Idc.Order.Strategy.MinSliceInterval";
	public static final String IDC_FLASH_UPDATE_PRICE_ON_MATCH = "Idc.OrderAdaptor.Flash.UpdatePriceOnOrderMatch";

	public static final String IDC_ORDER_IOC_EXPIRYTIME = "Idc.Orders.IOC_FOK.ExpiryTime";
	public static final String IDC_ORDER_IOC_EXPIRYTIME_PREFIX = IDC_ORDER_IOC_EXPIRYTIME + '.';
	public static final String IDC_ORDER_IOC_EXPIRYTIME_BRKRCUST_PREFIX = "Idc.Orders.IOC_FOK.ExpiryTime.BrokerCustomers.";
	public static final String IDC_ORDER_IOC_EXPIRYTIME_CHANNELS = "Idc.Orders.IOC_FOK.ExpiryTime.Channels";
	public static final String IDC_ORDER_IOC_EXPIRYTIME_CHANNELS_PREFIX = IDC_ORDER_IOC_EXPIRYTIME_CHANNELS + '.';
	public static final String IDC_ORDER_IOC_EXPIRYTIME_EXCLUSION_PATTERN = "Idc.Orders.IOC_FOK.ExpiryTime.BrokerCustomers.*|Idc.Orders.IOC_FOK.ExpiryTime.Channels.*";
	public static final String IDC_ORDER_IOC_EXPIRYTIMEONMATCH = "Idc.Orders.IOC_FOK.ExpiryTimeOnMatch";
	public static final String IDC_ORDER_IOC_EXPIRYTIMEONMATCH_PREFIX = "Idc.Orders.IOC_FOK.ExpiryTimeOnMatch.";
	public static final String IDC_ORDER_IOC_EXPIRYTIMEONMATCH_BRKRCUST_PREFIX = "Idc.Orders.IOC_FOK.ExpiryTimeOnMatch.BrokerCustomers.";
	public static final String IDC_ORDER_IOC_EXPIRYTIMEONMATCH_EXCLUSION_PATTERN = "Idc.Orders.IOC_FOK.ExpiryTimeOnMatch.BrokerCustomers.*";

	public static final String IDC_ORDER_SETORDERRATE_TO_FILLRATE = "Idc.OrderAdaptor.SetOrderRateToFillRate";
	public static final String IDC_ORDER_SETORDERRATE_TO_FILLRATE_PREFIX = "Idc.OrderAdaptor.SetOrderRateToFillRate.";
	public static final String IDC_ORDER_SETORDERRATE_TO_FILLRATE_BRKRCUST_PREFIX = "Idc.OrderAdaptor.SetOrderRateToFillRate.BrokerCustomers.";
	public static final String IDC_ORDER_SETORDERRATE_TO_FILLRATE_EXCLUSION_PATTERN = "Idc.OrderAdaptor.SetOrderRateToFillRate.BrokerCustomers.*";
	public static final String IDC_ORDER_DEFAULTMKTRANGE_PREFIX = "IDS.IS.ORDER.DefaultMarketRange.";
	public static final String IDC_ORDER_DEFAULTMKTRANGE = "IDS.IS.ORDER.DefaultMarketRange";


    public static final String IDC_ORDER_PERCENTAGE_MKTRANGE = "IDC.IS.ORDER.Default.MarketRange.Percentage";
    public static final String IDC_ORDER_PERCENTAGE_MKTRANGE_PREFIX = IDC_ORDER_PERCENTAGE_MKTRANGE + ".";

    public static final String IDC_FCS_OPT_FOK_EXEC = "Idc.FCS.OptimizedFOKExecution.Enabled";
	public static final String IDC_FCS_OPT_FOK_EXEC_PREFIX = "Idc.FCS.OptimizedFOKExecution.Enabled.";

	public static final String IDC_FCS_OPT_FOK_EXEC_PRICETYPE_VWAP = "Idc.FCS.OptimizedFOKExecution.PriceType.VWAP";

	public static final String IDC_OA_REJ_DisableProvider_TimePeriod = "Idc.OrderAdaptor.Rejection.DisableProvider.TimePeriod";
	public static final String IDC_OA_REJ_DisableProvider_TimePeriod_prefix = "Idc.OrderAdaptor.Rejection.DisableProvider.TimePeriod.";
	public static final String IDC_ORDERADAPTOR_REJECTION_DISABLEPROVIDER_EXCLUSION_PETTERN = "Idc.OrderAdaptor.Rejection.DisableProvider.TimePeriod";

	public static final String IDC_OA_FAM_EXECINST_TOB = "Idc.OrderAdaptor.FillAtMarket.TOB";
	public static final String IDC_OA_FAM_EXECINST_TOB_PREFIX = "Idc.OrderAdaptor.FillAtMarket.TOB.";

	public static final String IDC_OA_ET_TOLERANCE_DEFAULT = "Idc.OrderAdaptor.EffectiveTime.Tolerance";
	public static final String IDC_OA_ET_TOLERANCE_PREFIX = "Idc.OrderAdaptor.EffectiveTime.Tolerance.";
	public static final String IDC_OA_ET_ENABLED = "Idc.OrderAdaptor.EffectiveTime.Enabled";
	public static final String IDC_OA_ET_ENABLED_PREFIX = "Idc.OrderAdaptor.EffectiveTime.Enabled.";

	public static final String IDC_ORDERADAPTOR_REJECTADDITIONALPRECISIONORDERS = "Idc.OrderAdaptor.RejectAdditionalPrecisionOrders";

	public static final String IDC_DO_MAX_NUM_TIER = "Idc.DisplayedOrders.MaxNumTiersToBroadcast";
	public static final String IDC_DO_MAX_NUM_TIER_PREFIX = "Idc.DisplayedOrders.MaxNumTiersToBroadcast.";
	
	public static final String IDC_OA_DIRECT_ORDER_BA_ENABLED = "Idc.OrderAdaptor.Direct.Order.BrokerAdaptor.Enabled";
	public static final String IDC_OA_DIRECT_ORDER_BA_PREFIX = "Idc.OrderAdaptor.Direct.Order.BrokerAdaptor.Enabled.";

	public static final String IDC_OA_DIRECT_ORDER_LP_ENABLED = "Idc.OrderAdaptor.Direct.Order.ProviderAdaptor.Enabled";
	public static final String IDC_OA_DIRECT_ORDER_LP_PREFIX = "Idc.OrderAdaptor.Direct.Order.ProviderAdaptor.Enabled.";

    /*
        Buffer size for caching provider quotes
     */
    public static final String IDC_OA_PROVIDER_QUOTE_CACHE_SIZE = "Idc.OrderAdaptor.Provider.QuoteCache.Size";
    public static final String IDC_OA_PROVIDER_QUOTE_CACHE_SIZE_PREFIX = "Idc.OrderAdaptor.Provider.QuoteCache.Size.";

    public static final String IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH = "Idc.OrderAdaptor.Provider.Quote.History.Match.Depth";
    public static final String IDC_OA_PROVIDER_QUOTE_HISTORY_MATCH_DEPTH_PREFIX = "Idc.OrderAdaptor.Provider.Quote.History.Match.Depth.";

    // property to disable broadcast of orders dealt in term
    public static final String IDC_DO_TERM_CURRENCY_BROADCAST_ENABLED = "Idc.DisplayedOrders.TermCurrency.Broadcast.Enabled";
    public static final String IDC_DO_TERM_CURRENCY_BROADCAST_ENABLED_PREFIX = "Idc.DisplayedOrders.TermCurrency.Broadcast.Enabled.";
	 

    public static final String IDC_FLASH_EXPIRY = "Idc.OrderAdaptor.Flash.Expiry";
    public static final String IDC_FLASH_EXPIRY_PREFIX = "Idc.OrderAdaptor.Flash.Expiry.";

    public static final String IDC_ALGO_FAM_EXPIRY_INTERVAL = "Idc.OrderAdaptor.Algo.FillAtMarket.Expiry.Interval";

    public static final String IDC_IS_DISPLAY_ORDER_PROVIDERS_BLACK_LIST = "IDC.IS.DisplayOrderProviders.Black.List";

    public static final String IDC_OA_PERSISTENT_ORDER_MIGRATION_ENABLED = "Idc.OrderAdaptor.PersistentOrder.Migration.To.Spaces.Enabled";

    public static final String IDC_TWAP_REGULAR_SIZE        = "Idc.TWAP.RegularSize";
    public static final String IDC_TWAP_REGULAR_SIZE_PREFIX = "Idc.TWAP.RegularSize.";

    public static final String IDC_TRADING_VENUE_ORDER_ACK_ALERT_TIME = "Idc.DirectedOrder.Submit.Response.Alter.Time";

	static final String SANITY_FILTER_PREFIX = "Idc.DisplayedOrders.Sanity.Filter.";

	static final String IDC_DISPLAY_ORDER_SANITY_FILTER_ENABLED = SANITY_FILTER_PREFIX + "Enabled";

	static final String IDC_DISPLAY_ORDER_SANITY_FILTER_PERCENTAGE = SANITY_FILTER_PREFIX + "Percentage";

    public static final String IDC_ORDER_FIXING_TWAP_START_INTERVAL = "Idc.Order.FixingTWAP.Start.Interval";

    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_ALGORITHM = "Idc.Order.Aggregated.Fill.Price.Improvement.Algorithm";
    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_ALGORITHM_PREFIX = "Idc.Order.Aggregated.Fill.Price.Improvement.Algorithm.";

    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FEE_TYPE = "Idc.Order.Aggregated.Fill.Price.Improvement.Fee.Type";
    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FEE_TYPE_PREFIX = "Idc.Order.Aggregated.Fill.Price.Improvement.Fee.Type.";
    
    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_PERCENTAGE_TO_CUSTOMER = "Idc.Order.Aggregated.Fill.Price.Improvement.Percentage.To.Customer";
    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_PERCENTAGE_TO_CUSTOMER_PREFIX = "Idc.Order.Aggregated.Fill.Price.Improvement.Percentage.To.Customer.";
    
    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FIXED_FEE_TO_CUSTOMER = "Idc.Order.Aggregated.Fill.Price.Improvement.Fixed.Fee.To.Customer";
    public static final String IDC_ORDER_AGGREGATED_FILL_PRICE_IMPROVEMENT_FIXED_FEE_TO_CUSTOMER_PREFIX = "Idc.Order.Aggregated.Fill.Price.Improvement.Fixed.Fee.To.Customer.";

    public static final String IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED = "Idc.Order.Route.UnmatchedAmount.To.Clob.Enabled";
    public static final String IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX = "Idc.Order.Route.UnmatchedAmount.To.Clob.Enabled.";

    public static final String IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_MAX_NO_OF_TIMES = "Idc.Order.Route.UnmatchedAmount.To.Clob.Max.No.Of.Times";
    public static final String IDC_ORDER_ROUTE_UNMATCHED_AMOUNT_MAX_NO_OF_TIMES_PREXFIX = "Idc.Order.Route.UnmatchedAmount.To.Clob.Max.No.Of.Times.";

	public static final String IDC_ORDER_ROUTE_TERMCURRENCY_AMOUNT_ENABLED = "Idc.Order.Route.TermCurrencyAmount.To.Clob.Enabled";
	public static final String IDC_ORDER_ROUTE_TERMCURRENCY_AMOUNT_ENABLED_PREFIX = IDC_ORDER_ROUTE_TERMCURRENCY_AMOUNT_ENABLED+".";
	public static final String IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED = "Idc.Order.Route.Partials.UnmatchedAmount.To.Clob.Enabled";
	public static final String IDC_ORDER_ROUTE_PARTIALLY_FILLED_UNMATCHED_AMOUNT_TO_CLOB_ENABLED_PREFIX = "Idc.Order.Route.Partials.UnmatchedAmount.To.Clob.Enabled.";

	public static final String IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED = "Idc.Check.Marketability.Of.Venue.Routed.Orders.Enabled";
	public static final String IDC_CHECK_MARKETABILITY_OF_VENUE_ROUTED_ORDERS_ENABLED_PREFIX = "Idc.Check.Marketability.Of.Venue.Routed.Orders.Enabled.";

	public static final String IDC_MV_VENUE_RE_ROUTING_ORDERS_ENABLED = "Idc.MV.Venue.ReRouting.Orders.Enabled";
	public static final String IDC_MV_VENUE_RE_ROUTING_ORDERS_ENABLED_PREFIX = IDC_MV_VENUE_RE_ROUTING_ORDERS_ENABLED + ".";

    public static final String IDC_RISKNET_DIRECTED_ORDER_MIN_RESTING_TIME = "Idc.Risknet.Directed.Order.Min.Resting.Time";
    public static final String IDC_RISKNET_DIRECTED_ORDER_MIN_RESTING_TIME_PREFIX = "Idc.Risknet.Directed.Order.Min.Resting.Time.";

    public static final String IDC_ORDERADAPTOR_MARKET_SNAPSHOT_TRUNCATE_ENABLED = "Idc.OrderAdaptor.Market.Snapshot.Truncate";

    public static final String SmartSlicing_Enabled_prefix="Idc.OrderAdaptor.SmartSlicing.Enabled.";
    public static final String SmartSlicing_Enabled="Idc.OrderAdaptor.SmartSlicing.Enabled";
    public static final String SmartSlicing_MaxSliceInterval_prefix="Idc.OrderAdaptor.SmartSlicing.MaxSliceInterval.";
    public static final String SmartSlicing_MaxSliceInterval="Idc.OrderAdaptor.SmartSlicing.MaxSliceInterval";
    public static final String SmartSlicing_MinSliceInterval_prefix="Idc.OrderAdaptor.SmartSlicing.MinSliceInterval.";
    public static final String SmartSlicing_MinSliceInterval="Idc.OrderAdaptor.SmartSlicing.MinSliceInterval";
    public static final String SmartSlicing_OptimalSliceInterval_prefix="Idc.OrderAdaptor.SmartSlicing.OptimalSliceInterval.";
    public static final String SmartSlicing_OptimalSliceInterval="Idc.OrderAdaptor.SmartSlicing.OptimalSliceInterval";
    public static final String SmartSlicing_OptimalSliceSize_prefix="Idc.OrderAdaptor.SmartSlicing.OptimalSliceSize.";
    public static final String SmartSlicing_OptimalSliceSize="Idc.OrderAdaptor.SmartSlicing.OptimalSliceSize";
    public static final String SmartSlicing_SliceIntervalRandomization_prefix="Idc.OrderAdaptor.SmartSlicing.SliceIntervalRandomization.";
    public static final String SmartSlicing_SliceIntervalRandomization="Idc.OrderAdaptor.SmartSlicing.SliceIntervalRandomization";
    public static final String SmartSlicing_SliceSizeCeilingFactor_prefix="Idc.OrderAdaptor.SmartSlicing.SliceSizeCeilingFactor.";
    public static final String SmartSlicing_SliceSizeCeilingFactor="Idc.OrderAdaptor.SmartSlicing.SliceSizeCeilingFactor";
    public static final String SmartSlicing_SliceSizeRandomization_prefix="Idc.OrderAdaptor.SmartSlicing.SliceSizeRandomization.";
    public static final String SmartSlicing_SliceSizeRandomization="Idc.OrderAdaptor.SmartSlicing.SliceSizeRandomization";
    public static final String SmartSlicing_TIFForTWAP_prefix="Idc.OrderAdaptor.SmartSlicing.TIFForTWAP.";
    public static final String SmartSlicing_TIFForTWAP="Idc.OrderAdaptor.SmartSlicing.TIFForTWAP";
    public static final String SmartSlicing_TriggerAmount_prefix="Idc.OrderAdaptor.SmartSlicing.TriggerAmount.";
    public static final String SmartSlicing_TriggerAmount="Idc.OrderAdaptor.SmartSlicing.TriggerAmount";
    
    public static final String SwitchAlgo_AggressiveInterval_Absolute = "Idc.OrderAdaptor.SwitchAlgo.ActiveTime.Absolute";
    public static final String SwitchAlgo_AggressiveInterval_Absolute_Prefix = "Idc.OrderAdaptor.SwitchAlgo.ActiveTime.Absolute.";
    public static final String SwitchAlgo_AggressiveInterval_Percentage = "Idc.OrderAdaptor.SwitchAlgo.ActiveTime.Factor";
    public static final String SwitchAlgo_AggressiveInterval_Percentage_Prefix = "Idc.OrderAdaptor.SwitchAlgo.ActiveTime.Factor.";
    public static final String Smart_Adjusting_TWAP = "Idc.OrderAdaptor.SmartAdjustingTWAP";
    public static final String Smart_Adjusting_TWAP_Prefix = "Idc.OrderAdaptor.SmartAdjustingTWAP.";
    
    
    public static final String FULL_AMOUNT_STREAM_TOLERANCE_prefix = "Idc.OrderAdaptor.FullAmount.Stream.Tolerance.";
    public static final String FULL_AMOUNT_STREAM_TOLERANCE = "Idc.OrderAdaptor.FullAmount.Stream.Tolerance";
	public static final String FULL_AMOUNT_STREAM_AllowMatchIfNonFANotAvailable = "Idc.OrderAdaptor.FullAmount.AllowMatchIfNonFANotAvailable";
	public static final String FULL_AMOUNT_STREAM_AllowMatchIfNonFANotAvailable_Prefix = FULL_AMOUNT_STREAM_AllowMatchIfNonFANotAvailable+".";
    public static final String SORT_MARKET_SNAPSHOT = "Idc.OrderAdaptor.SortQuotes";
    public final static String ROUTE_ORDER_TO_OMS = "Idc.Oms.RouteOrderToOMS";
	public final static String OMS_CUSTOMER_ORDER_ADDITIONAL_STATES = "Idc.OMS.Customer.Order.Additional.States";
	String OMS_CUSTOMER_ORDER_PENDING_NEW_WITH_ORDERID = "Idc.OMS.Customer.Order.PendingNewWithOrderId";
	public final static String ROUTE_LINKED_ORDER_TO_OMS = "Idc.Oms.RouteLinkedOrderToOMS";
    public final static String OMS_MANUAL_ORDER_ACCEPTANCE_EXPIRY_TIME = "Idc.Oms.RouteOrderToOMS.OrderExpiryTime.Threshold";
	String ORDER_TYPES_TO_CLOB = "Idc.OrderAdaptor.OrderTypesToClob";
	String ROUTE_ORDER_TO_OMS_WITH_AUTO = "Idc.Oms.RouteOrderToOMSWithAuto";
	String ROUTE_ORDER_TO_OMS_WITH_AUTO_PREFIX = ROUTE_ORDER_TO_OMS_WITH_AUTO + ".";
	String ROUTE_FIXING_ORDER_TO_OMS_WITH_AUTO_ACCEPT = "Idc.Oms.RouteFixingOrderToOMSWithAutoAccept";
	String ROUTE_FIXING_ORDER_TO_OMS_WITH_AUTO_ACCEPT_PREFIX = ROUTE_FIXING_ORDER_TO_OMS_WITH_AUTO_ACCEPT + ".";

	String V4_ENABLED = "Idc.OrderAdaptor.V4EMSEnabled";
	String V4_ENABLED_PREFIX = V4_ENABLED+".";
	String LOCAL_V4_ENABLED = "Idc.OrderAdaptor.LOCAL.V4EMSEnabled";

	String LOCAL_V4_ENABLED_PREFIX = LOCAL_V4_ENABLED+".";

	String DIRECTEDORDER_RESPONSE_MAXSTRING_LENGTH="Idc.DirectedOrder.Response.MaxString.Length";

	String SHOW_REJECT_REASON = "Idc.OrderAdaptor.ShowRejectReason";

	String ZERO_LIQUIDITY_ACTIVE_QUOTES_IN_ORDER_BOOK_FLAGS = "Idc.OrderAdaptor.ZeroLiquidity.ActiveQuotesInOrderBook.Flags";
//	String LOGIN_CHANNEL_NOTIFICATION_MODE = "Idc.DirectedOrder.Response.MaxString.Length";
	String LOGIN_CHANNEL_NOTIFICATION_MODE = "Idc.OrderAdaptor.ClientHandler.LoginChannelNotificationMode";

	String EXTERNALDESTINATION_ORDERAMEND_ENABLED = "IDC.ExVenue.ExDestination.OrderAmend.Enabled";
	String LINKED_OCO_CANCEL_ENABLED = "Idc.OrderAdaptor.LinkedOCOCancel.Enabled";

	String OMS_ORDER_CREDIT_ENABLED = "Idc.OMS.Order.Credit.Enabled";
	String OMS_ORDER_CREDIT_ENABLED_PREFIX = OMS_ORDER_CREDIT_ENABLED + ".";

	String UNDISCLOSED_PROVIDER_MATCH_DISABLED = "Idc.OrderAdaptor.Undisclosed.Provider.Match.Disabled";
	String UNDISCLOSED_PROVIDER_MATCH_DISABLED_PREFIX = UNDISCLOSED_PROVIDER_MATCH_DISABLED + ".";

	public Long getOMSManualOrderAcceptanceExpiryTime(String brokerName);
    
    public List<OrderRequest.Type> getOrderTypeRouteToOMS(String brokerName, String fiName);
    
	public boolean isLinkedOrderRouteToOMS(String brokerName, String fiName);

	boolean isOrderRouteToOMSWithAuto(String brokerName, String fiName);

	boolean getOmsOrderCreditEnabled(String brokerName, String fiName);

	boolean isRouteFixingOrderToOMSWithAutoAccept(String brokerName, String fiName);

	public boolean isOmsCustomerOrderAdditionalStates(String brokerName, String fiName);

	boolean isOmsCustomerOrderPendingNewWithOrderId(String brokerName, String fiName);

    boolean isSortQuotesInSnapShot();
    
    boolean isDisplayedOrdersSanityFilterEnabled(String ccyPair);

	double getDisplayedOrdersSanityFilterPercentage(String ccyPair);

	/**
	 * Returns publishing interval for orders to app server
	 *
	 * @return order publish interval
	 */
	public int getOrderPublishingInterval();

	/**
	 * Returns publishing interval for quotes to app server
	 *
	 * @param organization organization
	 * @return quote publish interval
	 */
	public int getQuotePublicationInterval( String organization );

	/**
	 * Checks whether intrafloor matching enabled or not
	 *
	 * @return intra-floor match enabled
	 */
	public boolean isIntrafloorMatchingEnabled();

	/**
	 * returns the no of days for which active orders has to be cancelled.
	 *
	 * @return order cancellation period in days
	 */

	public int getOrderCancellationPeriod();

	/**
	 * returns whether to cancel active orders at startup or not.
	 *
	 * @return cancel all order at the end of session
	 */
	public boolean cancelAllNonPersistentActiveOrders();

	/**
	 * returns the no of days for which the order status has to be queried.
	 *
	 * @return order status query period in days
	 */
	public int getOrderStatusQueryPeriod();

	/**
	 * returns whether to sendOrderMassStatus is enabled or not.
	 *
	 * @return send order mass status
	 */
	public boolean isSendOrderMassStatusEnabled();

	public int getOrderExecutorsCorePoolSize();

	public int getOrderExecutorsMaxPoolSize();

	public int getOrderExecutorsPoolKeepAliveTime();

	public int getQuoteExecutorsCorePoolSize();

	public int getQuoteExecutorsMaxPoolSize();

	public int getQuoteExecutorsPoolKeepAliveTime();

	public String getTradeRejectionReason();

	/**
	 * An operation to cancel an order using a given order ID.
	 *
	 * @param orderID <code>String</code> order ID tio cancel.
	 * @return <code>true</code> if order was canceled successfully. Returns <code>false</code> is there was a problem
	 *         while cancelling the order.
	 */
	public boolean cancelOrder( final String orderID );

	/**
	 * System level property to control broadcast/display of orders.
	 *
	 * @return broadcast order enabled
	 */
	public boolean isOrderBroadcastEnabled();

	/**
	 * Log4j category for OrderMatchingExecution.log
	 *
	 * @return log category
	 */
	public String getOMSLogCategory();

	public String getOrderAdaptorProviderList();

	public double getOrderMinimumMarketRateAmount();

	public int getOrderBroadcasterExecutorsCorePoolSize();

	public int getOrderBroadcasterExecutorsMaxPoolSize();

	public int getOrderBroadcasterExecutorsPoolKeepAliveTime();

	/**
	 * Returns true if this OA instance is DirectOA setup. If it is false, it means it is a broadcast order only setup.
	 *
	 * @return direct OA setup
	 */
	public boolean isDirectOASetup();

	/**
	 * Returns true if the OA is used only for broadcast order from .Net client.
	 *
	 * @return broadcast only setup
	 */
	public boolean isBroadcastOnlySetup();

	/**
	 * Returns whether rematching of the order after a delayed rejection is enabled.
	 *
	 * @return rematch on delayed rejection
	 */
	public boolean isRematchAfterDelayedRejection();

	/**
	 * Returns a comma separated list of request classification shortnames which have to be rematched upon receiving a delayed rejection on any of the fills on them.
	 *
	 * @return order types
	 */
	public String getRematchAfterDelayedRejectionOrderTypes();

	/**
	 * Returns the list of request classifications of the orders which needs to be rematched once a delayed rejection is received in any of the fills.
	 *
	 * @return collection of request classifications
	 */
	public Collection<String> getRematchAfterDelayedRejectionOrderClassifications();

	/**
	 * Returns the maximum slippage allowed while rematching after a delayed rejection.
	 *
	 * @return slippage in pips
	 */
	public double getRematchAfterDelayedRejectionMaxSlippage();

	/**
	 * Returns the number of bands configured for VWAP execution. These bands will have matchable amounts at prices for VWAP execution.
	 *
	 * @return number of bands
	 */
	public int getVWAPExecutionNumberOfBands();

	/**
	 * Return the amount used to floor the VWAP execution bands. This will make the matchable amount in the band a number multiple of floor size.
	 *
	 * @return band floor amount
	 */
	public double getVWAPExecutionBandFloor();

	/**
	 * Returns the minimum amount for a VWAP band.
	 *
	 * @return minimum amount in the band
	 */
	public double getVWAPExecutionMinimumBandSize();

	/**
	 * Returns the best bidOffer publication interval for the passed organization.
	 *
	 * @param orgName org
	 * @return updateAdaptorCommonCache
	 */
	public int getBestBidOfferPublicationInterval( String orgName );

	/**
	 * Returns the best bidOffer publication interval.
	 *
	 * @return best bid/offer publication interval
	 */
	public int getBestBidOfferPublicationInterval();

	public List<Organization> getOrderAdaptorHiddenProviderList( String fiOrg );

	public boolean isHiddenProvider( String providerName, String fiOrg );

	//Vital thread pool parameters.
	public int getTakerOrderExecutorsCorePoolSize();

	public int getTakerOrderExecutorsMaxPoolSize();

	public int getTakerOrderExecutorsPoolKeepAliveTime();

	List<Organization> getOrderAdaptorUnSpreadedProviderList( String fiOrg );

	boolean isUnSpreadedProvider( String providerName, String fiOrg );

	boolean isUnSpreadedProvidersEnabled();

	boolean isHiddenProvidersEnabled();

	List<String> getOrderAdaptorExcludedProviderList( String fiOrg );

	public boolean isExcludedProvidersEnabled();

	/**
	 * Returns the collection of order provider organizations short names.
	 *
	 * @return order provider org shortnames
	 */
	public Collection<String> getOrderProvidersList();

	/**
	 * Returns the collection of order provider organizations.
	 *
	 * @return order provider organization list
	 */
	public Collection<Organization> getOrderProviderOrgsList();

	/**
	 * Returns the order providers as well as the orgs which set them as broker organization.
	 *
	 * @return list of order providers and their customer orgs
	 */
	public Collection<Organization> getExtendedOrderProviderOrgsList();

    public Collection<Organization> getExtendedOrderProviderOrgsListForHeartbeat();

	/**
	 * Returns whether the specified organization is an order provider.
	 *
	 * @param org organization
	 * @return order provider
	 */
	public boolean isOrderProvider( Organization org );

	/**
	 * Revaluates the list of extended providers. This will be normally done when the property is modified or broker for org is set in admin.
	 *
	 * @param updateAdaptorCommonCache update cache
	 */
	public void resetOrderProviderOrgsList( boolean updateAdaptorCommonCache );

	public boolean isCreditCheckEnabled();

	public boolean isUseQuoteExecutors();

    public boolean isUseExecutorsForRouting();

	public boolean isUseExecutorsForCheckingMarketability();

	/**
	 * Returns max show amount for a currency pair.
	 *
	 * @param ccyPairISOName ccy pair
	 * @return max show amount
	 */
	public Double getMaxShowAmount( String ccyPairISOName );

	public boolean isOPAdjustmentEnabled();

	public Double getOPAdjustmentPips( String orgName, String ccyPair );

	public List<String> getOPAdjustmentAllowedChannels();

	public List<String> getOPAdjustmentAllowedOrderTypes();

	public boolean isCircuitBreakersEnabled();

	public Integer getCircuitBreakerThreshold( String orderPlacingOrg );

	/**
	 * Returns the integer code representing the order matching delay.
	 * Value of -1 means first matching will be done and then remaining amount will be broadcasted without any delay
	 * Value of -2 means first TOB matching will be done and then remainder of the amount will be broadcasted.
	 * Value of 0 means order matching and broadcasting will be simultaneously done.
	 * Any non-zero positive integer means broadcast is done right away and order matching will be done after a delay of the value in milliseconds.
	 *
	 * @return flash order mode
	 */
	public int getOrderMatchDelay();

	/**
	 * Returns the integer code representing the order matching delay.
	 * Value of -1 means first matching will be done and then remaining amount will be broadcasted without any delay
	 * Value of -2 means first TOB matching will be done and then remainder of the amount will be broadcasted.
	 * Value of 0 means order matching and broadcasting will be simultaneously done.
	 * Any non-zero positive integer means broadcast is done right away and order matching will be done after a delay of the value in milliseconds.
	 *
	 * @param org organization
	 * @return flash order mode
	 */
	public int getOrderMatchDelay( Organization org );


    public int getOrderMatchDelay( String  orgName );

	/**
	 * Return the collection of order types supported for flash orders.
	 *
	 * @return order types
	 */
	public Collection<String> getFlashOrderTypes();

	/**
	 * Return the collection of order types supported for flash orders for the specified organization.
	 *
	 * @param org organization
	 * @return order types
	 */
	public Collection<String> getFlashOrderTypes( Organization org );

	/**
	 * Returns the collection of time in forces supported for flash orders.
	 *
	 * @return supported tif
	 */
	public Collection<String> getFlashOrderTIFs();

	/**
	 * Returns the collection of time in forces supported for flash orders for the specified organization.
	 *
	 * @param org organization
	 * @return supported tif
	 */
	public Collection<String> getFlashOrderTIFs( Organization org );

	/**
	 * Returns the minimum size for the flashing orders. If the order amount is below this value, then order will not be flashed.
	 *
	 * @param org     organization
	 * @param ccyPair currency pair
	 * @return minimum size
	 */
	public Double getFlashOrderMinSize( Organization org, CurrencyPair ccyPair );

	/**
	 * Returns the maximum size for the flashing orders. If the order amount is above this value, then order will not be flashed.
	 *
	 * @param org     organization
	 * @param ccyPair currency pair
	 * @return maximum size
	 */
	public Double getFlashOrderMaxSize( Organization org, CurrencyPair ccyPair );

	/**
	 * Returns the time in milliseconds a taker lift request will wait for any pending provider response.
	 *
	 * @return lift request wait time in milliseconds
	 */
	public int getFlashLiftTimeout();

	/**
	 * Returns the time in milliseconds a taker lift request from the specified organization will wait for any pending provider response.
	 *
	 * @param org org
	 * @return lift request wait time in milliseconds
	 */
	public int getFlashLiftTimeout( Organization org );

	/**
	 * Updates RequestHandlerFactoryRegistry and HeartBeatDestination cache for given organization.
	 *
	 * @param org org
	 */
	public void UpdateAdaptorCommonCache( Organization org );

	/**
	 * Returns the REGULAR SIZE for slices. Slice size/amount should in multiple of this size.
	 *
	 * @return lift request wait time in milliseconds
	 */
	public Double getCustomerRegularSize();

	/**
	 * Returns the REGULAR SIZE for slices for specified org. Slice size/amount should in multiple of this size.
	 *
	 * @param orgShortName short name of the customer organization
	 * @return regular size of slices for specified organization
	 */
	public Double getCustomerRegularSize( String orgShortName );

	/**
	 * Returns the REGULAR SIZE for slices for specified org. Slice size/amount should in multiple of this size.
	 *
	 * @param org org
	 * @return regular size of slices for specified org
	 */
	public Double getCustomerRegularSize( Organization org );

	/**
	 * Returns milliseconds for which slice should be active after activated.
	 *
	 * @return Time-To-Live for a slice in milliseconds.
	 */
	public int getOrderSliceTTL();

	/**
	 * Returns minimum slice interval. Value is in milliseconds
	 *
	 * @return minimum slice interval
	 */
	public int getOrderMinSliceInterval();

	/**
	 * Returns whether update on the order is flashed upon match or upon verification
	 *
	 * @return flash upon match vs. verification
	 */
	public boolean isFlashUpdatePriceOnMatch();

	public int getIOCExpiryTime();

	public int getIOCExpiryTime( String custOrgName );

	public int getIOCExpiryTimeForBrokerCustomers( String custOrgName );

	public boolean isExpiryTimeEnabledForChannel( String channelName );

	public boolean isExpiryTimeEnabledForChannel( String channelName, String custOrgName );

	public boolean isExpiryOnMatch();

	public Boolean isExpiryOnMatch( String custOrgName );

	public Boolean isExpiryOnMatchForBroker( String brokerOrgName );

	public Boolean isSetOrderRateToFillRate( String orgName );

	public Boolean isSetOrderRateToFillRateForBroker( String brokerOrgName );

	public boolean isSetOrderRateToFillRate();

	public double getDefaultMKTRange( Organization org, String ccyPair );

    public double getPercentageDefaultMKTRange( Organization org, String ccyPair );

	public boolean isOptimizedFOKExecutionEnabled( Organization org );

	public boolean isSendVWAPPriceOptimizedFOKExecution();

	public int getProviderSuspensionPeriod( Organization org );

	public boolean getFillAtMarketExecInst( Organization org );

	/**
	 * Returns true if effective time is enabled for the 'org' customer organization.
	 * @return boolean.
	 */
	public boolean isEffectiveTimeEnabled( Organization org );

	/**
	 * Returns a tolerance in the number of milliseconds, if difference between EffectiveTime/OrderExecuionStartTime and current time
	 * is less then tolerance the order will be submitted right away. Due to time constraint - OrderExecutionStartTime will be set to 
	 * null on request. 
	 * @param org
	 * @return
	 */
	public long getEffectiveTimeTolerance( Organization org );

	/**
	 * Returns max numbers of tiers to be published for an order book. 
	 * @param org
	 * @return 
	 */
	public int getMaxBroadcastTiers( String org );

	public boolean isSpreadedProviderProperTierAssociationEnabled();

	/**
	 * Returns the setting for whether to reject orders with more precision than the currency tick factor.
	 * 
	 * @return the setting for whether to reject orders with more precision than the currency tick factor.
	 */
	public boolean getRejectAdditionalPrecisionOrders();

	/**
	 * Returns true then orders submitted against broker adaptor will be directed/forwarded to it, if BA is the only provider
	 * @param org
	 * @return
	 */
	boolean isDirectOrderToBroker( Organization org );

	/**
	 * Returns true if orders submitted against LP adaptor will be directed/forwarded to it, if LP is the only provider
	 */
	boolean isDirectOrderToLP(Organization org, Organization realLp);

    int getProviderQuoteCacheSize();

    int getProviderQuoteCacheSize(String providerOrg);

    int getProviderQuoteHistoryMatchDepth();

    int getProviderQuoteHistoryMatchDepth(String fiOrg);

    int getOrderFlashExpiryPeriod();

    int getOrderFlashExpiryPeriod(String fiOrg);

     /**
     * Returns true if Displayed Order broadcast is enabled, otherwise false.
     * @return true if Displayed Order broadcast is enabled, otherwise false.
     */
    boolean isTermCurrencyBroadcastEnabled( String orgShortName );

    int getAlgoFAMExpiryInterval();

    public List<String> getDisplayOrderProvidersBlackList();

    boolean isPersistentOrderMigrationToSpacesEnabled();

    public double getTwapRegularSize(String fiOrg);

    public long getDirectedOrderAlertTimeForSubmit();

    public long getFixingTwapStartInterval();
    
    public void resetMDFServerEnabledList();

    public String getAggregatedFillPriceImprovementAlgorithm(String fiOrg);

    public double getAggregatedFillPriceImprovementPercentage(String fiOrg);
    
    public String getAggregatedFillPriceImprovementFeeType(String fiOrg) ;
    
    public double getAggregatedFillPriceImprovementFixedFee(String fiOrg) ;

    public int getMaxNoOfTimesToRouteToClob(String fiOrg);
    
    public boolean routeUnmatchedAmountToClobEnabled(String fiOrg);

	public boolean routePartialsUnmatchedAmountToClobEnabled(String fiOrg);

    public int getMinRestingTimeInRiskNet(String fiOrg);

    public boolean isMarketSnapshotTruncateEnabled();

	public boolean doMarketabilityCheckOnRoutedOrders(String fiOrg);

    public boolean getSmartSlicingEnabled(Organization org);

    public int getSmartSlicingMaxSliceInterval(Organization org);

	public int getSmartSlicingMinSliceInterval(Organization org);

	public int getSmartSlicingOptimalSliceInterval(Organization org);

	public int getSmartSlicingOptimalSliceSize(Organization org);

	public double getSmartSlicingSliceIntervalRandomization(Organization org);

	public double getSmartSlicingSliceSizeCeilingFactor(Organization org);

	public double getSmartSlicingSliceSizeRandomization(Organization org);

	public double getSmartSlicingTIFForTWAP(Organization org);

	public double getSmartSlicingTriggerAmount(Organization org);
	
	public int getSwitchAlgoAbsoluteAggrressionInterval();

	public double getSwitchAlgoPercentageAggrressionInterval();

    public Integer getSwitchAlgoAbsoluteAggrressionInterval(Organization org);
    
    public Double getSwitchAlgoPercentageAggrressionInterval(Organization org);
    
    public boolean isSmartAdjustingTWAPEnabled(String org);
    
    public double getFAStreamTolerance(String fiOrg, String user);

	boolean allowFAMatchWhenNonFAVwapNotAvailable(String fiOrg);

	Collection<String> orderTypesSupportedInClob(String org, String ccyPair);
	public boolean isV4Enabled(String orgName);
	public boolean routeTermCCYAmountToClobEnabled(String fiOrg);

	public boolean isLocalV4Enabled();
	public boolean isLocalV4Enabled(String orgName);

	public byte getDirectedOrderResponseMaxStringLength();
	public boolean ShowRejectReason(String fiOrg);

	/**
	 *
	 * @param customerOrg {@link Organization} of the customer
	 * @return integer mask for {@link com.integral.finance.dealing.Quote#getProvisioningFlags()} for which {@link com.integral.is.common.util.ISUtilImpl#UNSUPPORTED_QUOTE_CLSF}
	 * {@link com.integral.finance.dealing.Quote}s will be treated as Active on order book
	 * Such quotes are eligible to trigger a Stop order
	 */
	public int getZeroLiquidityActiveQuotesInOrderBookFlags(Organization customerOrg);

	/**
	 * @return Notification Mode for User's Login Channel
	 * 0 : Use the handler on the request only
	 * 1 : Send to handler on the request and login channel based handler if different
	 * 2 : Only send to the login channel based handler
	 */
	int getLoginChannelNotificationMode();
	public boolean isExtDestinationOrderAmendEnabled(String orgShortName);

	boolean isLinkedOCOCancellationEnabled(String userOrgShortName);

    /**
	 * Returns true if undisclosed provider match is disabled for the given order type
	 * @param org organization
	 * @param orderType order type
	 * @return true if undisclosed provider match is disabled for the given order type
	 */
	boolean isUndisclosedProviderMatchDisabled(Organization org, String orderType);

	public boolean isVenueReRoutingOrdersEnabled( String orgShortName, String ccyPair );
}
