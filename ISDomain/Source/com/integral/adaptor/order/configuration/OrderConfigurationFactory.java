package com.integral.adaptor.order.configuration;

import com.integral.util.Factory;

/**
 * This class is used as a factory to create the order configuration classes
 * used for order management system configuration.
 */
public class OrderConfigurationFactory extends Factory
{
	protected static OrderConfigurationFactory current;

	static
	{
		current = new OrderConfigurationFactory();
	}

	public static OrderConfigurationMBean getOrderConfigurationMBean()
	{
		return OrderConfiguration.getInstance();
	}
}
