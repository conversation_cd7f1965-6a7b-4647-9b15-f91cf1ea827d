package com.integral.adaptor.order.subscription;

import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.exception.IdcException;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Request;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.service.AutoSubscriptionService;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.user.Organization;
import com.integral.user.User;

import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 * User: divakaranr
 * Date: 3/7/13
 * Time: 1:38 PM
 * To change this template use File | Settings | File Templates.
 */
public class OutrightDisplayOrderSubscription
{
    private static final Log LOG = LogFactory.getLog(OutrightDisplayOrderSubscription.class);
    public static void subscribe()
    {
        

        LOG.info("going to send ESP subscription request to all display order providers");
        try
        {
            String providersList = ISFactory.getInstance().getServicProvidersMBean().getProvidersList();
            Collection<Organization> fisOnThisVS = OrderConfiguration.getInstance().getExtendedOrderProviderOrgsList();
            Set<String> configuredProvidersSet = constructProviderNameSet(fisOnThisVS);
            String[] providerArray = providersList.split(",");
            for (String providerName : providerArray)
            {
                LOG.info("going to send ESP subscription request to the provider: " + providerName);
                Organization providerOrg = ISUtilImpl.getInstance().getOrg( providerName );
                if(providerOrg != null && providerOrg.isDisplayOrderProvider() && ISFactory.getInstance().getISMBean().isOutrightDisplayOrderEnabled(providerName))
                {
                    Collection<Organization> relatedOrganizations = providerOrg.getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
                    Organization org = null;
                    Collection<CurrencyPair> currencyPairs = null;
                    User subscriptionUser = null;
                    for(Organization relatedOrg : relatedOrganizations)
                    {
                        if(configuredProvidersSet.contains(relatedOrg.getName()))
                        {
                            subscriptionUser = ISUtilImpl.getInstance().getSystemSubscriptionUser(relatedOrg);
                            currencyPairs = AutoSubscriptionService.getInstance().getCurrencyPairs(subscriptionUser.getOrganization(), providerOrg);
                            if(currencyPairs != null)
                            {
                                org = relatedOrg;
                                break;
                            }
                        }
                    }
                    if(org == null)
                    {
                        LOG.warn("no eligible user to subscribe for Outright Display order for the provider: " + providerName);
                        continue;
                    }
                    ISUtilImpl.getInstance().setSessionContext( subscriptionUser );
                    Iterator<CurrencyPair> iterator = currencyPairs.iterator();
                    while (iterator.hasNext())
                    {
                        CurrencyPair currencyPair = iterator.next();
                        MessageHandler messageHandler = new MessageHandler() {
                            @Override
                            public Message handle(Message message) throws IdcException {
                                return null;
                            }
                        };
                        WorkflowMessage responseMessage = subscribeCurrencyPairRates(subscriptionUser, providerOrg, currencyPair, false, messageHandler);
                        if ( responseMessage != null && responseMessage.getStatus().equals( MessageStatus.SUCCESS ) )
                        {
                            LOG.info("ESP subscription request sent to the display order provider: " + providerName);
                            break;
                        }
                    }
                }
                LOG.warn("Could not sent ESP subscription to the display order provider: " + providerName);
            }
            LOG.info("ESP subscription request sent to all display order providers");
        }catch (Exception e)
        {
            LOG.error("could not do the subscription for Outright Display Order Market Rates. ", e);
        }
    }

    private static WorkflowMessage subscribeCurrencyPairRates( User user, Organization providerOrg, CurrencyPair ccyPair, boolean populateQuote, MessageHandler handler )
    {
        WorkflowMessage msg = AutoSubscriptionService.getInstance().getSubscriptionWorkflowMessage(user, ccyPair, providerOrg, populateQuote, handler);
        Request request = (Request)msg.getObject();
        request.getRequestAttributes().setListenerRegistrationForDisplayOrder(true);
        WorkflowMessage respMsg = ( WorkflowMessage ) ServiceFactory.getISRequestService().process( msg ).getReplyMessage();
        if ( respMsg == null || !respMsg.getStatus().equals( MessageStatus.SUCCESS ) )
        {
            StringBuffer error = ISUtilImpl.getInstance().getErrorMessage( respMsg.getErrors() );
            LOG.warn(new StringBuilder("OutrightDisplayOrderSubscription : Subscription failed for user=")
                    .append(user.getFullName()).append(",provider=").append(providerOrg).append(",reason=").append(error.toString()).toString());
        }
        return respMsg;
    }

    private static Set<String> constructProviderNameSet(Collection<Organization> orgs)
    {
        Set<String> nameSet = new HashSet<String>();
        for(Organization org : orgs)
        {
            nameSet.add(org.getName());
        }
        return nameSet;
    }
}
