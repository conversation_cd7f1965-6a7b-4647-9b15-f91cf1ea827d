package com.integral.adaptor.manager;

import com.integral.adaptor.login.AdaptorLoginManagerC;
import com.integral.imtp.session.IMTPSession;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import javax.jms.QueueSession;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * This singleton class caches the BrokerOrderRequest messages coming to the BrokerAdaptor
 * and is used to forward the BrokerOrderResponse messages to the appropriate OrderAdaptor.
 */
public class BrokerAdaptorTradeManager
{
    /**
     * Logger.
     */
    private static final Log log = LogFactory.getLog(BrokerAdaptorTradeManager.class);

    /**
     * Singleton instance of this class.
     */
    private static volatile BrokerAdaptorTradeManager instance;

    /**
     * Mapping from the trade id to the corresponding BrokerOrderRequest.
     */
    private final ConcurrentMap<String, BrokerOrderRequest> tradeRequestMap =
        new ConcurrentHashMap<String, BrokerOrderRequest>(15);

    /**
     * Map from virtual server name to the corresponding QueueSession.
     */
    private final ConcurrentHashMap<String, QueueSession> sessionsMap = new ConcurrentHashMap<String, QueueSession>();

    /**
     * Constructor
     */
    private BrokerAdaptorTradeManager()
    {
    }

    /**
     * Returns the singleton instance of BrokerAdaptorTradeManager.
     */
    public static BrokerAdaptorTradeManager getInstance()
    {
        if (instance == null) {
            synchronized (AdaptorTradeManager.class) {
                if (instance == null) {
                    instance = new BrokerAdaptorTradeManager();
                }
            }
        }

        return instance;
    }

    /**
     * Adds the given BrokerOrderRequest object to the cache.
     */
    public void addTradeRequest(BrokerOrderRequest request)
    {
        String tradeId = request.getTradeId();
        tradeRequestMap.put(tradeId, request);
    }

    public BrokerOrderRequest getTradeRequest(String tradeId)
    {
        return tradeRequestMap.get(tradeId);
    }

    public void removeTrade(String tradeId)
    {
        tradeRequestMap.remove(tradeId);
    }

    public String getServerID(String tradeId)
    {
        BrokerOrderRequest request = tradeRequestMap.get(tradeId);
        if (request != null) {
            return request.getServerId();
        }

        return null;
    }

    public String getBrokerId(String tradeId)
    {
        String serverId = getServerID(tradeId);

        if (serverId != null) {
            return AdaptorLoginManagerC.getInstance().getBroker(serverId);
        }

        return null;
    }

    /**
     * Returns the queue name to which the BrokerOrderResponse with the given trade id should be sent.
     */
    public String getTradeDestination(String tradeId)
    {
        return AdaptorLoginManagerC.getInstance().getTradeQueue((tradeRequestMap.get(tradeId)).getServerId());
    }

    /**
     * Returns the IMTPSession to which the BrokerOrderResponse with the given trade id should be sent.
     */
    public IMTPSession getTradeDestinationSession(String tradeId)
    {
		if (tradeId != null) {
			BrokerOrderRequest request = tradeRequestMap.get(tradeId);
			if (request != null) {
				return AdaptorLoginManagerC.getInstance().getLoggedInIMTPSession(request.getServerId());
			}
		}
        return null;
    }
}