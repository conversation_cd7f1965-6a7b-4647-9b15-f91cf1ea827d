package com.integral.adaptor.log;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.integral.is.message.MarketRate;
import com.integral.is.message.MarketRateDelete;
import com.integral.log.LogC;
import com.integral.time.IdcDate;


public class RateLogger
{
    private static com.integral.log.Log log = new LogC( "com.integral.adaptor.log.rates" , null);
    private static final char SPACE = ' ';
    private static final String INACTIVE_STATUS = "InActive";
    private static final String ACTIVE_STATUS = "Active";
    private static final String TIMEZONE = "GMT";
    private static final String TIER = "TIER";

    public static void logRate( MarketRate isMessage )
    {
        logRate( isMessage, new SimpleDateFormat( "yyyyMMdd HH:mm:ss:SSS" ) );
    }

    public static void logRate( MarketRate isMessage, SimpleDateFormat sdf )
    {
        try
        {
            int bidTierSize = isMessage.getBidTierSize();
            int offerTierSize = isMessage.getOfferTierSize();
            int maxTiers = bidTierSize > offerTierSize ? bidTierSize : offerTierSize;
			double bidRate, bidLimit, offerRate, offerLimit, bidSpotRate, offerSpotRate, bidFwdPoints, offerFwdPoints;
			String bidFixingDate, offerFixingDate;
//            Timing timing = isMessage.getTiming();
            String receivedTime = "null";
            String rateEffectiveTime = "null";
//            Date receivedDate = timing.getTime( ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE );
            Date receivedDate = null;
            if ( isMessage.getRateReceivedByAdapter() > 0 )
            {
                receivedDate = new Date( isMessage.getRateReceivedByAdapter() );
            }
//            Date rateEffectiveDate = timing.getTime( ISCommonConstants.EVENT_TIME_RATE_EFFECTIVE );
            Date rateEffectiveDate = null;
            if ( isMessage.getRateEffective() > 0 )
            {
                rateEffectiveDate = new Date( isMessage.getRateEffective() );
            }

            if ( receivedDate != null )
            {
                receivedTime = sdf.format( receivedDate );
            }
            if ( rateEffectiveDate != null )
            {
                rateEffectiveTime = sdf.format( rateEffectiveDate );
            }
            for ( int i = 0; i < maxTiers; i++ )
            {
                bidRate = 0.0;
                bidLimit = 0.0;
                offerRate = 0.0;
                offerLimit = 0.0;
				bidSpotRate = 0.0;
                offerSpotRate = 0.0;
				bidFwdPoints = 0.0;
				offerFwdPoints = 0.0;
                bidFixingDate = "null";
    			offerFixingDate = "null";
                boolean tierIsStale = false;
                if ( i < bidTierSize )
                {
                    bidRate = isMessage.getBidRate( i );
                    bidLimit = isMessage.getBidLimit( i );
                    bidSpotRate = isMessage.getBidSpotRate(i);
                    bidFwdPoints = isMessage.getBidForwardPoints(i);
                    if(isMessage.getBidFixingDate(i)!=null)
                    {
                    	  bidFixingDate = isMessage.getBidFixingDate().getFormattedDate(IdcDate.DD_MM_YYYY_HYPHEN);
                    }
                  
                }
                if ( i < offerTierSize )
                {
                    offerRate = isMessage.getOfferRate( i );
                    offerLimit = isMessage.getOfferLimit( i );
                    offerSpotRate = isMessage.getOfferSpotRate(i);
                    offerFwdPoints = isMessage.getOfferForwardPoints(i);
                    if(isMessage.getOfferFixingDate(i)!=null)
                    {
                    	  offerFixingDate = isMessage.getOfferFixingDate().getFormattedDate(IdcDate.DD_MM_YYYY_HYPHEN);
                    }
                }
                if ( bidLimit == 0.0 && offerLimit == 0.0 )
                {
                    tierIsStale = true;
                }
                StringBuilder buffer = new StringBuilder( 300 );
                buffer.append( receivedTime ).append( SPACE );
                buffer.append( rateEffectiveTime ).append( SPACE );
                buffer.append( isMessage.getProviderShortName() ).append( SPACE );
                buffer.append( isMessage.getBaseCcy() ).append( SPACE );
                buffer.append( isMessage.getVariableCcy() ).append( SPACE );
                buffer.append( bidRate ).append( SPACE );
                buffer.append( offerRate ).append( SPACE );
                buffer.append( bidLimit ).append( SPACE );
                buffer.append( offerLimit ).append( SPACE );
                buffer.append( bidSpotRate ).append( SPACE );
                buffer.append( offerSpotRate ).append( SPACE );
                buffer.append( bidFwdPoints ).append( SPACE );
                buffer.append( offerFwdPoints ).append( SPACE );
                buffer.append( bidFixingDate ).append( SPACE );
                buffer.append( offerFixingDate ).append( SPACE );
                if ( isMessage instanceof MarketRateDelete )
                {
                    buffer.append( INACTIVE_STATUS ).append( SPACE );
                }
                else
                {
                    if ( isMessage.isStale() )
                    {
                        buffer.append( INACTIVE_STATUS ).append( SPACE );
                    }
                    else if ( tierIsStale )
                    {
                        buffer.append( INACTIVE_STATUS ).append( SPACE );
                    }
                    else
                    {
                        buffer.append( ACTIVE_STATUS ).append( SPACE );
                    }
                }
                buffer.append( TIMEZONE ).append( SPACE );
                buffer.append( isMessage.getQuoteId() ).append( SPACE );
                buffer.append( isMessage.getProviderQuoteId( i ) ).append( SPACE );
                IdcDate valDate = isMessage.getValueDate();
                buffer.append( valDate != null ? valDate.getFormattedDate( IdcDate.DD_MMM_YYYY_HYPHEN ) : null ).append( SPACE );
                buffer.append( TIER ).append( SPACE ).append( i + 1 ).append( SPACE );
                buffer.append( isMessage.getStreamId() ).append( SPACE );
                log.warn( buffer.toString() );
            }
        }
        catch ( Exception e )
        {
            log.warn( "RatesLogger.logMessage: Exception in logging rate ", e );
        }
    }


}
