package com.integral.adaptor.log;

import com.integral.pipeline.metrics.MetricsManager;
import com.integral.pipeline.metrics.MetricsProxy;
import com.integral.pipeline.metrics.VolatileMetrics;

import java.util.Iterator;

/**
* This will generate rate sending metrics on adaptors (Broker and provider)
 * Rate metrics will display statistics for provider - stream - currency pair - rate processing latency etc
 */
public class ProviderRateMetrics
{
      public static class RateMetric implements MetricsProxy
      {

          static final int MAX_PROCESSING_TIME=200; //ms
          static final int BUCKET_INTERVAL = 10; //ms
          static final int NUM_INTERVALS = MAX_PROCESSING_TIME/BUCKET_INTERVAL;

          static final int PROVIDER_SEND_TIME=1000; //ms
          static final int PROVIDER_BUCKET_INTERVAL = 50; //ms
          static final int PROVIDER_NUM_INTERVALS = MAX_PROCESSING_TIME/BUCKET_INTERVAL;

          //static values
          public String providerName;
          public String streamName;
          public int port;

          //dynamic values
          public long numRatesSent;
          public long totalProcessingTime;
          public long totalProviderDelayTime;
          //JMS only =1, Multicast only=2, JMS & Multiicast=3
          public int maxPacketSize;
          //public long numRatesReceived;

          int [] processingTimeCounter = new int [ NUM_INTERVALS +1 ];
          int [] providerDelayCounter = new int [ PROVIDER_NUM_INTERVALS +1 ];
          
          public void addProcessingTime ( long processingTime )
          {
              if(processingTime<=0)
              {
                    processingTimeCounter[0]++;
              }
              else if(processingTime>MAX_PROCESSING_TIME)
              {
                    processingTimeCounter[processingTimeCounter.length-1]++;
              }
              else
              {
                    processingTimeCounter[ (int)processingTime/NUM_INTERVALS ]++;
              }
              totalProcessingTime+=processingTime;
          }

          public void addProviderDelay( long providerDelayTime )
          {
              if(providerDelayTime<=0)
              {
                  providerDelayCounter[0]++;
              }
              else if(providerDelayTime>MAX_PROCESSING_TIME)
              {
                  providerDelayCounter[providerDelayCounter.length-1]++;
              }
              else
              {
                    providerDelayCounter[ (int)providerDelayTime/PROVIDER_NUM_INTERVALS ]++;
              }
              totalProviderDelayTime+=providerDelayTime;
          }


          public void swapMetrics()
          {
              //volatileRateMetrics.vNumRatesReceived = numRatesReceived;
              volatileRateMetrics.vnumRatesSent = numRatesSent;
              volatileRateMetrics.vtotalProcessingTime = totalProcessingTime;
              volatileRateMetrics.vMaxPacketSize = maxPacketSize;
              for (int i=0; i< processingTimeCounter.length-1 ; i++)
              {
                  volatileRateMetrics.vProcessingTimeCounter[i]= processingTimeCounter[i];
                  processingTimeCounter[i]=0;
              }
              for (int j=0; j< providerDelayCounter.length-1 ; j++)
              {
                  volatileRateMetrics.vProviderDelayTime[j]= providerDelayCounter[j];
                  providerDelayCounter[j]=0;
              }
              //numRatesReceived=0;
              numRatesSent=0;
              totalProcessingTime=0;
              maxPacketSize =0;
          }

          VolatileRateMetrics volatileRateMetrics = new VolatileRateMetrics();

          public VolatileMetrics getMetrics()
          {
              return volatileRateMetrics;
          }

          public class VolatileRateMetrics implements VolatileMetrics
          {
              //volatile long vNumRatesReceived;
              volatile long vnumRatesSent;
              long vtotalProcessingTime;
              long vTotalProviderDelayTime;
              int vMaxPacketSize;
              int [] vProcessingTimeCounter = new int [ NUM_INTERVALS +1 ];
              int [] vProviderDelayTime = new int [ PROVIDER_NUM_INTERVALS +1 ];

              public StringBuilder report()
              {
				boolean isDebugEnabled = MetricsManager.metricsLog.isDebugEnabled();
                  StringBuilder sb = new StringBuilder(256);
                  sb.append("ProvMet:").append(" PR=").append(providerName).append(", strm=").append(streamName)
                          .append(", p=").append(port).append(", RtSnt=").append( vnumRatesSent )
                          .append(", PrcTm=").append(vtotalProcessingTime)
                          .append(", PT: [ ");
                  for (int i=0; i<vProcessingTimeCounter.length ; i++)
                  {
                      sb.append(vProcessingTimeCounter[i]).append( ", " );
                  }
                  sb.append(" ]").append(", PrvDly=").append(vTotalProviderDelayTime).append(", PD: [ ");
                  for (int i=0; i<vProviderDelayTime.length ; i++)
                  {
                      sb.append(vProviderDelayTime[i]).append( ", " );
                  }
                  sb.append(" ]");

                  if( isDebugEnabled )
                  {
                      MetricsManager.metricsLog.debug(sb.toString());
                  }
                  else
                  {
                      MetricsManager.metricsLog.info(sb.toString());
                  }


                  vnumRatesSent=0;
                  vtotalProcessingTime=0;
                  vTotalProviderDelayTime=0;
                  
                  return sb;
              }
          }
      }

}
