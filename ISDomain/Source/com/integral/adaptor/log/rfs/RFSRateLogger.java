package com.integral.adaptor.log.rfs;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.integral.is.ISCommonConstants;
import com.integral.is.message.Timing;
import com.integral.is.message.rfs.RFSFXLeg;
import com.integral.is.message.rfs.RFSFXRate;
import com.integral.is.message.rfs.RFSMarketRate;
import com.integral.log.LogFactory;

/**
 *
 * <AUTHOR>
 * class for logging RFS Rates
 */
public class RFSRateLogger
{

	private static com.integral.log.Log log = LogFactory.getLog("com.integral.adaptor.log.rfs.rates");
	private static final String SPACE = " ";
	private static final String RATE = "Rate";
	private static final String SPOTRATE = "SpotRate";
	private static final String FORWARDPOINTS = "ForwardPoints";
	private static final String NEARLEG = "NL";
	private static final String FARLEG = "FL";
	private static final String BID = "Bid";
	private static final String OFFER = "Offer";
	private static final String DEALTCUREENCY = "DealtCurrency";
	private static final String TENOR = "Tenor";
	private static final String VALUEDATE = "ValueDate";
	private static final String LIMIT = "Limit";
	private static final String INACTIVE_STATUS = "InActive";
	private static final String ACTIVE_STATUS = "Active";
    private static final String MID = "Mid";
	
	
	public static final String NULL = "null";

	static
	{
		StringBuffer buffer = new StringBuffer("RateReceivedTime");
		buffer.append(SPACE).append("RateEffectiveTime").append(SPACE).append("RequestId").append(SPACE).append("QuoteId").append(SPACE).
		append("Status").append(SPACE).append("BaseCcy").append(SPACE).append("VarCcy").append(SPACE).
		append(NEARLEG + BID + RATE).append(SPACE).append(NEARLEG + BID + SPOTRATE).append(SPACE).append(NEARLEG + BID + FORWARDPOINTS).append(SPACE).
		append(NEARLEG + OFFER + RATE).append(SPACE).append(NEARLEG + OFFER + SPOTRATE).append(SPACE).append(NEARLEG + OFFER + FORWARDPOINTS).append(SPACE).
		append(NEARLEG + BID + LIMIT).append(SPACE).append(NEARLEG + OFFER + LIMIT).append(SPACE).append(NEARLEG + TENOR).append(SPACE).append(NEARLEG + VALUEDATE).append(SPACE).append(NEARLEG + DEALTCUREENCY).append(SPACE).
		append(FARLEG + BID + RATE).append(SPACE).append(FARLEG + BID + SPOTRATE).append(SPACE).append(FARLEG + BID + FORWARDPOINTS).append(SPACE).
		append(FARLEG + OFFER + RATE).append(SPACE).append(FARLEG + OFFER + SPOTRATE).append(SPACE).append(FARLEG + OFFER + FORWARDPOINTS).append(SPACE).
		append(FARLEG + BID + LIMIT).append(SPACE).append(FARLEG + OFFER + LIMIT).append(SPACE).append(FARLEG + TENOR).append(SPACE).append(FARLEG + VALUEDATE).append(SPACE).append(FARLEG + DEALTCUREENCY).append(SPACE).
		append("TimeToLive").append(SPACE).append("ProviderQuoteId").append(SPACE).append("Provider").append(SPACE).append("StreamId").append(SPACE).
		append("Classification").append(SPACE).append("FixingTenor").append(SPACE).append("FixingDate").
        append(SPACE).append("NDFPriority").append(SPACE).
        
        append(NEARLEG+MID+RATE).append(SPACE).append(NEARLEG+MID+SPOTRATE).append(SPACE).append(NEARLEG+MID+FORWARDPOINTS).append(SPACE).
    	append(FARLEG+MID+RATE).append(SPACE).append(FARLEG+MID+SPOTRATE).append(SPACE).append(FARLEG+MID+FORWARDPOINTS);

		log.warn(buffer.toString());
	}

	public static void logRate(RFSMarketRate isMessage)
	{
		if ( isMessage != null )
		{
			try
			{
				StringBuffer buffer = new StringBuffer();
				Timing timing = isMessage.getTiming();

				buffer.append(formatDate(timing.getTime(ISCommonConstants.EVENT_TIME_DISP_ADAPTER_REC_RATE), "yyyyMMdd HH:mm:ss:SSS")).append(SPACE);
				buffer.append(formatDate(timing.getTime(ISCommonConstants.EVENT_TIME_RATE_EFFECTIVE), "yyyyMMdd HH:mm:ss:SSS")).append(SPACE);
				buffer.append(isMessage.getRequestId()).append(SPACE);
				buffer.append(isMessage.getQuoteId()).append(SPACE);
				buffer.append(isMessage.isStale() == true ? INACTIVE_STATUS : ACTIVE_STATUS).append(SPACE);
				if(isMessage.isStale()) {
					buffer.append(isMessage.getQuoteStaleReason()).append(SPACE);
				}
				buffer.append(isMessage.getBaseCurrency()).append(SPACE);
				buffer.append(isMessage.getVariableCurrency()).append(SPACE);
				//nearleg related
				RFSFXLeg nearLeg = isMessage.getNearLeg();
				if ( nearLeg != null )
				{
					RFSFXRate bidRate = nearLeg.getBidRate();
					RFSFXRate offerRate = nearLeg.getOfferRate();
					if ( bidRate != null )
					{
						buffer.append(bidRate.getRate()).append(SPACE);
						buffer.append(bidRate.getSpotRate()).append(SPACE);
						buffer.append(bidRate.getForwardPoints()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
					}
					if ( offerRate != null )
					{
						buffer.append(offerRate.getRate()).append(SPACE);
						buffer.append(offerRate.getSpotRate()).append(SPACE);
						buffer.append(offerRate.getForwardPoints()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
					}

					if ( bidRate != null )
					{
						buffer.append(bidRate.getDealtAmount()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
					}
					if ( offerRate != null )
					{
						buffer.append(offerRate.getDealtAmount()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
					}

					buffer.append(nearLeg.getTenor()).append(SPACE);
					buffer.append(nearLeg.getValueDate()).append(SPACE);
					buffer.append(nearLeg.getDealtCurrency()).append(SPACE);
				}
				else
				{
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
				}

				//farleg related
				RFSFXLeg farLeg = isMessage.getFarLeg();
				if ( farLeg != null )
				{
					RFSFXRate bidRate = farLeg.getBidRate();
					RFSFXRate offerRate = farLeg.getOfferRate();
					if ( bidRate != null )
					{
						buffer.append(bidRate.getRate()).append(SPACE);
						buffer.append(bidRate.getSpotRate()).append(SPACE);
						buffer.append(bidRate.getForwardPoints()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
					}
					if ( offerRate != null )
					{
						buffer.append(offerRate.getRate()).append(SPACE);
						buffer.append(offerRate.getSpotRate()).append(SPACE);
						buffer.append(offerRate.getForwardPoints()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
						buffer.append(NULL).append(SPACE);
					}

					if ( bidRate != null )
					{
						buffer.append(bidRate.getDealtAmount()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
					}
					if ( offerRate != null )
					{
						buffer.append(offerRate.getDealtAmount()).append(SPACE);
					}
					else
					{
						buffer.append(NULL).append(SPACE);
					}

					buffer.append(farLeg.getTenor()).append(SPACE);
					buffer.append(farLeg.getValueDate()).append(SPACE);
					buffer.append(farLeg.getDealtCurrency()).append(SPACE);
				}
				else
				{
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
					buffer.append(NULL).append(SPACE);
				}

				buffer.append(isMessage.getTimeToLiveInSeconds()).append(SPACE);
				buffer.append(isMessage.getProviderQuoteId()).append(SPACE);
				buffer.append(isMessage.getProviderShortName()).append(SPACE);
				buffer.append(isMessage.getStreamId()).append(SPACE);
				buffer.append(isMessage.getTradeClassification()).append(SPACE);
	            //ndf addition
	            if (nearLeg!=null){
	            	 buffer.append(nearLeg.getFixingTenor()).append(SPACE);
	     			 buffer.append(nearLeg.getFixingDate()).append(SPACE);
	            }
	            buffer.append(isMessage.getNDFPriority()).append(SPACE);
	            
	            
	            RFSFXRate nearMidRate = null;
	            if (nearLeg!=null)
	            {
	            	nearMidRate = nearLeg.getMidRate();
	            }
	            
    			if ( nearMidRate!=null )
    			{
    				buffer.append(nearMidRate.getRate()).append(SPACE);
    				buffer.append(nearMidRate.getSpotRate()).append(SPACE);
    				buffer.append(nearMidRate.getForwardPoints()).append(SPACE);
    			}
    			else
    			{
    				buffer.append(NULL).append(SPACE);
    				buffer.append(NULL).append(SPACE);
    				buffer.append(NULL).append(SPACE);
    			}
	            
	            RFSFXRate farMidRate = null;
	            if (farLeg!=null)
	            {
	            	farMidRate = farLeg.getMidRate();
	            }
	            
    			if ( farMidRate!=null )
    			{
    				buffer.append(farMidRate.getRate()).append(SPACE);
    				buffer.append(farMidRate.getSpotRate()).append(SPACE);
    				buffer.append(farMidRate.getForwardPoints()).append(SPACE);
    			}
    			else
    			{
    				buffer.append(NULL).append(SPACE);
    				buffer.append(NULL).append(SPACE);
    				buffer.append(NULL).append(SPACE);
    			}
    			
    			
				log.warn(buffer.toString());

			}
			catch ( Exception e )
			{
				log.warn("RFSRateLogger.logMessage: Exception in logging rate ", e);
			}
		}
	}

	private static String formatDate(Date inputDate, String format)
	{
		try
		{
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
			return simpleDateFormat.format(inputDate);
		}
		catch ( Exception e )
		{
			return null;
		}

	}

}
