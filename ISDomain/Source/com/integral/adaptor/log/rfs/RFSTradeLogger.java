package com.integral.adaptor.log.rfs;

import com.integral.is.message.rfs.RFSTradeReject;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.is.message.rfs.RFSTradeResponse;
import com.integral.is.message.rfs.RFSTradeVerify;
import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 *
 * <AUTHOR>
 * class for logging RFS Trade Requests and RFS Trade Responses
 *
 */

public class RFSTradeLogger
{

	private static Log log = LogFactory.getLog("com.integral.adaptor.log.rfs.orders");

	public static void logTradeRequest(RFSTradeRequest req)
	{
		log.warn("RFSTradeLogger.logRFSTradeRequest :" + req.toString());
	}

	public static void logTradeVerify(RFSTradeVerify message)
	{
		log.warn("RFSTradeLogger.logRFSTradeVerify :" + message.toString());
	}

	public static void logTradeReject(RFSTradeReject message)
	{
		log.warn("RFSTradeLogger.logRFSTradeReject :" + message.toString());
	}

	public static void logTradeResponse(RFSTradeResponse response)
	{
		if ( response instanceof RFSTradeVerify )
		{
			logTradeVerify((RFSTradeVerify) response);
		}
		else if ( response instanceof RFSTradeReject )
		{
			logTradeReject((RFSTradeReject) response);
		}
	}
}
