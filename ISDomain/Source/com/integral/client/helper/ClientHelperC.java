package com.integral.client.helper;

import com.integral.finance.dealing.RequestService;
import com.integral.is.common.client.handler.ISHandlerFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageHandler;
import com.integral.message.WorkflowMessage;
import com.integral.util.StopWatchC;

import java.rmi.RemoteException;


/**
 * ClientHelperC is a helper class to getRequestService,setMessageHandler
 *
 * <AUTHOR> Development Corp.
 */
public class ClientHelperC {

    protected Log log = LogFactory.getLog(this.getClass());

    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();

    /**
     * returns RequestService
     */
    public static RequestService getRequestService(WorkflowMessage wfMsg) throws RemoteException {

        if (isMBean.getServiceNameQualifier() != null && !"".equals(isMBean.getServiceNameQualifier())) {
            return ServiceFactory.getOrderRequestService();
        }
        return ServiceFactory.getISRequestService();
    }

    /**
     *  cleans the client session
     */
    public static void logout(WorkflowMessage msg) {
    }

    /**
     * Set the message handler for the workflow message.
     * This assumes that the webApp and httpSession are stored
     * as properties on the workflow message.
     */
    public void setMessageHandler(WorkflowMessage msg) {
        StopWatchC watch = null;
        if (LogFactory.isTimingEnabled(ClientHelperC.class)) {
            watch = LogFactory.getStopWatch(ClientHelperC.class, "setMessageHandler");
        }
        if (watch != null) watch.start();
        MessageHandler clientHandler = ISHandlerFactory.getHandler(msg);
        msg.setParameterValue("messageHandler", clientHandler);
        if (watch != null) watch.stopAndLog();
    }


}
