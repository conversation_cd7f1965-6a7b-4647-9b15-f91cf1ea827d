package com.integral.client.helper;

import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.dealing.RequestService;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.client.handler.ISHandlerFactory;
import com.integral.is.common.client.handler.ISRateHandler;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.finance.dealing.ServiceFactory;
import com.integral.is.system.notification.NotificationMessageSender;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageFactory;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.time.IdcDate;
import com.integral.user.User;
import com.integral.util.StopWatchC;
import com.integral.xml.mapping.XMLException;

import java.io.StringWriter;
import java.rmi.RemoteException;


/**
 * ClientHelperCTemp is a helper class to getRequestService,setMessageHandler
 *
 * <AUTHOR> Development Corp.
 */
public class ClientHelperCTemp
{

    protected Log log = LogFactory.getLog( this.getClass() );

    protected static String channelName = "ClientSDK";
    protected static IdcSessionManager sessionManager = IdcSessionManager.getInstance();

    private static ServerRuntimeMBean sMbean = RuntimeFactory.getServerRuntimeMBean();
    private static ISMBean isMBean = ISFactory.getInstance().getISMBean();


    /**
     * returns RequestService
     */
    public static RequestService getRS( WorkflowMessage wfMsg ) throws RemoteException
    {
        if ( sMbean.isServerWarmingUp() )
        {
            return ServiceFactory.getISRequestService();
        }

/*        if (wfMsg != null && wfMsg.getObject() != null && wfMsg.getObject() instanceof Request) {
            if ("ESP".equalsIgnoreCase(tradeType))
                return ServiceFactory.getISRequestService();
            else
                return ServiceFactory.getOrderRequestService();
        }
        return ServiceFactory.getISRequestService();
        */
        if ( isMBean.getServiceNameQualifier() != null && !"".equals( isMBean.getServiceNameQualifier() ) )
        {//If OA then send all then use OrderRequestService 
                return ServiceFactory.getOrderRequestService();
        }
        return ServiceFactory.getISRequestService();

    }

    /**
     * cleans the client session
     */
    public static void logout( WorkflowMessage msg )
    {
        User user = msg.getSender();
        IdcSessionContext ctx = sessionManager.getSessionContext( user );
        ctx.removeAttribute( "isFXISession" );
        ctx.removeAttribute( "isClientSession" );

    }

    /**
     * Set the message handler for the workflow message.
     * This assumes that the webApp and httpSession are stored
     * as properties on the workflow message.
     */
    public static void setMessageHandler( WorkflowMessage msg )
    {
        StopWatchC watch = null;
        if ( LogFactory.isTimingEnabled( ClientHelperCTemp.class ) )
        {
            watch = LogFactory.getStopWatch( ClientHelperCTemp.class, "setMessageHandler" );
        }
        if ( watch != null )
        {
            watch.start();
        }
        if ( msg.getParameterValue( "messageHandler" ) == null )
        {
            LogFactory.getLog(ISRateHandler.class).info("messageHandler not set, creating new", new RuntimeException("TraceIgnoreIt")) ;
            MessageHandler clientHandler = ISHandlerFactory.getHandler( msg );
            msg.setParameterValue( "messageHandler", clientHandler );
        }
        if ( watch != null )
        {
            watch.stopAndLog();
        }
    }

    public void sendRollTimeNotification( WorkflowMessage msg )
    {
        WorkflowMessage wm = MessageFactory.newWorkflowMessage();
        wm.setEvent( ISConstantsC.MSG_EVT_ALERT );
        wm.setTopic( "TRADEDATE" );
        wm.setStatus( MessageStatus.SUCCESS );
        wm.setParameterValue( "TradeDate", EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate().getFormattedDate( IdcDate.YYYY_MM_DD_HYPHEN ) );
        String messageContent = getXML( wm );
        if ( log.isDebugEnabled() )
        {
            log.debug( "ClientHelperCTemp.sendRollTimeNotification::Sending roll time notification" );
        }
        NotificationMessageSender.sendMessageToAllUsers( messageContent, null );
    }

    /**
     * returns the xml of the workflowmessage object
     */
    private String getXML( Message wfMsgObj )
    {
        String xml = null;

        try
        {
            StringWriter out = new StringWriter();
            ISUtilImpl.xmlConvertor.convertToXML( out, wfMsgObj, "IntegrationServer" );
            xml = out.toString();
            return xml;
        }
        catch ( XMLException xe )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "ClientHelperCTemp.getXML: Caught XMLException in getXML. Message is - " + xe.getDetailMessage() );
            }
        }
        catch ( Exception e )
        {
            if ( log.isDebugEnabled() )
            {
                log.debug( "ClientHelperCTemp.getXML: Caught exception in getXML.", e );
            }
        }

        return xml;
    }


}
