package com.integral.client.util;

import com.integral.message.WorkflowMessage;
import com.integral.is.common.comm.http.HttpHandlerC;
import com.integral.is.common.comm.http.HttpsHandlerC;
import com.integral.is.common.comm.http.HttpHandler;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ClientConfMBean;
import com.integral.is.ISCommonConstants;
import com.integral.log.Log;
import com.integral.log.LogFactory;

import java.net.Authenticator;
import java.net.PasswordAuthentication;

/**
 * Created by IntelliJ IDEA.
 * User: tiwary
 * Date: Jun 13, 2008
 * Time: 3:12:40 PM
 * To change this template use File | Settings | File Templates.
 */
public class NTDHelperC
{
	private Log log = LogFactory.getLog(this.getClass());
    private ClientConfMBean clientconfMBean = ISFactory.getInstance().getClientConfMBean();
	private static final String MANIFESTS_XML = "manifests.xml";
	private static final String VERSIONS_TXT = "Versions.txt";
	private static final NTDHelperC instance = new NTDHelperC();

	public static NTDHelperC getInstance()
	{
		return instance;
	}

	public void setNTDVersions(WorkflowMessage wfMsg, WorkflowMessage replyMsg)
	{
		String versionTxt = this.getNtdVersion(wfMsg);
		String clientVersion = getVersion(ISCommonConstants.CLIENT_VERSION, versionTxt);
		String brandVersion = getVersion(ISCommonConstants.BrandVersion,versionTxt);
		replyMsg.setParameterValue(ISCommonConstants.CLIENT_VERSION,clientVersion);
		replyMsg.setParameterValue(ISCommonConstants.BrandVersion,brandVersion);
	}

	private String getVersion(String versionName, String versionTxt)
	{
		String clientVersion = "";
		if(versionTxt != null && !versionTxt.trim().equals(""))
		{
			int ind1 = versionTxt.indexOf(versionName) + versionName.length();
			if(versionTxt.indexOf(versionName)  != -1)
			{
				versionTxt = versionTxt.substring(ind1 + 1);
				clientVersion = versionTxt.substring(0,versionTxt.indexOf('|'));
			}
		}
		return clientVersion;
	}

	private String getNtdVersion(WorkflowMessage wfMsg)
    {
		String responseStr =null;
		String url = retreiveVersionUrl(wfMsg);
		HttpHandler handler;
		log.warn( "NTDHelperC.getNtdVersion : url=" + url);
		if(url != null)
		{
			boolean isSecure = isSecureConnection(url);
			Authenticator auth = new MyAuthenticator();
			if(isSecure)
			{
				handler = new HttpsHandlerC(url,auth);
			}
			else
			{
				handler = new HttpHandlerC(url,auth);
			}
			StringBuffer params = new StringBuffer();
			log.warn("NTDHelperC.getNtdVersion : Sending following message ");
			log.warn(" URL : " + url );
			int status = 0;
			boolean timeoutEnabled = true;
			try{
				status = handler.execute(params.toString(),timeoutEnabled);
			}
			catch(Exception e)
			{
				log.error("NTDHelperC.getNtdVersion : Error in sending message ",e);
			}
			if(status != 200){
				log.warn("NTDHelperC.getNtdVersion : connection status " + status);
				return "";
			}
			responseStr = handler.getResponse();
			if(log.isDebugEnabled()){
				log.debug("NTDHelperC.getNtdVersion : status="+status+" responseStr="+responseStr);
			}
		}
		return responseStr;
    }

	private boolean isSecureConnection(String url)
	{
		if(url !=null && url.startsWith("https"))
		{
			if(log.isDebugEnabled())
			{
				log.debug("NTDHelperC.isSecureConnection : using https protocol");
			}
			return true;
		}
		else
		{
			if(log.isDebugEnabled())
			{
				log.debug("NTDHelperC.isSecureConnection : using http protocol");
			}
			return false;
		}
	}

	private String retreiveVersionUrl(WorkflowMessage wfMsg)
    {
		String updateURL;
		String versionUrl=null;
		String brandName = VersionCheckC.getBrand(wfMsg);
		if(brandName != null)
		{
			updateURL = clientconfMBean.getBrandUpdateURL(wfMsg.getSender(),brandName);
		}
		else
			updateURL = clientconfMBean.getFXIFullUpdURL(wfMsg.getSender());
		if(updateURL != null && updateURL.trim().length() > 0)
		{
			versionUrl = updateURL.replaceFirst(MANIFESTS_XML,VERSIONS_TXT);
		}
		return versionUrl;
    }

	class MyAuthenticator extends Authenticator
	{
		private String USER_NAME = clientconfMBean.getNTDServerUserName();
		private String PASSWORD = clientconfMBean.getNTDServerPassword();

		protected PasswordAuthentication getPasswordAuthentication()
		{
      		return new PasswordAuthentication (USER_NAME, PASSWORD.toCharArray());
		}
	}
}
