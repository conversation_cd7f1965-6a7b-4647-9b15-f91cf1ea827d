package com.integral.client.util;

import com.integral.cas.config.CASMBeanC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.model.portal.PortalApp;
import com.integral.model.portal.PortalAppAccessInterceptor;
import com.integral.model.portal.PortalAppVisibility;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.user.User;

public class PortalAccessInterceptorFXIClientV1 implements PortalAppAccessInterceptor {
    private static final String Permission_IntegralTraderEnabled = "EnableIntegralTrader";
    @Override
    public boolean isAppEnabled(PortalApp app) {
        IdcSessionContext sessionContext = IdcSessionManager.getInstance().getSessionContext();
        if( sessionContext == null ){
            return false;
        }
        User user = (User) sessionContext.getUser();
        if( user == null ) {
            return false;
        }
        if(ISFactory.getInstance().getClientConfMBean().isIntegralTraderAccessControlEnabled(user)){
            if(!user.hasPermission(Permission_IntegralTraderEnabled)){
                return false;
            }
        }
        String clientAppName = CASMBeanC.getInstance().getSSOClientAppName(user);
        String mappedClientAppName = ISFactory.getInstance().getClientConfMBean().getMappedClientAppName(app.getName());
        if( clientAppName != null && !clientAppName.trim().isEmpty()){
            return mappedClientAppName.startsWith(clientAppName);
        }
        else{
            String defaultPortalClientApp = ISFactory.getInstance().getClientConfMBean().getDefaultPortalClientAppName();
            if( defaultPortalClientApp.equals(app.getName())){
                return true;
            }
            else{
                return false;
            }
        }
    }

    @Override
    public PortalAppVisibility getPortalAppVisibility(PortalApp app) {
        IdcSessionContext sessionContext = IdcSessionManager.getInstance().getSessionContext();
        if( sessionContext == null ){
            return PortalAppVisibility.HIDE;
        }
        User user = (User) sessionContext.getUser();
        if( user == null ) {
            return PortalAppVisibility.HIDE;
        }
        if(ISFactory.getInstance().getClientConfMBean().isIntegralTraderAccessControlEnabled(user)){
            if(!user.hasPermission(Permission_IntegralTraderEnabled)){
                return PortalAppVisibility.HIDE;
            }
        }
        String clientAppName = CASMBeanC.getInstance().getSSOClientAppName(user);
        String mappedClientAppName = ISFactory.getInstance().getClientConfMBean().getMappedClientAppName(app.getName());
        if( clientAppName != null && !clientAppName.trim().isEmpty() ){
            /*
                Use startsWith instead of equals so that branded clients in portal can also be configured with access interceptor
                e.g. FXI8 maps to FXInside8 on portal
                Branded client app should be named FXInside8<BRAND> so that access interceptor works for FXInside8<BRAND> as well.
             */
            if( mappedClientAppName.startsWith(clientAppName)) {
                return PortalAppVisibility.SHOW;
            }
            else{
                return PortalAppVisibility.HIDE;
            }
        }
        else{
            String defaultPortalClientApp = ISFactory.getInstance().getClientConfMBean().getDefaultPortalClientAppName();
            if( defaultPortalClientApp.equals(app.getName())){
                return PortalAppVisibility.SHOW;
            }
        }
        return PortalAppVisibility.HIDE;
    }
}