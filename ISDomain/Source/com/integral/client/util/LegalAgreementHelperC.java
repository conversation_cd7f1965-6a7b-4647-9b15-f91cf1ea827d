package com.integral.client.util;

import com.integral.finance.counterparty.CounterpartyGroupC;
import com.integral.persistence.NamespaceC;
import com.integral.persistence.NamespaceGroupC;
import com.integral.user.*;
import org.eclipse.persistence.sessions.Session;
import com.integral.admin.AdminServiceC;
import com.integral.exception.IdcOptimisticLockException;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.ISEmailUtil;
import com.integral.is.common.util.ISTransactionManager;
import com.integral.is.audit.ISLegalAgreementAuditEventFacadeC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.WorkflowMessage;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.persistence.PersistenceException;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcTransaction;
import com.integral.audit.AuditFactory;
import com.integral.audit.AuditServiceFactory;
import com.integral.audit.AuditService;

import java.util.Vector;
import java.util.Date;

/**
 * Created by IntelliJ IDEA.
 * User: tiwary
 * Date: Apr 21, 2008
 * Time: 2:29:21 PM
 * contains LegalAgreement related changes
 */
public class LegalAgreementHelperC
{

    private static final LegalAgreementHelperC legalAgreementInstance = new LegalAgreementHelperC();

    private static final Log log = LogFactory.getLog( LegalAgreementHelperC.class );

    private static final String NOT_REQUIRED = "Not Required";

    private static final String OPTIONAL = "Optional";

	private static final String NOT_SET = "Not Set";

    private static final Vector<Class> readOnlyClasses = new Vector<Class>();

    static
    {
        readOnlyClasses.add ( OrganizationC.class );
        readOnlyClasses.add ( UserGroupC.class );
        readOnlyClasses.add ( UserPermissionC.class );
        readOnlyClasses.add ( UserRoleC.class );
        readOnlyClasses.add ( UserPermissionClassificationC.class );
        readOnlyClasses.add ( CounterpartyGroupC.class );
        readOnlyClasses.add ( NamespaceC.class );
        readOnlyClasses.add ( NamespaceGroupC.class );
    }

	public static LegalAgreementHelperC getInstance()
    {
        return legalAgreementInstance;
    }

    public String getLegalAgreement(User user){
        if (user != null) {
            LegalAgreement legalAgreement = AdminServiceC.getInstance().getApplicableLicenseAgreement(user.getOrganization());
            if (legalAgreement != null) {
                return legalAgreement.getContent();
            }
        }
        return null;
    }

    /**
     * Get LegalAgreement status of User. If user has already accepted the status or
     * organization status is either null or Optional (passive) then return status as true
     * else return false so that client can query the status.
     * @param user user
     * @return flag
     */
    public boolean getLegalAgreementStatus(User user)
    {
        String orgStatus = AdminServiceC.getInstance().getCustomerRequirementStatus(user.getOrganization());
        if(orgStatus == null || orgStatus.trim().length() <= 0 || orgStatus.equalsIgnoreCase(NOT_REQUIRED)
				|| orgStatus.equalsIgnoreCase(NOT_SET)) {
            if(log.isInfoEnabled())
                log.info("LegalAgreementHelperC.getLegalAgreementStatus : The organization " + user.getOrganization().getShortName()
                + " status is " + orgStatus );
            return true;
        }

        boolean status = false;
        LegalAgreement la = AdminServiceC.getInstance().getApplicableLicenseAgreement(user.getOrganization());
        if(la != null)
        {
            if(log.isDebugEnabled())
                log.debug("LegalAgreementHelperC.getLegalAgreementStatus : Got Legal Agreement " + la + " for user " + user.getShortName());
            String userAgreementReferenceNumber =(String) user.getCustomFieldValue(ISConstantsC.USER_LEGAL_AGREEMENT_REFERENCE_NUMBER);
			String referenceId = la.getReferenceId();
			if(referenceId == null)
				referenceId = la.getLongName();
			String currentAgreementReferenceNumber = referenceId + la.getLicenseVersion();
            Object userStatus = user.getCustomFieldValue(ISConstantsC.USER_LEGAL_AGREEMENT_STATUS);
            if(userStatus != null)
                status = ((Boolean) userStatus) && !user.isReconfirmLegalAgreement() ;
            if(!status)
                return status;
			else if(status && orgStatus.equalsIgnoreCase(OPTIONAL))
 				return status;
			if(status && !currentAgreementReferenceNumber.equals(userAgreementReferenceNumber))
            {
                if(log.isDebugEnabled())
                    log.debug("LegalAgreementHelperC.getLegalAgreementStatus : Mismatch in reference agreement no. user = " + user.getShortName() +
                        " User Agreement is " + userAgreementReferenceNumber +
                        " Current Agreement is " + currentAgreementReferenceNumber);
                status = false;
                updateUserStatus(user, false, null, 5);
            }
        }
        return status;
    }

    /**
     * Update legal agreement status of User. This message is indication that client has accepted
     * the legal agreement. It doesn't check whether the user accepted version and organization version
     * are in sync or not.
     * @param msg message
     * @return workflow message
     */
    public WorkflowMessage updateLegalAgreementStatus(WorkflowMessage msg) {
        WorkflowMessage replyMsg = MessageFactory.newWorkflowMessage();
        try {
            replyMsg.setTopic(msg.getTopic());
            replyMsg.setEvent(msg.getEvent());
            User user = msg.getSender();
            LegalAgreement la = AdminServiceC.getInstance().getApplicableLicenseAgreement(user.getOrganization());
            if(la != null)
            {
				String referenceId = la.getReferenceId();
				if(referenceId == null)
					referenceId = la.getLongName();
				String currentAgreementReferenceNumber = referenceId + la.getLicenseVersion();
                updateUserStatus(user, true, currentAgreementReferenceNumber, 5);
                replyMsg.setStatus(MessageStatus.SUCCESS);
                ISEmailUtil.getInstance().sendLicenseAgreementAcceptMessage(user.getShortName(), user.getOrganization().getShortName(), System.currentTimeMillis(), referenceId, la.getLicenseVersion());
                audit( user, la);
            } else {
                replyMsg.setStatus(MessageStatus.FAILURE);
            }

        } catch (Exception e) {
            if(log.isDebugEnabled())
                e.printStackTrace();
            log.error("LegalAgreementHelperC.updateLegalAgreementStatus " + e, e);
            replyMsg.setStatus(MessageStatus.FAILURE);
        }
        msg.setReplyMessage(replyMsg);
        return msg;
    }

    private void updateUserStatus(User user,boolean status, String agreementReferenceNo, int retryCount)
    {
        try
        {
            IdcTransaction tx = ISTransactionManager.startTransaction( readOnlyClasses,"LegalAgreementHelperC.updateUserStatus");
            if ( tx != null )
            {
                tx.setNamespaceValidationEnabled ( false );// broker customer users initiate the transaction
            }
			user = (User)ISTransactionManager.getRegisteredObject(user);
			if(agreementReferenceNo != null)
                user.putCustomField(ISConstantsC.USER_LEGAL_AGREEMENT_REFERENCE_NUMBER,agreementReferenceNo);
            user.putCustomField(ISConstantsC.USER_LEGAL_AGREEMENT_STATUS,status);
            if(status){
            	user.setReconfirmLegalAgreement(false);
            }
            ISTransactionManager.endTransaction(tx,"LegalAgreementHelperC.updateUserStatus");
        }
        catch(IdcOptimisticLockException e)
        {
            try
            {
                Session session = PersistenceFactory.newSession();
                session.refreshObject(user);
                if(retryCount >0)
                {
                    updateUserStatus(user,status, agreementReferenceNo, --retryCount);
                }
                else
                {
                    log.error("LegalAgreementHelperC.updateUserStatus Optimistic lock exception unresolved",e);
                }
            } catch (PersistenceException e1)
            {
                log.error("LegalAgreementHelperC.updateUserStatus error in persisting",e1);
            }
        }
        catch(Exception e)
        {
            log.error("LegalAgreementHelperC.updateUserStatus user: " + user + " status: " + status + " retryCount:" + retryCount,e);
        }
    }

    private void audit(User user, LegalAgreement la)
    {
        ISLegalAgreementAuditEventFacadeC agreementAuditEventFacadeC = (ISLegalAgreementAuditEventFacadeC) AuditFactory.newAuditEventFacade(ISLegalAgreementAuditEventFacadeC.IS_LEGALAGREEMENT_AUDIT_FACADE, UserC.class);
        Date licIdcDateTime = new java.util.Date();
        String licAgreementRef = la.getReferenceId();
        if( null == licAgreementRef ) licAgreementRef = la.getLongName();
        agreementAuditEventFacadeC.setLegalAgreementDetails(user, licIdcDateTime, licAgreementRef,  la.getLicenseVersion()) ;
        AuditService auditService = AuditServiceFactory.getDbAuditService();
        auditService.audit(agreementAuditEventFacadeC.getAuditEvent());
    }


}
