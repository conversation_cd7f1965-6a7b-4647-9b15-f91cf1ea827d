package com.integral.client.util;

import com.integral.admin.utils.organization.SalesDealerGroupUtil;
import com.integral.finance.businessCenter.EndOfDayServiceFactory;
import com.integral.finance.counterparty.CounterpartyGroup;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.order.configuration.OrderServiceMBeanC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.email.TradeEmailMBean;
import com.integral.is.common.mbean.ClientConfMBean;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.mbean.ISMBean;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.common.util.QuoteConventionUtilC;
import com.integral.is.management.monitor.UserRuntimeMonitor;
import com.integral.is.message.MessageFactory;
import com.integral.jmsproxy.server.configuration.JMSProxyConfigurationFactory;
import com.integral.jmsx.JMSMBeanC;
import com.integral.jmsx.JMSXMLMBeanC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.persistence.ExternalSystemId;
import com.integral.persistence.PersistenceFactory;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.server.VirtualServer;
import com.integral.time.IdcDate;
import com.integral.user.LoginChannel;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.user.UserUtilC;
import org.eclipse.persistence.expressions.Expression;
import org.eclipse.persistence.expressions.ExpressionBuilder;
import org.eclipse.persistence.queries.ReportQuery;
import org.eclipse.persistence.queries.ReportQueryResult;
import org.eclipse.persistence.sessions.Session;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * Description of the interface or class.
 *
 * <AUTHOR> Development Corp.
 */

public class VersionCheckC
{

	private static Log log = LogFactory.getLog(VersionCheckC.class);
	private static final VersionCheckC versionCheck = new VersionCheckC();
	private static final char COMMA = ',';
	private static ClientConfMBean clientconfMBean = ISFactory.getInstance().getClientConfMBean();
	private static TradeEmailMBean tradeEmailMBean = ISFactory.getInstance().getTradeEmailMBean();
	private static JMSMBeanC jmsbean = JMSXMLMBeanC.getInstance();
	private static ISMBean isMbean = ISFactory.getInstance().getISMBean();
	private static ISUtilImpl isUtil = ISUtilImpl.getInstance();

	private static String dateTimeFormatStr = "yyyy-MM-dd HH:mm:ss z";

	private static IdcSessionManager sessionManager = IdcSessionManager.getInstance();

	private static String PROXY_URL = "proxyURL";
	private static String PROXY_SECURE_URL = "proxySecureURL";
	private static String PROXY_POLLING_INTERVAL = "proxyPollingInterval";
	private static String PROXY_MAX_MSG_COUNT = "proxyMaxMsgCount";
	private static String PROXY_MAIN_URL = "proxyMainURL";
	private static String PROXY_BROKER_ID = "proxyBrokerID";
	private static String JMS_BROKER_URL = "jmsBrokerURL";
	private static String JMS_INIT_QUEUE = "jmsInitQueue";
	private static String CONNECTION_STATUS_TOPIC = "connectionStatusTopic";
	private static String HEARTBEAT_INTERVAL = "heartBeatInterval";
	private static String MINHEARTBEAT_INTERVAL = "MinimumHeartbeatInterval";
	private static String FXRATE_BASIS_QUERY_TIMESTAMP = "fxRateBasisQTS";
	private static String PROVIDER_ORG_WITH_ORDERMATCH_QUERY_TIMESTAMP = "providerOrgWithOrderMatchQTS";
	private static String PROVIDER_ORG_WITHOUT_ORDERMATCH_QUERY_TIMESTAMP = "providerOrgWithoutOrderMatchQTS";
	private static String SUPPORTED_CCYPAIR_QUERY_TIMESTAMP = "supportedCurrencyPairQTS";
	private static String ORDERMATCHING_INTERVAL = "orderMatchingInterval";
	private static String USER_ROLE = "userRole";
	private static String FXI_USER_ROLE = "fxiUserRole";
	private static String CD_PERM = "chiefDealerPerm";
	private static String USER_PERM = "userPerm";
	private static String STGRD_POLL_SWITCH = "stgrdPollSwitch";
	private static String ACCPT_COMPACT_LEVEL = "acptCompactLevel";
	private static String QUOTE_COMPACT_LEVEL = "qtCompactLevel";
	private static String ORG_NAME_MAP = "orgNameMap";
	private static String MULTITIER_ORGS = "multitierOrgs";
	//private static String ORDERADAPTOR_ORGS = "oaOrgs";
	private static String ORDERSADAPTOR_URL_PREFIX = "ordersAdaptorURLPrefix";
	private static String RFS_SERVER_URL_PREFIX = "rfsServerURLPrefix";
	private static String RFS_SERVER_FALLBACK_TO_ESP_SERVER = "rfsFallbackToESPServer";
	private static String PRICE_ADJUSTMENT_THRESHOLD = "priceAdjustmentThreshold";
	private static String BUSINESS_DATE = "businessDate";
	private static String CLIENT_CROSSING = "clientBasedOrderMatching";
	private static String DEALREQUEST_TIMEOUT = "DealRequestTimeout";
	private static String HEARBEAT_REQ_TIMEOUT = "HeartbeatRequestTimeout";
	private static String MISSEDHEARTBEAT_RETRIES = "MissedHeartbeatsRetries";
	private static String MISSEDPOLLING_REQ_RETRIES = "MissedPollingRequestsRetries";
	private static String MINTIME_ELAPSEDTO_DETECT_NETWORK_FAILURE = "MinElapsedTimeToDetectNetworkFailure";
	private static String WAITTIME_DISP_PENDING_ALERT = "WaitTimeToDisplayPendingAlert";
	private static String TRADEEMAIL_EUROPE_CONTACTNO = "EuropeContactNos";
	private static String TRADEEMAIL_NORTH_AMERICA_CONTACTNO = "NorthAmericaContactNos";
	private static String SERVER_NAME = "ServerName";
	private static String TENORS = "Tenors";
	private static String TENOR_SUFFIXES = "TenorSuffix";
	private static final String LA_STATUS = "LegalAgreementStatus";
	public static final String SHOW_ORDERPRICE = "ShowOrderPrice";
	private static final String CLIENT_VERSION = "ClientVersion";
	private static final String CLIENT_NAME = "ClientName";
	public static final String CLIENT_HELP_URL = "ClientHelpURL";
	private static final String MAIN = "MAIN";
	private static final String CLIENT_MAX_PERIOD = "ClientMaxPeriod";
	private static final String SDK_VERSION_1_0_0_1 = "*******";
	private static final String SDK_CLIENT_VERSION_1_2_2 = "1.2.2";
	private static final String PROXY_MODE = "proxyMode";
	private static final String PROXY_STREAM_REQUEST_INTERVAL = "proxyStreamRequestInterval";
	private static final String USER_ORG_INDEX = "userOrgIndex";
	private static final String VIRTUAL_SERVER = "virtualServer";
	private static final String IS_SINGLE_LP_ENABLED = "isSingleLPEnabled";
	private static final String IS_FXI_PRIME_ENABLED = "isFXIPrimeEnabled";
	private static final String IS_FXI_NEWPRO_ENABLED = "isFXINewProEnabled";
	private static final String FORCECPTYCHANGE_ENABLED = "forceCptyChngEnabled";
	private static final String ADMIN_PORTAL_URL = "adminPortalUrl";
	private static final String FORCECPTYCHANGELIST = "forceCptyChangeList";
	private static final String MULTIFILLCONFIG = "multiFillConfig";
	private static final String MARKET_RANGE_ORDER_PROVIDERS = "marketRangeOrderProviders";
	private static final String BROKER_ORGANIZATION = "brokerOrg";
	private static final String PRICE_TIER_PROVIDER = "priceTierProvider";
	private static final String MIN_TRADE_AMOUNT = "minTradeAmount";
	private static final String MAX_ORDER_AMOUNT = "maxOrderAmount";
	private static final String PERSISTENT_ORDERS_ENABLED = "persistentOrdersEnabled";
	private static final String CANCEL_ON_DISCONNECT = "cancelOnDisconnect";
	private static final String IS_BROKER = "isBroker";
	private static final String IS_PRIME_BROKER = "isPrimeBroker";
	private static final String NRH2Enabled = "NRH2Enabled";
	private static final String LEGAL_ENTITIES_FOR_TRADING = "legalEntitiesForTrading";
	private static final String LEGAL_URL = "legalURL";
	private static final String EXECUTION_ENABLED_ORGS = "executionEnabled";

	protected Map<Organization, FXRateConvention> fxRateConventionMap = new ConcurrentHashMap<Organization, FXRateConvention>();

	private VersionCheckC()
	{
	}

	public static VersionCheckC getInstance()
	{
		return versionCheck;
	}

	/**
	 * Check version of client.
	 * For API Client different property is used while FXI Client are checked against
	 * different property.
	 *
	 * @param wfMsg workflow message
	 * @return workflow message
	 */
	public static WorkflowMessage checkVersion( WorkflowMessage wfMsg )
	{

		String clientName = (String) wfMsg.getProperty(CLIENT_NAME);
		if ( wfMsg.getProperty(CLIENT_VERSION) != null )
		{
			if ( isUtil.isCurrentVersionGreaterThanEqualTo((String) wfMsg.getProperty(CLIENT_VERSION), "2.5") )
			{
				sessionManager.getSessionContext(wfMsg.getSender()).setAttribute(ISConstantsC.CLIENT_VERSION_LESS_THAN_TWO_POINT_FIVE, false);
			}
			else
			{
				sessionManager.getSessionContext(wfMsg.getSender()).setAttribute(ISConstantsC.CLIENT_VERSION_LESS_THAN_TWO_POINT_FIVE, true);
			}
		}
		String clientBrand = getBrand(wfMsg);
		String clientVersion = (String) wfMsg.getProperty(CLIENT_VERSION);
		if ( !versionCheck.isValidClientVersion(clientName, clientVersion, wfMsg) )
		{
			log.warn("VersionCheckC.checkVersion UNSUPPORTED_CLIENT_VERSION " + clientVersion);
			WorkflowMessage replyMessage = MessageFactory.newWorkflowMessage();
			replyMessage.setEventName(wfMsg.getEventName());
			replyMessage.setTopic(wfMsg.getTopic());
			replyMessage.addError("UNSUPPORTED_CLIENT_VERSION");
			replyMessage.setStatus(MessageStatus.FAILURE);
			wfMsg.setReplyMessage(replyMessage);
			return wfMsg;
		}
		else
		{
			WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();
			if ( replyMessage == null )
			{
				replyMessage = MessageFactory.newWorkflowMessage();
				wfMsg.setReplyMessage(replyMessage);
			}
			versionCheck.addJMSProxyProperties(replyMessage, wfMsg.getSender(),clientName);
			versionCheck.addOtherLoginProperties(wfMsg, replyMessage);
			versionCheck.addOrdersAdaptorProperties(wfMsg, replyMessage);
			versionCheck.addRFSProperties(wfMsg, replyMessage);
			versionCheck.addMultiFillProperties(wfMsg, replyMessage);
			versionCheck.addProvidersSupportingMarketRangeOrders(wfMsg, replyMessage);
			versionCheck.extractLoginInfo(wfMsg);
			String shouldClientUpdate;
			if ( clientBrand != null )
			{
				shouldClientUpdate = clientconfMBean.getUpdateClientBrandFlag(wfMsg.getSender().getOrganization().getShortName(), getBrand(wfMsg));
			}
			else
			{
				shouldClientUpdate = clientconfMBean.getUpdateClientFlagForOrg(wfMsg.getSender().getOrganization().getShortName());
			}

			replyMessage.setParameterValue(ISConstantsC.UpgradeAlert, shouldClientUpdate != null ? shouldClientUpdate : "0");
			replyMessage.setParameterValue(EXECUTION_ENABLED_ORGS, getExecutionEnabledOrgs(wfMsg.getSender()));

			return wfMsg;
		}
	}

	public static String getBrand( WorkflowMessage wfm )
	{
		Boolean isPreviousClient = (Boolean) sessionManager.getSessionContext(wfm.getSender()).getAttribute(ISConstantsC.CLIENT_VERSION_LESS_THAN_TWO_POINT_FIVE);
		if ( isPreviousClient != null && isPreviousClient )
		{
			return null;
		}
		else
		{
			String brand = wfm.getSender().getOrganization().getAdminBrandName();
			return MAIN.equals(brand) ? null : brand;
		}
	}

	private boolean isValidClientVersion( String clientName, String currentClientVersion, WorkflowMessage wfm )
	{
		if ( log.isDebugEnabled() )
		{
			log.debug("Entering VersionCheckC.isValidClientVersion clientName = " + clientName + " currentClientVersion = " + currentClientVersion);
		}
		if ( clientName == null || clientName.startsWith("ClientSDK") )
		{
			String supportedSDKVersionList = clientconfMBean.getSupportedSDKVersions();
			String sdkVersion = (String) wfm.getProperty("SDKVersion");
			if ( sdkVersion != null && currentClientVersion != null && SDK_VERSION_1_0_0_1.equals(sdkVersion) && SDK_CLIENT_VERSION_1_2_2.equals(currentClientVersion) )
			{
				log.warn("VersionCheckC.isValidClientVersion : incompatible combination of client and sdk version : client Version = " + currentClientVersion + " sdk version = " + sdkVersion);
				return false;
			}
			else
			{
				return (supportedSDKVersionList.indexOf(currentClientVersion.trim()) >= 0);
			}
		}
		else if ( "FXInside".equals(clientName) )
		{
			User user = wfm.getSender();
			String supportedClientVersionList = clientconfMBean.getSupportedFXIVersions(user);
			return (supportedClientVersionList.indexOf(currentClientVersion.trim()) >= 0);
		}
		return false;
	}

	/**
	 * Send JMS PRoxy parameters in Login Response
	 *
	 * @param replyMessage reply message
	 * @param user         user
	 * @param clientName 
	 */
	protected void addJMSProxyProperties( WorkflowMessage replyMessage, User user, String clientName )
	{
		replyMessage.setParameterValue(PROXY_URL, clientconfMBean.getProxyServerURL());
		replyMessage.setParameterValue(PROXY_SECURE_URL, clientconfMBean.getProxyServerSecureURL());
		replyMessage.setParameterValue(PROXY_POLLING_INTERVAL, clientconfMBean.getProxyPollingInterval(user,clientName));
		replyMessage.setParameterValue(PROXY_MAX_MSG_COUNT, clientconfMBean.getProxyMaxMsgCount());
		replyMessage.setParameterValue(PROXY_MAIN_URL, clientconfMBean.getProxyMainURL());
		replyMessage.setParameterValue(PROXY_BROKER_ID, clientconfMBean.getProxyBrokerID());
		if ( JMSProxyConfigurationFactory.getJMSProxyMBean().getPollOrStreamForUser(user) == ServerMBean.STREAMING_MODE_POLL )
		{
			replyMessage.setParameterValue(PROXY_MODE, "Poll");
		}
		else
		{
			replyMessage.setParameterValue(PROXY_MODE, "Stream");
		}
		replyMessage.setParameterValue(PROXY_STREAM_REQUEST_INTERVAL, "" + JMSProxyConfigurationFactory.getJMSProxyMBean().getStreamRequestIntervalForUser(user));
	}

	/**
	 * Send OrdersAdapator prefix url
	 *
	 * @param wfMsg        workflow message
	 * @param replyMessage reply message
	 */
	protected void addOrdersAdaptorProperties( WorkflowMessage wfMsg, WorkflowMessage replyMessage )
	{
		String oaURL = clientconfMBean.getOrdersAdaptorURLPrefix(wfMsg.getSender().getOrganization().getShortName());
		replyMessage.setParameterValue(ORDERSADAPTOR_URL_PREFIX, oaURL);
	}

	/**
	 * Send Separate RFS Server properties.
	 *
	 * @param wfMsg        workflow message
	 * @param replyMessage reply message
	 */
	protected void addRFSProperties( WorkflowMessage wfMsg, WorkflowMessage replyMessage )
	{
		User user = wfMsg.getSender();
		Organization userOrg = user.getOrganization();
		String oaURL = clientconfMBean.getRFSServerURLPrefix(userOrg.getShortName());
		replyMessage.setParameterValue(RFS_SERVER_URL_PREFIX, oaURL);

		boolean f = clientconfMBean.useESPServerIfRFSServerNotWorking(userOrg.getShortName());
		replyMessage.setParameterValue(RFS_SERVER_FALLBACK_TO_ESP_SERVER, f);
		replyMessage.setParameterValue(ISConstantsC.RFS_MISMATCH_SWAP_COMFIG, ISUtilImpl.getInstance().getRFSMismatchSwapConfiguration(userOrg));
	}

	/**
	 * Send other than JMS PRoxy parameters in Login Response.
	 *
	 * @param wfMsg        workflow message
	 * @param replyMessage reply message
	 */
	protected void addOtherLoginProperties( WorkflowMessage wfMsg, WorkflowMessage replyMessage )
	{
		User user = wfMsg.getSender();
		String brokerURL;
		String jmsInitQueue;
		String tradingEnabled = "true";
		brokerURL = clientconfMBean.getJMSURL();
		jmsInitQueue = jmsbean.getDestination("ISClient").getJmsName();
		replyMessage.setParameterValue(JMS_BROKER_URL, brokerURL);
		replyMessage.setParameterValue(JMS_INIT_QUEUE, jmsInitQueue);
		replyMessage.setParameterValue(CONNECTION_STATUS_TOPIC, "IDC.IS.PROVIDER.HEARTBEAT.DESTINATION");
		replyMessage.setParameterValue(MINHEARTBEAT_INTERVAL, clientconfMBean.getMinHeartBeatIinterval());
		replyMessage.setParameterValue(HEARTBEAT_INTERVAL, clientconfMBean.getHeartBeatIinterval());
		replyMessage.setParameterValue(ORDERMATCHING_INTERVAL, clientconfMBean.getOrderMatchingInterval());
		replyMessage.setParameterValue(SERVER_NAME, isMbean.getServerName());

		if ( !isTradingEnabled(user) )
		{
			tradingEnabled = "false";
		}

		addUserPermissions(wfMsg);
		addUserLEInfo(replyMessage, user);
		addLegalEntitiesAvailableForTrading(replyMessage, user);
		replyMessage.setParameterValue(USER_ORG_INDEX, user.getOrganization().getIndex());
		VirtualServer vs = user.getOrganization().getVirtualServer();
		if ( vs != null )
		{
			replyMessage.setParameterValue(VIRTUAL_SERVER, vs.getName());
		}
		else
		{
			replyMessage.setParameterValue(VIRTUAL_SERVER, "");
		}
		sessionManager.getSessionContext(wfMsg.getSender()).setAttribute(ISConstantsC.TRADING_ENABLED, tradingEnabled);
		replyMessage.setParameterValue(ISConstantsC.TRADING_ENABLED, tradingEnabled);
		String orgModifiedTime = new SimpleDateFormat(dateTimeFormatStr).format(new Date(System.currentTimeMillis()));
		replyMessage.setParameterValue(PROVIDER_ORG_WITH_ORDERMATCH_QUERY_TIMESTAMP, orgModifiedTime);
		replyMessage.setParameterValue(PROVIDER_ORG_WITHOUT_ORDERMATCH_QUERY_TIMESTAMP, orgModifiedTime);
		replyMessage.setParameterValue(SUPPORTED_CCYPAIR_QUERY_TIMESTAMP, orgModifiedTime);
		replyMessage.setParameterValue(FXRATE_BASIS_QUERY_TIMESTAMP, getQuoteConvetionModifiedDateTime(user.getOrganization()));
		replyMessage.setParameterValue(STGRD_POLL_SWITCH, clientconfMBean.getStaggeredPollSwitch(wfMsg.getSender()));
		replyMessage.setParameterValue(ACCPT_COMPACT_LEVEL, clientconfMBean.getAcceptCompactionLevel());
		replyMessage.setParameterValue(QUOTE_COMPACT_LEVEL, clientconfMBean.getQuoteCompactionLevel());
		replyMessage.setParameterValue(ORG_NAME_MAP, clientconfMBean.getOrgNameMapping());
		replyMessage.setParameterValue(MULTITIER_ORGS, isMbean.getMultiTierOrgList());
		replyMessage.setParameterValue(PRICE_ADJUSTMENT_THRESHOLD, clientconfMBean.getPriceAdjustmentThreshold());

		IdcDate rollDate = EndOfDayServiceFactory.getEndOfDayService().getCurrentTradeDate();
		replyMessage.setParameterValue(BUSINESS_DATE, rollDate.getFormattedDate(IdcDate.YYYY_MM_DD_HYPHEN));
		replyMessage.setParameterValue(CLIENT_CROSSING, isMbean.isClientCrossingEnabled(user.getOrganization()));
		//Login Failure paramaters
		replyMessage.setParameterValue(DEALREQUEST_TIMEOUT, clientconfMBean.getDealRequestTimeOut());
		ExternalSystemId heartBeatReqTimeOutId = user.getOrganization().getExternalSystemId(HEARBEAT_REQ_TIMEOUT);
		if ( heartBeatReqTimeOutId != null )
		{
			replyMessage.setParameterValue(HEARBEAT_REQ_TIMEOUT, heartBeatReqTimeOutId.getName());
		}
		else
		{
			replyMessage.setParameterValue(HEARBEAT_REQ_TIMEOUT, clientconfMBean.getHeartBeatRequestTimeOut());
		}
		ExternalSystemId minTimeElapsedToDetectNWFailure = user.getOrganization().getExternalSystemId(ISConstantsC.TIME_TO_DETECT_NETWORK_FAILURE);
		if ( minTimeElapsedToDetectNWFailure != null )
		{
			replyMessage.setParameterValue(MINTIME_ELAPSEDTO_DETECT_NETWORK_FAILURE, minTimeElapsedToDetectNWFailure.getName());
		}
		else
		{
			replyMessage.setParameterValue(MINTIME_ELAPSEDTO_DETECT_NETWORK_FAILURE, clientconfMBean.getMinTimeElapsedTotoDetectNWFailure());
		}
		replyMessage.setParameterValue(MISSEDHEARTBEAT_RETRIES, clientconfMBean.getMissedHeartBeatRetries());
		replyMessage.setParameterValue(MISSEDPOLLING_REQ_RETRIES, clientconfMBean.getMissedPollingRetries());
		replyMessage.setParameterValue(WAITTIME_DISP_PENDING_ALERT, clientconfMBean.getWaitTimeToDisplayPendingAlert(user.getOrganization()));
		replyMessage.setParameterValue(TRADEEMAIL_EUROPE_CONTACTNO, tradeEmailMBean.getEuropeContactNo());
		replyMessage.setParameterValue(TRADEEMAIL_NORTH_AMERICA_CONTACTNO, tradeEmailMBean.getNorthAmericaContactNo());
		String helpURL;
		String clientBrand = getBrand(wfMsg);
		if ( clientBrand != null )
		{
			replyMessage.setParameterValue(ISCommonConstants.BrandName, clientBrand);
			replyMessage.setParameterValue(ISCommonConstants.CONTACT_NUMBER, clientconfMBean.getBrandContactNumber(clientBrand));
			replyMessage.setParameterValue(ISCommonConstants.CONTACT_EMAIL, clientconfMBean.getBrandContactEMail(clientBrand));
			String adminUrl = isMbean.getAdminPortalUrlForOrg(user.getOrganization().getShortName());
			if ( adminUrl == null || adminUrl.trim().equals("") )
			{
				adminUrl = isMbean.getAdminPortalUrlForBrand(clientBrand);
				if ( adminUrl == null )
				{
					adminUrl = "";
				}
			}
			replyMessage.setParameterValue(ADMIN_PORTAL_URL, adminUrl.trim());
		}
		else
		{
			replyMessage.setParameterValue(ISCommonConstants.CONTACT_NUMBER, clientconfMBean.getContactNumber());
			replyMessage.setParameterValue(ISCommonConstants.CONTACT_EMAIL, clientconfMBean.getContactEMail());
			String adminUrl = isMbean.getAdminPortalUrlForOrg(user.getOrganization().getShortName());
			if ( adminUrl == null )
			{
				adminUrl = "";
			}
			replyMessage.setParameterValue(ADMIN_PORTAL_URL, adminUrl.trim());
		}
		replyMessage.setParameterValue(IS_SINGLE_LP_ENABLED, clientconfMBean.isSingleLPModeEnabled(user, clientBrand));
		if ( clientconfMBean.isFXIPrimeEnabled(user, clientBrand) )
		{
			replyMessage.setParameterValue(IS_FXI_PRIME_ENABLED, true);
		}
		if ( clientconfMBean.isFXINewProEnabled(user, clientBrand) )
		{
			replyMessage.setParameterValue(IS_FXI_NEWPRO_ENABLED, true);
		}
		else
		{
			replyMessage.setParameterValue(IS_FXI_NEWPRO_ENABLED, false);
		}
		replyMessage.setParameterValue(FORCECPTYCHANGE_ENABLED, clientconfMBean.isForceCptyChangeEnabled(user, clientBrand));
		replyMessage.setParameterValue(FORCECPTYCHANGELIST, clientconfMBean.getForceCptyChangeList(user.getOrganization()));
		helpURL = clientconfMBean.getClientHelpURL();
		replyMessage.setParameterValue(CLIENT_HELP_URL, helpURL);
		replyMessage.setParameterValue(TENORS, clientconfMBean.getSupportedTenors());
		replyMessage.setParameterValue(TENOR_SUFFIXES, clientconfMBean.getSupportedTenorSuffixes());

		String clientMaxPeriod = clientconfMBean.getClientMaxPeriod(user.getOrganization().getShortName());
		replyMessage.setParameterValue(CLIENT_MAX_PERIOD, clientMaxPeriod);
		replyMessage.setParameterValue(LA_STATUS, LegalAgreementHelperC.getInstance().getLegalAgreementStatus(wfMsg.getSender()));
		replyMessage.setParameterValue(SHOW_ORDERPRICE, ISUtilImpl.getInstance().isShowLimitAsMatchedPrice(user.getOrganization()));
		if ( user.getOrganization().getBrokerOrganization() != null )
		{
			replyMessage.setParameterValue(BROKER_ORGANIZATION, user.getOrganization().getBrokerOrganization().getShortName());
		}
		else
		{
			replyMessage.setParameterValue(BROKER_ORGANIZATION, "NULL");
		}
		replyMessage.setParameterValue(PRICE_TIER_PROVIDER, clientconfMBean.getPriceTierProvider(user.getOrganization()));

		String minTradeSizeConfig = isMbean.getMinimumTradeSizeConfigurationForOrg(user.getOrganization());
		String maxOrderSizeConfig = isMbean.getMaximumOrderSizeConfigurationForOrg(user.getOrganization());
		if ( minTradeSizeConfig != null )
		{
			replyMessage.setParameterValue(MIN_TRADE_AMOUNT, minTradeSizeConfig);
		}
		if ( maxOrderSizeConfig != null )
		{
			replyMessage.setParameterValue(MAX_ORDER_AMOUNT, maxOrderSizeConfig);
		}
		replyMessage.setParameterValue(PERSISTENT_ORDERS_ENABLED, OrderServiceMBeanC.getInstance().isPersistentOrderEnabled());
		replyMessage.setParameterValue(CANCEL_ON_DISCONNECT, OrderServiceMBeanC.getInstance().isPersistentOrderCancellationOnDisconnectEnabled(user.getOrganization()));
		replyMessage.setParameterValue(IS_BROKER, clientconfMBean.isCustomerIsBroker(user.getOrganization()));
		replyMessage.setParameterValue(IS_PRIME_BROKER, clientconfMBean.isCustomerIsPrimeBroker(user.getOrganization()));
		replyMessage.setParameterValue(NRH2Enabled, clientconfMBean.isNRH2HandlerEnabled());
		//legal link
		if ( clientconfMBean.isAdminLegalLinkEnabled(user.getOrganization()) )
		{
			replyMessage.setParameterValue(LEGAL_URL, clientconfMBean.getAdminLegalLink(user.getOrganization()));
		}
	}

	private void addLegalEntitiesAvailableForTrading( WorkflowMessage replyMessage, User user )
	{
		StringBuilder sb = new StringBuilder(200);
		if ( isMbean.isOBOWorkflowEnabled(user) )
		{
			Collection<TradingParty> tradingParties = SalesDealerGroupUtil.getCounterpartyLegalEntitiesForAssociatedSDG(user);
			for ( TradingParty tp : tradingParties )
			{
				if ( tp.isActive() && tp.getLegalEntity().isActive() && !tp.getLegalEntityOrganization().getShortName().equals("FXI") )
				{
					sb.append('{').append(tp.getShortName()).append('@').append(tp.getLegalEntityOrganization().getShortName()).append(':').append(tp.getLegalEntity().getObjectID()).append('}');
				}
			}
		}
		CounterpartyGroup existingCptyGroup = (CounterpartyGroup) user.getCustomFieldValue("DirectFX_AssociatedLegalEntities");
		if ( existingCptyGroup != null )
		{
			Collection associatedLEs = existingCptyGroup.getCounterparties();
			for ( Object associatedLE : associatedLEs )
			{
				LegalEntity le = (LegalEntity) associatedLE;
				if ( le.isActive() )
				{
					sb.append('{').append(le.getShortName()).append('@').append(le.getOrganization().getShortName()).append(':').append(le.getObjectID()).append('}');
				}
			}
		}
		replyMessage.setParameterValue(LEGAL_ENTITIES_FOR_TRADING, sb.toString());
	}

	private void addUserLEInfo( WorkflowMessage replyMessage, User user )
	{
		CounterpartyGroup existingCptyGroup = (CounterpartyGroup) user.getCustomFieldValue("DirectFX_AssociatedLegalEntities");
		StringBuilder sb = new StringBuilder(20);
		if ( existingCptyGroup != null )
		{
			Collection associatedLEs = existingCptyGroup.getCounterparties();
			boolean addComma = false;
			for ( Object associatedLE : associatedLEs )
			{
				LegalEntity le = (LegalEntity) associatedLE;
				if ( addComma )
				{
					sb.append(",");
				}
				if ( le.isActive() )
				{
					sb.append(le.getShortName());
					addComma = true;
				}
			}
		}
		replyMessage.setParameterValue(ISCommonConstants.USER_ASSOCIATED_LEGAL_ENTITIES, sb.toString());
		LegalEntity defaultLe = user.getDefaultDealingEntity();
		if ( defaultLe == null )
		{
			replyMessage.setParameterValue(ISCommonConstants.USER_DEFAULT_LEGAL_ENTITY, "");
		}
		else
		{
			replyMessage.setParameterValue(ISCommonConstants.USER_DEFAULT_LEGAL_ENTITY, defaultLe.getShortName());
		}
	}

	private void addUserPermissions( WorkflowMessage wfMsg )
	{
		User user = wfMsg.getSender();
		WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();
		String userPermissions = getUserPermissions(user);
		replyMessage.setParameterValue(USER_PERM, userPermissions);
		// For Bug # 22267 , new properties to send roles to the FXI client to disable/enable features on the client.
		if ( UserUtilC.isMarketMaker(user) )
		{
			replyMessage.setParameterValue(USER_ROLE, "MM");
			replyMessage.setParameterValue(FXI_USER_ROLE, "MM");
			if ( user.hasPermission(ISConstantsC.CHIEFDEALER_DBVIEW_PERM) || user.hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM) )
			{
				replyMessage.setParameterValue(USER_ROLE, "CD");
			}
			// for compatability keep sending CD_PERM.
			replyMessage.setParameterValue(CD_PERM, userPermissions);
		}
		else if ( UserUtilC.isSalesAdministrator(user) )
		{
			replyMessage.setParameterValue(USER_ROLE, "SA");
			replyMessage.setParameterValue(FXI_USER_ROLE, "SA");

		}
		else
		{
			replyMessage.setParameterValue(USER_ROLE, "MAS");
			replyMessage.setParameterValue(FXI_USER_ROLE, "MAS");
		}
	}

	private boolean isTradingEnabled( User user )
	{
		return ISUtilImpl.isOrderExecutionEnabledForTakerOrg(user.getOrganization());
	}

	/**
	 * extract login information sent by the client
	 *
	 * @param wfMsg workflow message
	 */
	protected void extractLoginInfo( WorkflowMessage wfMsg )
	{
		User user = wfMsg.getSender();
		//Extract user's connection related information from workflowMessage and stored it on user object.
		String hostServerAddress = (String) wfMsg.getProperty("HostServerAddress");
		if ( hostServerAddress != null && clientconfMBean.getVPNAddresses().indexOf(hostServerAddress.toUpperCase()) > -1 )
		{
			user.setProperty("network", "VPN");
		}
		else
		{
			user.setProperty("network", "Internet");
		}

		Object protocol = wfMsg.getProperty("ConnectionType");
		Object ipAddress = wfMsg.getProperty("IPAddress");
		Object sdkVersion = wfMsg.getProperty("SDKVersion");
		Object clientVersion = wfMsg.getProperty(CLIENT_VERSION);
		Object clientBrandVersion = wfMsg.getProperty(ISCommonConstants.BrandVersion);
		Object clientName = wfMsg.getProperty(CLIENT_NAME);
		Object clientType = wfMsg.getProperty("ClientType");
		if ( log.isInfoEnabled() )
		{
			log.info(new StringBuilder(200).append("VersionCheckC.extractLoginInfo: ").append(user).append(", protocol[").append(protocol).append("], ipAddress[").append(ipAddress).append("], sdkVersion[").append(sdkVersion).append("], clientVersion[").append(clientVersion).append("], clientName[").append(clientName).append("], Language[").append(clientType).append("]").toString());
		}
		String brandName = getBrand(wfMsg);
		if ( protocol != null )
		{
			user.setProperty("protocol", protocol);
		}
		if ( ipAddress != null )
		{
			user.setProperty("ipAddress", ipAddress);
		}
		if ( sdkVersion != null )
		{
			user.setProperty("sdkVersion", sdkVersion);
		}
		if ( clientVersion != null )
		{
			user.setProperty("clientVersion", clientVersion);
		}
		if ( brandName != null )
		{
			user.setProperty(ISCommonConstants.BrandName, brandName);
			if ( clientBrandVersion != null )
			{
				user.setProperty(ISCommonConstants.BrandVersion, clientBrandVersion);
			}
		}
		if ( clientName != null )
		{
			user.setProperty("clientName", clientName);
		}

		Object cpuType = wfMsg.getProperty("cpuType");
		Object cpuNumber = wfMsg.getProperty("cpuNumber");
		Object cpuSpeed = wfMsg.getProperty("cpuSpeed");
		Object memory = wfMsg.getProperty("memory");

		if ( log.isDebugEnabled() )
		{
			log.debug("VersionCheckC.extractLoginInfo ::cpuType " + cpuType);
			log.debug("VersionCheckC.extractLoginInfo ::cpuNumber " + cpuNumber);
			log.debug("VersionCheckC.extractLoginInfo ::cpuSpeed " + cpuSpeed);
			log.debug("VersionCheckC.extractLoginInfo ::memory " + memory);
		}
		if ( cpuType != null )
		{
			user.setProperty("cpuType", cpuType);
		}
		if ( cpuNumber != null )
		{
			user.setProperty("cpuNumber", cpuNumber);
		}
		if ( cpuSpeed != null )
		{
			user.setProperty("cpuSpeed", cpuSpeed);
		}
		if ( memory != null )
		{
			user.setProperty("memory", memory);
		}

		//set that the user is FXI Client user  session
		IdcSessionContext ctx = sessionManager.getSessionContext(user);
		ctx.setAttribute(ISConstantsC.CTX_ATTR_CLIENT_VERSION, clientVersion);
		ctx.setAttribute(ISConstantsC.CTX_ATTR_CLIENT_NAME, clientName);
		ctx.setAttribute(ISConstantsC.CLIENT_CONNECTION_TYPE, protocol);
		ctx.setAttribute(ISCommonConstants.CTX_ATTR_CLIENT_IP, ipAddress);
		ISUtilImpl isUtil = ISUtilImpl.getInstance();
		//set compaction type based on login parameters
		if ( null == clientName )
		{
			if ( "1.2.2".equals(clientVersion) )
			{
				ctx.setAttribute(ISConstantsC.CLIENT_VERSION, ISConstantsC.API_CLIENT_VERSION_1_2_2);
				ctx.setLoginChannel(LoginChannel.CLIENT_SDK_JAVA);
			}
		}
		else
		{
			if ( "FXInside".equals(clientName) )
			{
				if ( isUtil.isCurrentVersionGreaterThanEqualTo((String) clientVersion, "2.5") )
				{
					ctx.setAttribute(ISConstantsC.CLIENT_VERSION, ISConstantsC.FXI_CLIENT_VERSION_2_5);
				}
				else
				{
					ctx.setAttribute(ISConstantsC.CLIENT_VERSION, ISConstantsC.FXI_CLIENT_VERSION_2_0);
				}
				ctx.setAttribute("isClientSession", Boolean.TRUE);
				ctx.setAttribute("isFXISession", Boolean.TRUE);
				ctx.setLoginChannel(LoginChannel.FXINSIDE_DOTNET);
			}
			else if ( ((String) clientName).startsWith("ClientSDK") )
			{
				if ( "1.2.2".equals(clientVersion) )
				{
					ctx.setAttribute(ISConstantsC.CLIENT_VERSION, ISConstantsC.API_CLIENT_VERSION_1_2_2);
				}
				else if ( isUtil.isCurrentVersionGreaterThanEqualTo((String) clientVersion, "1.1") )
				{
					ctx.setAttribute(ISConstantsC.CLIENT_VERSION, ISConstantsC.API_CLIENT_VERSION_1_1);
				}
				ctx.setAttribute("isClientSession", Boolean.TRUE);
				ctx.setAttribute("isFXISession", Boolean.FALSE);
				ctx.setLoginChannel(LoginChannel.CLIENT_SDK_JAVA);
			}
		}
		if ( log.isInfoEnabled() )
		{
			log.info("VersionCheckerC.extractLoginInfo  compactionType of user " + user.getShortName() + " is " + ctx.getAttribute(ISConstantsC.CLIENT_VERSION));
		}
		//Should be last statement in this method
		UserRuntimeMonitor.getInstance().notifyLogin(user);
	}

	/**
	 * Query FXRateBasis for getting max(modifiedDate).
	 *
	 * @param org organization
	 * @return modified date
	 */
	protected String getQuoteConvetionModifiedDateTime( Organization org )
	{
		FXRateConvention oldFXRateConvention = null;
		Date fxLastRateConvModifiedDate = new Date(System.currentTimeMillis());
		if ( fxRateConventionMap.containsKey(org) )
		{
			oldFXRateConvention = fxRateConventionMap.get(org);
		}
		if ( oldFXRateConvention != null )
		{
			fxLastRateConvModifiedDate = oldFXRateConvention.getModifiedDate();
		}

		FXRateConvention rateConv = QuoteConventionUtilC.getInstance().getFXRateConvention(org);
		SimpleDateFormat sdf = new SimpleDateFormat(dateTimeFormatStr);
		if ( fxLastRateConvModifiedDate.compareTo(rateConv.getModifiedDate()) != 0 )
		{
			fxRateConventionMap.put(org, rateConv);
			return sdf.format(rateConv.getModifiedDate());
		}

		String lastModifiedTime = sdf.format(new Date(System.currentTimeMillis()));
		try
		{
			ExpressionBuilder eb = new ExpressionBuilder();
			ReportQuery query = new ReportQuery(eb);
			query.addItem("FXRateBasisLastTime", eb.get("modifiedDateTime").maximum());
			Expression expr = new ExpressionBuilder().get("fxRateConvention").equal(rateConv);
			query.setSelectionCriteria(expr);
			query.setReferenceClass(FXRateBasis.class);
			Session session = PersistenceFactory.newSession();
			Vector result = (Vector) session.executeQuery(query);
			Date date = new Date(((Timestamp) ((ReportQueryResult) result.elementAt(0)).get("FXRateBasisLastTime")).getTime());
			lastModifiedTime = sdf.format(date);
		}
		catch ( Exception e )
		{
			log.error("VersionCheckC.getQuoteConvetionModifiedDateTime.ERROR : Error getting the quote convention modified time. conv=" + rateConv, e);
		}
		return lastModifiedTime;
	}

	protected int getVersion( String version )
	{
		String str;
		StringTokenizer st = new StringTokenizer(version, ".");
		StringBuffer sb = new StringBuffer();
		while ( st.hasMoreTokens() )
		{
			str = st.nextToken();
			sb.append(str);
		}
		return new Integer(sb.toString()).intValue();
	}

	public static WorkflowMessage addFXIClientInfo( WorkflowMessage wfMsg )
	{
		Object appName = wfMsg.getParameterValue("appName");
		if ( appName == null || "".equals(appName) || !("FXI".equalsIgnoreCase((String) appName)) )
		{
			return wfMsg;
		}
		WorkflowMessage replyMessage = (WorkflowMessage) wfMsg.getReplyMessage();
		if ( replyMessage == null )
		{
			replyMessage = MessageFactory.newWorkflowMessage();
			wfMsg.setReplyMessage(replyMessage);
		}
		replyMessage.setEventName("SOFTWARE");
		replyMessage.setTopic("UPDATE");
		replyMessage.setParameterValue("appName", appName);
		String clientBrand = getBrand(wfMsg);
		String updateURL, supportedVersions;
		User user = wfMsg.getSender();
		if ( clientBrand != null )
		{
			supportedVersions = clientconfMBean.getSupportedFXIBrandVersions(user, clientBrand);
			updateURL = clientconfMBean.getBrandUpdateURL(user, clientBrand);
		}
		else
		{
			supportedVersions = clientconfMBean.getSupportedFXIVersions(user);
			updateURL = clientconfMBean.getFXIFullUpdURL(user);
		}

		replyMessage.setParameterValue("supportedVersions", supportedVersions);
		replyMessage.setParameterValue("incrementalUpdate", clientconfMBean.getFXIIncrUpdURL());
		replyMessage.setParameterValue("versionUpgrader", clientconfMBean.getFXIVerUpdURL());
		replyMessage.setParameterValue("completeUpdate", updateURL);
		NTDHelperC.getInstance().setNTDVersions(wfMsg, replyMessage);

		replyMessage.setParameterValue("forcefullUpdate", clientconfMBean.getFXIForceFullUpdCond());
		return wfMsg;
	}

	/**
	 * extract user update preference information sent by the client
	 *
	 * @param wfMsg workflow message
	 */
	public static void extractUserPrefUpdate( WorkflowMessage wfMsg )
	{
		User user = wfMsg.getSender();
		//Extract user's Preference update info from workflowMessage and stored it on user object.
		if ( user != null )
		{
			Object obj = wfMsg.getParameterValue(ISCommonConstants.SEND_TRADE_EMAIL_VERIFICATION);
			if ( obj != null )
			{
				user.setProperty(ISCommonConstants.SEND_TRADE_EMAIL_VERIFICATION, obj);
			}
			obj = wfMsg.getParameterValue(ISCommonConstants.SEND_TRADE_EMAIL_REJECTION);
			if ( obj != null )
			{
				user.setProperty(ISCommonConstants.SEND_TRADE_EMAIL_REJECTION, obj);
			}
		}
	}

	/**
	 * @param user user
	 * @return comma separated permissions to be sent to client
	 */
	private String getUserPermissions( User user )
	{
		Map<String, String> permMapping = clientconfMBean.getClientPermissionMapping();
		StringBuilder sb = new StringBuilder(100);
		boolean appendComma = false;
		for ( Map.Entry<String, String> perm : permMapping.entrySet() )
		{
			if ( user.hasPermission(perm.getKey()) )
			{
				if ( appendComma )
				{
					sb.append(COMMA);
				}
				else
				{
					appendComma = true;
				}
				sb.append(perm.getValue());
			}
		}
		//To handle RFS Related
		if ( !user.hasPermission(ISConstantsC.RFSTRADING_PERM) )
		{
			if ( permMapping.containsKey(ISConstantsC.TRADE_TYPE_RFS_SPOT_PERM) && user.hasPermission(ISConstantsC.TRADE_TYPE_RFS_SPOT_PERM) || permMapping.containsKey(ISConstantsC.TRADE_TYPE_RFS_OUTRIGHT_PERM) && user.hasPermission(ISConstantsC.TRADE_TYPE_RFS_OUTRIGHT_PERM) || permMapping.containsKey(ISConstantsC.TRADE_TYPE_RFS_SWAP_PERM) && user.hasPermission(ISConstantsC.TRADE_TYPE_RFS_SWAP_PERM) )
			{
				if ( permMapping.containsKey(ISConstantsC.RFSTRADING_PERM) )
				{
					if ( appendComma )
					{
						sb.append(COMMA);
					}
					sb.append(permMapping.get(ISConstantsC.RFSTRADING_PERM));
				}
			}
		}
		return sb.toString();
	}

	private void addMultiFillProperties( WorkflowMessage wfMsg, WorkflowMessage replyMessage )
	{
		User user = wfMsg.getSender();
		replyMessage.setParameterValue(MULTIFILLCONFIG, ISUtilImpl.getInstance().getMultiFillConfiguration(user.getOrganization()));

	}

	private void addProvidersSupportingMarketRangeOrders( WorkflowMessage wfMsg, WorkflowMessage replyMessage )
	{
		User user = wfMsg.getSender();
		replyMessage.setParameterValue(MARKET_RANGE_ORDER_PROVIDERS, ISUtilImpl.getInstance().getRelatedMarketRangeOrderProviders(user.getOrganization()));
	}

	public static String getExecutionEnabledOrgs( User loggedInuser )
	{
		Organization org = loggedInuser.getOrganization();
		String value = org.getShortName() + ',' + Boolean.valueOf( ISUtilImpl.isOrderExecutionEnabledForTakerOrg(org)) + '|';
		Collection<Organization> o = loggedInuser.getOrganization().getRelatedOrganizations(ISCommonConstants.LP_ORG_RELATIONSHIP);
		Iterator<Organization> orgs = o.iterator();
		while ( orgs.hasNext() )
		{
			Organization _c = orgs.next();
			value = value + _c.getShortName() + ',' + Boolean.valueOf(ISUtilImpl.isOrderExecutionEnabledForTakerOrg(_c)) + '|';
			continue;
		}
		return value;
	}

}
