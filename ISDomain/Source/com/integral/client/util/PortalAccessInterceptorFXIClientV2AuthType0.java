package com.integral.client.util;

import com.integral.cas.config.CASMBean;
import com.integral.cas.config.CASMBeanC;
import com.integral.model.portal.PortalApp;
import com.integral.model.portal.PortalAppVisibility;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.user.User;

/**
 * For AuthType 0 i.e. SSO Cookie
 */
public class PortalAccessInterceptorFXIClientV2AuthType0 extends PortalAccessInterceptorFXIClientV1 {
    @Override
    public PortalAppVisibility getPortalAppVisibility(PortalApp app) {
        IdcSessionContext sessionContext = IdcSessionManager.getInstance().getSessionContext();
        if( sessionContext != null ){
            User user = (User) sessionContext.getUser();
            if( user != null ) {
                if(CASMBeanC.getInstance().getAuthTokenType(user) != CASMBean.AuthToken_Type_SSO_COOKIE){
                    return PortalAppVisibility.HIDE;
                }
            }
        }
        return super.getPortalAppVisibility(app);
    }
}
