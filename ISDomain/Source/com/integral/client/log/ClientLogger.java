 package com.integral.client.log;

import com.integral.message.WorkflowMessage;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.ISConstantsC;

/**
 * ClientLogger is a singleton,for remote logging for
 * Client SDK
 *
 * <AUTHOR> Development Corp.
 */



public class ClientLogger {

  /**
   * A handle to the unique ClientLogger instance.
   */
   static private ClientLogger _instance = null;

   protected Log log = LogFactory.getLog(ISFactory.getInstance().getISMBean().getClientLogCategory());

   /**
     * private constructor.
     */
   private ClientLogger() {

   }

   /**
    * @return The unique instance of this class.
    */
   public static  ClientLogger getInstance() {
      if(null == _instance) {
         _instance = new ClientLogger();
      }
      return _instance;
   }

   public static synchronized void log(WorkflowMessage msg){
	    StringBuffer buff = new StringBuffer("Received message -[ ");
		buff.append(msg.getParameterValue(ISConstantsC.PARAM_NAME_LOG_MSG));
		_instance.log.info(buff.toString());		
   }

}
