package com.integral.spaces;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.spaces.metrics.MetricsEvent;
import com.integral.spaces.metrics.MetricsException;
import com.integral.spaces.metrics.SpacesMetricsListener;
import com.integral.system.configuration.ConfigurationFactory;

import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Created with IntelliJ IDEA.
 * User: bhanotp
 * Date: 9/12/12
 */
public class DefaultSpacesMetricsListener implements SpacesMetricsListener {

    private final AtomicLong counter;
    public static String LOCAL_HOST_NAME;
    private static String METRICS = "METRICS";
    private Log logger = LogFactory.getLog(DefaultSpacesMetricsListener.class);
    private String vsName;
    static {
        try {
            java.net.InetAddress localMachine = java.net.InetAddress.getLocalHost();
            LOCAL_HOST_NAME = localMachine.getHostName();
        } catch (java.net.UnknownHostException uhe) {
            LOCAL_HOST_NAME = "LOCALHOST";
        }
    }

    private ThreadLocal<MetricsEvent> threadMetrics = new ThreadLocal<MetricsEvent>() {
        @Override
        protected MetricsEvent initialValue() {
            return new MetricsEvent();
        }
    };

    public DefaultSpacesMetricsListener() {
        counter = new AtomicLong(System.nanoTime());

    }

    public void process(SpaceValueEvent sve) throws MetricsException {
        /*MetricsEvent me = threadMetrics.get();
        try {
            me.setMetaspace(sve.getMetaspaceName());
            me.setNamespace(sve.getNamespace());
            me.setSpace(sve.getCollectionName());
            me.setVirtualServer(getVirtualServerName());
            me.set_id(getVirtualServerName() + "_" + counter.incrementAndGet());
            // TODO: need to see if the cluster name is really required as the metaspace/namespace should be enough
            me.setCluster(Metaspaces.getInstance().getClusterName(sve.getNamespace(), METRICS));
            me.setTimestamp(new Date());
            me.setHostname(LOCAL_HOST_NAME);
            me.getMetrics().put("_id", (String) sve.getId());
            me.getMetrics().put("oper", sve.getOperation().name());
            me.getMetrics().put("rLevel", sve.getReliabilityLevel().name());
            addTimeEvents(me, sve.getSpaceEventTimes());
            Metaspace ms = Metaspaces.getInstance().getMetaspace(sve.getNamespace(), METRICS);
            ms.save(METRICS, me);
        } catch (Exception ex) {
            logger.error("Unable to process MetricsEvent:" + me.toString());
            throw new MetricsException("Unable to process MetricsEvent", ex);
        } finally {
            me.reset();
        }*/
    }

    private String getVirtualServerName() {
        if(vsName == null){
            vsName = ConfigurationFactory.getServerMBean().getVirtualServerName();
        }
        return vsName;
    }

    private void addTimeEvents(MetricsEvent me, SpaceValueEvent.SpaceValueEventTimes svet) {

        me.getMetrics().put("ttlT", "" + (svet.getEndTime() - svet.getStartTime()) / 1000000);
        me.getMetrics().put("srlzT", "" + (svet.getSrlzEndTime() - svet.getSrlzStartTime()) / 1000000);
        me.getMetrics().put("rBT", "" + (svet.getBufferRetrievalTime() - svet.getBufferInsertTime()) / 1000000);
        me.getMetrics().put("hndlT", "" + (svet.getHandleEndTime() - svet.getHandleStartTime()) / 1000000);
        me.getMetrics().put("prsT", "" + (svet.getPersistenceEndTime() - svet.getPersistenceStartTime()) / 1000000);
        me.getMetrics().put("ntfT", "" + (svet.getNotificationStartTime() - svet.getNotificationEndTime()) / 1000000);
    }
}
