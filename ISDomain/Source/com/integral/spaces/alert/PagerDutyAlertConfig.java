package com.integral.spaces.alert;

import com.integral.system.configuration.IdcMBeanC;

/**
 * User: verma
 * Date: 12/11/13
 * Time: 11:55 AM
 */
public class PagerDutyAlertConfig extends IdcMBeanC implements PagerDutyAlertConfigMBean {
    private boolean pagerDutyEnabled;
    private String pagerDutyAuthToken;
    private String pagerDutyEventsURL;
    private String pagerDutyEventsCreateURI;

    public PagerDutyAlertConfig() {
        super( "com/integral/spaces/alert/IdcPagerDutyAlertConfig" );
    }

    @Override
    public void initialize() {
        super.initialize();
        pagerDutyEnabled = getBooleanProperty( PagerDutyAlertConfigMBean.PAGER_DUTY_ENABLED, false );
        pagerDutyAuthToken = getStringProperty( PagerDutyAlertConfigMBean.PAGER_DUTY_AUTHTOKEN, null );
        pagerDutyEventsURL = getStringProperty( PagerDutyAlertConfigMBean.PAGER_DUTY_EVENTS_URL,null );
        pagerDutyEventsCreateURI = getStringProperty( PagerDutyAlertConfigMBean.PAGER_DUTY_EVENTS_CREATE_URI,null );
    }

    @Override
    public boolean isPagerDutyEnabled() {
        return pagerDutyEnabled;
    }

    @Override
    public String getPagerDutyAuthToken() {
        return pagerDutyAuthToken;
    }

    @Override
    public String getPagerDutyServiceKey( String environment, String component ) {
        if( environment == null || component == null ){
            return null;
        }
        String key = new StringBuilder( 100 ).append( PagerDutyAlertConfigMBean.PAGER_DUTY_SERVICE_PREFIX )
                .append( environment.trim() ).append( '_' )
                .append( component.trim() ).toString();
        String service = getStringProperty( key,null );
        if( service == null ){
            key = new StringBuilder( 100 ).append( PagerDutyAlertConfigMBean.PAGER_DUTY_SERVICE_PREFIX )
                    .append( environment.trim() ).append( '_' )
                    .append( PagerDutyAlertConfigMBean.PAGER_DUTY_SERVICE_DEFAULT ).toString();
            service = getStringProperty( key,null );
        }
        return service;
    }

    @Override
    public String getPagerDutyEventsUrl() {
        return pagerDutyEventsURL;
    }

    @Override
    public String getPagerDutyEventsCreateUri() {
        return pagerDutyEventsCreateURI;
    }
}
