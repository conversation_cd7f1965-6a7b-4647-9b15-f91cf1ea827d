package com.integral.spaces.alert;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.util.GUIDFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * User: verma
 * Date: 12/9/13
 * Time: 4:22 PM
 */
public class AlertFactory {
    protected static AlertFactory current;
    private static final Log log = LogFactory.getLog( AlertFactory.class );

    static {
        current = new AlertFactory();
    }

    public static Alert newAlert(AlertCode alertCode){
        return current._newAlert(alertCode);
    }

    protected Alert _newAlert( AlertCode alertCode ) {
        Alert alert = _newAlert();
        alert.setCode( alertCode.getCode() );
        alert.setName( alertCode.name() );
        alert.setComponent( alertCode.getComponent() );
        alert.setType( alertCode.getType().name() );
        alert.setHandlerType( alertCode.getAlertHandlerType() );
        return alert;
    }

    public static Alert newAlert() {
        return current._newAlert();
    }

    protected Alert _newAlert() {
        Alert alert = new Alert();
        alert.setId( generateUID() );
        alert.setTimestamp( System.currentTimeMillis() );
        alert.setVirtualServer( ConfigurationFactory.getServerMBean().getVirtualServerName() );
        alert.setHostname( getLocalServerName() );
        alert.setEnvironment( ConfigurationFactory.getServerMBean().getEnvironment() );
        return alert;
    }

    private String generateUID() {
        return GUIDFactory.nextGUID();
    }

    protected static String getLocalServerName() {
        String hostname = null;
        try {
            hostname = InetAddress.getLocalHost().getHostName();
        }
        catch ( UnknownHostException e ) {
            log.warn( "getLocalServerName: ServerHostAddress is not found" );
        }
        return hostname;
    }
}
