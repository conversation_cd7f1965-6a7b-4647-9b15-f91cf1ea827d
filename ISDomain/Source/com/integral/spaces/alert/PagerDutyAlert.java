package com.integral.spaces.alert;

import com.integral.spaces.serialize.ClassSerializer;
import com.integral.spaces.serialize.FieldSerializer;
import com.integral.spaces.serialize.StringSerializer;

/**
* User: verma
* Date: 12/10/13
* Time: 1:06 PM
*/
@ClassSerializer( name = "pagerDutyAlert" )
public class PagerDutyAlert {
    @FieldSerializer( shortName = "service_key",serializeUsing = StringSerializer.class )
    private String service_key;
    @FieldSerializer( shortName = "event_type",serializeUsing = StringSerializer.class )
    private String event_type;
    @FieldSerializer( shortName = "description",serializeUsing = StringSerializer.class )
    private String description;
    @FieldSerializer( shortName = "incident_key",serializeUsing = StringSerializer.class )
    private String incident_key;
    @FieldSerializer( shortName = "client",serializeUsing = StringSerializer.class )
    private String client;
    @FieldSerializer( shortName = "client_url",serializeUsing = StringSerializer.class )
    private String client_url;
    @FieldSerializer( shortName = "details",serializeUsing = StringSerializer.class )
    private String details;



    public String getService_key() {
        return service_key;
    }

    public void setService_key( String service_key ) {
        this.service_key = service_key;
    }

    public String getEvent_type() {
        return event_type;
    }

    public void setEvent_type( String event_type ) {
        this.event_type = event_type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription( String description ) {
        this.description = description;
    }

    public String getIncident_key() {
        return incident_key;
    }

    public void setIncident_key( String incident_key ) {
        this.incident_key = incident_key;
    }

    public String getClient() {
        return client;
    }

    public void setClient( String client ) {
        this.client = client;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails( String details ) {
        this.details = details;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder( );
        sb.append( "service_key='" ).append( service_key ).append( '\'' );
        sb.append( ", event_type='" ).append( event_type ).append( '\'' );
        sb.append( ", description='" ).append( description ).append( '\'' );
        sb.append( ", incident_key='" ).append( incident_key ).append( '\'' );
        sb.append( ", client='" ).append( client ).append( '\'' );
        sb.append( ", client_url='" ).append( client_url ).append( '\'' );
        sb.append( ", details='" ).append( details ).append( '\'' );
        sb.append( '}' );
        return sb.toString();
    }
}
