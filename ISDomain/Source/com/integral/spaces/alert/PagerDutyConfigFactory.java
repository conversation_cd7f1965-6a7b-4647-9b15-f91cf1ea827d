package com.integral.spaces.alert;

/**
 * User: verma
 * Date: 12/11/13
 * Time: 2:00 PM
 */
public class PagerDutyConfigFactory {
    protected static PagerDutyConfigFactory current;
    private PagerDutyAlertConfig config = new PagerDutyAlertConfig();

    static {
        current = new PagerDutyConfigFactory();
    }

    public static PagerDutyAlertConfigMBean getPagerDutyConfig(){
        return current._getPagerDutyConfig();
    }

    protected PagerDutyAlertConfigMBean _getPagerDutyConfig() {
        return config;
    }
}
