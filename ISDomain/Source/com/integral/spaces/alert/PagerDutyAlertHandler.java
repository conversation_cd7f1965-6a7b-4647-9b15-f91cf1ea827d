package com.integral.spaces.alert;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.spaces.serialize.SerializationHandler;
import com.integral.spaces.serialize.SerializerFactory;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.protocol.BasicHttpContext;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;


/**
 * User: verma
 * Date: 12/9/13
 * Time: 5:13 PM
 */
public class PagerDutyAlertHandler extends LogAlertHandler implements AlertHandler {
    private static final Log log = LogFactory.getLog(PagerDutyAlertHandler.class);

    @Override
    public void handle( Alert alert ) {
        DefaultHttpClient httpclient = null;
        try {
            httpclient = new DefaultHttpClient();
            httpclient.getParams().setIntParameter( CoreConnectionPNames.CONNECTION_TIMEOUT,5000 );
            httpclient.getParams().setIntParameter( CoreConnectionPNames.SO_TIMEOUT,5000 );
            BasicHttpContext localcontext = new BasicHttpContext();
            PagerDutyAlertConfigMBean pdConfig = PagerDutyConfigFactory.getPagerDutyConfig();
            if ( !pdConfig.isPagerDutyEnabled() ) {
                super.handle( alert );
                return;
            }
            String serviceKey = pdConfig.getPagerDutyServiceKey( alert.getEnvironment(),alert.getComponent() );
            if( serviceKey == null || serviceKey.trim().equals( "" )){
                super.handle( alert );
                return;
            }
            HttpHost targetHost = new HttpHost( pdConfig.getPagerDutyEventsUrl() );
            HttpPost httpPost = new HttpPost( pdConfig.getPagerDutyEventsCreateUri() );
            httpPost.addHeader( "Authorization: Token token",pdConfig.getPagerDutyAuthToken() );
            httpPost.addHeader( "Content-Type", "application/json" );
            PagerDutyAlert pda = new PagerDutyAlert();
            pda.setService_key( serviceKey );
            pda.setEvent_type( "trigger" );
            pda.setIncident_key( alert.getId() );
            pda.setDescription( alert.getDescription() );
            pda.setDetails( alert.getData() );
            SerializationHandler handler = SerializerFactory.getHandlerForType( SerializerFactory.Type.JSON );
            ByteBuffer buff = ByteBuffer.allocate( 8192 );
            handler.serializeObject( pda, SerializationHandler.Operation.INSERT, buff );
            String serialized = new String( buff.array() );
            StringEntity e = new StringEntity( serialized );
            httpPost.setEntity( e );
            HttpResponse response = httpclient.execute( targetHost, httpPost, localcontext );
            if( response.getStatusLine().getStatusCode() == HttpStatus.SC_OK ){
                log.info( "Alert sent to PagerDuty. Alert="+serialized );
            }
            else{
                StringBuilder sb = new StringBuilder( 500 );
                BufferedReader br = new BufferedReader(new InputStreamReader((response.getEntity().getContent())));
                String output;
                while ((output = br.readLine()) != null) {
                    sb.append( output );
                }
                log.warn( "Failed to send Alert to PagerDuty. Alert="+serialized+",Response="+sb.toString() );
                super.handle( alert );
            }
        }
        catch ( Exception ex ) {
            log.error( "handle : Failed to process alert "+alert,ex );
            super.handle( alert );
        }
        finally {
            if( httpclient != null ){
                httpclient.getConnectionManager().shutdown();
            }
        }
    }
}
