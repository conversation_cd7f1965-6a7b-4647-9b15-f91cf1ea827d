package com.integral.spaces.alert;

import com.integral.system.configuration.IdcMBean;

/**
 * User: verma
 * Date: 12/11/13
 * Time: 11:56 AM
 */
public interface PagerDutyAlertConfigMBean extends IdcMBean {
    public static final String PAGER_DUTY_ENABLED = "Idc.Spaces.Alert.PagerDuty.Enabled";
    public static final String PAGER_DUTY_EVENTS_URL = "Idc.Spaces.Alert.PagerDuty.Events.URL";
    public static final String PAGER_DUTY_EVENTS_CREATE_URI = "Idc.Spaces.Alert.PagerDuty.Events.Create.URI";
    public static final String PAGER_DUTY_SERVICE_PREFIX = "Idc.Spaces.Alert.PagerDuty.Service.";
    public static final String PAGER_DUTY_AUTHTOKEN = "Idc.Spaces.Alert.PagerDuty.AuthToken";
    public static final String PAGER_DUTY_SERVICE_DEFAULT = "General";

    public boolean isPagerDutyEnabled();

    public String getPagerDutyAuthToken();

    public String getPagerDutyServiceKey( String environment, String component );

    public String getPagerDutyEventsUrl();

    public String getPagerDutyEventsCreateUri();
}
