package com.integral.spaces.alert;

import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * User: verma
 * Date: 12/9/13
 * Time: 12:47 PM
 *
 * This processor Logs the Alert to a Log File.
 */
public class LogAlertHandler implements AlertHandler {
    private static final Log log = LogFactory.getLog(LogAlertHandler.class);

    @Override
    public void handle( Alert alert ) {
        try{
            log.warn( "SPAC-ALERT alert="+alert );
        }
        catch ( Exception ex ){
            log.error( "SPAC-ALERT-ERROR : Exception in Logging Alert ",ex );
        }
    }
}
