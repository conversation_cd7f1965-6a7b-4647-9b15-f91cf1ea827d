package com.integral.spaces.alert;

import com.integral.spaces.serialize.ClassSerializer;
import com.integral.spaces.serialize.EnumSerializer;
import com.integral.spaces.serialize.FieldSerializer;
import com.integral.spaces.serialize.LongSerializer;
import com.integral.spaces.serialize.StringSerializer;

import java.util.Date;

/**
 * User: verma
 * Date: 12/9/13
 * Time: 11:31 AM
 */
@ClassSerializer(name = "alert")
public class Alert {
    /**
     * Unique ID of the alert.
     */
    @FieldSerializer( shortName = "id", serializeUsing = StringSerializer.class)
    private String id;
    /**
     * Virtual Server that generated this alert.
     */
    @FieldSerializer(shortName = "vs", serializeUsing = StringSerializer.class)
    private String virtualServer;

    /**
     * Hostname of the machine that generated this alert.
     */
    @FieldSerializer(shortName = "host", serializeUsing = StringSerializer.class)
    private String hostname;

    /**
     * Environment to which this alert belongs to. e.g. QA,PROD,UAT etc.
     */
    @FieldSerializer( shortName = "env", serializeUsing = StringSerializer.class)
    private String environment;

    /**
     * MilliSeconds representing the time at which this alert was generated.
     */
    @FieldSerializer(shortName = "time", serializeUsing = LongSerializer.class)
    private long timestamp;

    /**
     * Name of the alert.
     */
    @FieldSerializer(shortName = "name", serializeUsing = StringSerializer.class)
    private String name;

    /**
     * Code assigned to the alert.
     */
    @FieldSerializer(shortName = "code", serializeUsing = StringSerializer.class)
    private String code;

    /**
     * Type of the alert.
     */
    @FieldSerializer(shortName = "type", serializeUsing = StringSerializer.class)
    private String type;

    /**
     * Processor for this alert.
     */
    @FieldSerializer(shortName = "handlerType", serializeUsing = EnumSerializer.class)
    private AlertHandlerType handlerType;

    /**
     * Component that generated this alert.
     */
    @FieldSerializer(shortName = "component", serializeUsing = StringSerializer.class)
    private String component;

    /**
     * Any additional data that has to be passed as part of the alert. JSON is preferred if you want to do additional
     * processing.
     */
    @FieldSerializer(shortName = "data", serializeUsing = StringSerializer.class)
    private String data;

    public String getId() {
        return id;
    }

    public void setId( String id ) {
        this.id = id;
    }

    public String getVirtualServer() {
        return virtualServer;
    }

    public void setVirtualServer( String virtualServer ) {
        this.virtualServer = virtualServer;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname( String hostname ) {
        this.hostname = hostname;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment( String environment ) {
        this.environment = environment;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp( long timestamp ) {
        this.timestamp = timestamp;
    }

    public String getName() {
        return name;
    }

    public void setName( String name ) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode( String code ) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType( String type ) {
        this.type = type;
    }

    public AlertHandlerType getHandlerType() {
        return handlerType;
    }

    public void setHandlerType( AlertHandlerType handlerType ) {
        this.handlerType = handlerType;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent( String component ) {
        this.component = component;
    }

    public String getData() {
        return data;
    }

    public void setData( String data ) {
        this.data = data;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder(300).append( "Alert{" );
        sb.append( "id=" ).append( id );
        sb.append( ", virtualServer=" ).append( virtualServer );
        sb.append( ", hostname=" ).append( hostname );
        sb.append( ", environment=" ).append( environment );
        sb.append( ", timestamp=" ).append( timestamp );
        sb.append( ", name=" ).append( name );
        sb.append( ", code=" ).append( code );
        sb.append( ", type=" ).append( type );
        sb.append( ", processor=" ).append( handlerType );
        sb.append( ", component=" ).append( component );
        sb.append( ", data=" ).append( data );
        sb.append( '}' );
        return sb.toString();
    }

    public String getDescription() {
        StringBuilder sb = new StringBuilder( 200 );
        sb.append( "environment=" ).append( environment );
        sb.append( ", id=" ).append( id );
        sb.append( ", virtualServer=" ).append( virtualServer );
        sb.append( ", hostname=" ).append( hostname );
        sb.append( ", time=" ).append( new Date( timestamp ) );
        sb.append( ", name=" ).append( name );
        sb.append( ", code=" ).append( code );
        sb.append( ", type=" ).append( type );
        sb.append( ", component=" ).append( component );
        return sb.toString();
    }
}
