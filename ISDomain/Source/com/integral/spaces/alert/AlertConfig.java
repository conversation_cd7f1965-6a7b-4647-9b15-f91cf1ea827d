package com.integral.spaces.alert;

import java.util.Map;

import com.integral.system.configuration.IdcMBeanC;

/**
 * User: verma
 * Date: 12/11/13
 * Time: 11:02 AM
 */
public class AlertConfig extends IdcMBeanC implements AlertConfigMBean {
    private boolean alertEnabled;
    private static String IDC_SEND_ALERT_ENABLED = "Idc.SendAlert.Enabled";
    private boolean isSendAlertEnabled;
    private Map<String, Boolean> isSendAlertEnabledMap;

    private AlertConfig() {
        super( "com/integral/spaces/alert/IdcAlertConfig" );
        initialize();
    }
    
	private static final AlertConfig INSTANCE = new AlertConfig();
	

	public static AlertConfig getInstance()
	{
		return INSTANCE;
	}
    @Override
    public void initialize() {
        super.initialize();
        alertEnabled = getBooleanProperty( AlertConfigMBean.ALERT_ENABLED,false );
        isSendAlertEnabled = getBooleanProperty(IDC_SEND_ALERT_ENABLED, false);
        isSendAlertEnabledMap = initSingleSuffixBooleanPropertyMap(IDC_SEND_ALERT_ENABLED + ".", null, isSendAlertEnabled);
    }

    @Override
    public boolean isAlertEnabled() {
        return alertEnabled;
    }
    
    public boolean isSendAlertEnabled(String orgName){
        if(orgName != null) {
            Boolean value = isSendAlertEnabledMap.get(orgName);
            if(value != null) return value;
        }

        return isSendAlertEnabled;
    } 
    
}
