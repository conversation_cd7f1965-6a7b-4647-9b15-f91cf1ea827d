package com.integral.spaces.alert;

/**
 * User: verma
 * Date: 12/9/13
 * Time: 12:34 PM
 */
public enum AlertCode {
    NS_HANDLER_EXCEPTION("NS_HANDLER_EXCEPTION",AlertType.EXCEPTION,AlertHandlerType.PAGER_DUTY,AlertComponents.NOTIFICATION);
    private final String code;
    private final AlertType type;
    private final AlertHandlerType alertHandlerType;
    private final String component;

    private AlertCode( String code,AlertType type, AlertHandlerType alertHandlerType, String component ){
        this.code = code;
        this.type = type;
        this.alertHandlerType = alertHandlerType;
        this.component = component;
    }

    public AlertType getType() {
        return type;
    }

    public AlertHandlerType getAlertHandlerType() {
        return alertHandlerType;
    }

    public String getComponent() {
        return component;
    }

    public String getCode() {
        return code;
    }
}
