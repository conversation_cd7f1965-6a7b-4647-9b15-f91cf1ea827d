package com.integral.spaces.alert;

import com.integral.log.Log;
import com.integral.log.LogFactory;

/**
 * User: verma
 * Date: 12/9/13
 * Time: 11:28 AM
 */
public class AlertManager {
    private static final Log log = LogFactory.getLog(AlertManager.class);
    private static final AlertHandler logAlertHandler = new LogAlertHandler();
    private static final AlertHandler pagerDutyAlertHandler = new PagerDutyAlertHandler();

    /**
     * Publish an {@link com.integral.spaces.alert.Alert}
     * This method should be called by the applications to publish an alert.
     * Applications can generate an alert by using {@link com.integral.spaces.alert.AlertFactory} and publish them.
     *
     * @param alert
     */
    public void publish(Alert alert){
        //todo implement
    }

    /**
     * Process an {@link com.integral.spaces.alert.Alert}
     * This method should be called on receiving a alert to process it.
     * The relevant handler will be invoked to process the alert.
     * Applications should not call it directly. They should use {@link com.integral.spaces.alert.AlertManager#publish(Alert)} method.
     * @param alert
     */
    public void process( Alert alert ){
        try{
            AlertHandler handler = getAlertHandler( alert );
            handler.handle( alert );
        }
        catch ( Exception ex ){
            log.error( "process : Exception in processing Alert ",ex );
        }
    }

    private AlertHandler getAlertHandler( Alert alert ) {
        AlertHandlerType handlerType = alert.getHandlerType();
        if( handlerType == null ){
            handlerType = AlertHandlerType.LOG;
        }
        switch ( handlerType ){
            case LOG:
                return logAlertHandler;
            case PAGER_DUTY:
                return pagerDutyAlertHandler;
            default:
                return logAlertHandler;
        }
    }
}
