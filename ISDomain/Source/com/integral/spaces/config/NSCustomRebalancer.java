package com.integral.spaces.config;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.integral.cluster.ClusterConfigDataService;
import com.integral.model.cluster.ClusterMetaData;
import com.integral.services.ServiceContainerMBean;
import org.apache.helix.HelixDefinedState;
import org.apache.helix.HelixManager;
import org.apache.helix.api.Cluster;
import org.apache.helix.api.State;
import org.apache.helix.api.id.ParticipantId;
import org.apache.helix.api.id.PartitionId;
import org.apache.helix.controller.context.ControllerContextProvider;
import org.apache.helix.controller.rebalancer.CustomRebalancer;
import org.apache.helix.controller.rebalancer.config.RebalancerConfig;
import org.apache.helix.controller.stages.ResourceCurrentState;
import org.apache.helix.model.IdealState;
import org.apache.helix.model.ResourceAssignment;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.cluster.ResourceInfo;
import com.integral.system.configuration.IdcMBeanC;
import com.integral.system.property.SystemPropertyC;
import com.integral.util.CompositeKeys;

public class NSCustomRebalancer extends CustomRebalancer {

    private static Log log = LogFactory.getLog(NSCustomRebalancer.class);

    private static final String INVALID_CONFIG = "INVALID_CONFIG";

    private static final String NAMESPACE_DELIMITTER = "-";

    // Key : Resource name;Ex:FXI_BQC2#ALL, retailbqc2#ALL
    // Value: primary virtual server, secondary virtual server
    private static volatile Map<String, CompositeKeys> handlerToNSPreference = new HashMap<String, CompositeKeys>();

    @Override
    public void init(HelixManager helixManager, ControllerContextProvider contextProvider) {
        if (handlerToNSPreference.isEmpty()) {
            //Take the class level lock. Even init is only one for the 'n' instances getting created for the NSCustomRebalancer class.
            synchronized (NSCustomRebalancer.class) {
                if (handlerToNSPreference.isEmpty()) {
                    ServiceContainerMBean coordinatorConfig = ServiceContainerMBean
                            .getInstance();
                    String effectiveClusterName = coordinatorConfig.getServicesNamespace()
                            + NAMESPACE_DELIMITTER + MetaspacesConfigMBean.NS_CLUSTER_NAME;

                    ClusterMetaData clusterMetaData = ClusterConfigDataService
                            .getInstance().getClusterMetaData(effectiveClusterName);

                    log.info("Initializing ideal states for custom Rebalancer, for the cluster:" + effectiveClusterName);
                    initIdealReBalancingState(clusterMetaData.getResources());
                }
            }
        }
    }

    public void initIdealReBalancingState(List<ResourceInfo> resources) {
        Set<String> allResources = new HashSet<String>();
        for (ResourceInfo resource : resources) {
            allResources.add(resource.getName());
        }

        if (allResources.isEmpty()) {
            log.info("No resources to compute the ideal allocation");
            return;
        }

        String configuredRMQPrefix = MetaspacesConfigMBeanImpl.getInstance()
                .getNotificationServerRMQPrefix();

        String sb = "Initializing ideal re balancing state for resource:";
        for (String resrc : allResources) {
            try {
                log.info(sb + resrc);

                // extract the rmq prefix, handler name
                String[] res = resrc
                        .split(MetaspacesConfigMBean.NS_RESOURCE_SEPARATOR);
                String queuePrefix = res[0];
                String[] listeners = res[1].split("_");
                String listenerHandlerGroup = listeners[0];

                // 1. For the handler, find the designated VS.
                String propertyName = MetaspacesConfigMBean.NOTIFICATION_LISTENER_HANDLER_GROUPS_PROPERTY;
                Collection<SystemPropertyC> properties = IdcMBeanC
                        .getPropertyFromDB(propertyName, listenerHandlerGroup);

                String primPrefVirtualServer;
                String secondPrefVirtualServer;
                if (properties != null && !properties.isEmpty()) {
                    // there will be atleast 2 properties : a NS with
                    // rmqprefix
                    // and one without any rmq prefix
                    Iterator<SystemPropertyC> itr = properties.iterator();

                    SystemPropertyC property = itr.next();
                    // get the Idc.Metaspaces.Notifications for this virtual
                    // server
                    Collection<SystemPropertyC> metaspaceNotificationProps = IdcMBeanC
                            .getPropertyFromDBForVS(
                                    property.getVirtualServer().getName(),
                                    MetaspacesConfigMBean.NOTIFICATION_PROPERTIES_PREFIX);
                    // there will be only one property
                    Iterator<SystemPropertyC> i = metaspaceNotificationProps
                            .iterator();

                    if (!i.hasNext()) {
                        log.warn("Missing the cluster mode configuration:" + MetaspacesConfigMBean.NOTIFICATION_PROPERTIES_PREFIX + " for , virtual server: " + property.getVirtualServer().getName());
                        return;
                    }

                    SystemPropertyC metaspaceNotificationProperty = i.next();
                    // check if the metaspaceNotificationProperty contains
                    // the queue prefix

                    if (queuePrefix.equals(configuredRMQPrefix)) {
                        if (metaspaceNotificationProperty.getStringValue()
                                .contains("rmqPrefix=" + queuePrefix)) {
                            primPrefVirtualServer = property.getVirtualServer()
                                    .getName();
                            if (itr.hasNext()) {
                                secondPrefVirtualServer = itr.next()
                                        .getVirtualServer().getName();
                            } else {
                                secondPrefVirtualServer = primPrefVirtualServer;
                            }
                        } else {
                            secondPrefVirtualServer = property.getVirtualServer()
                                    .getName();
                            if (itr.hasNext()) {
                                primPrefVirtualServer = itr.next()
                                        .getVirtualServer().getName();
                            } else {
                                primPrefVirtualServer = secondPrefVirtualServer;
                            }
                        }
                    } else {
                        if (!metaspaceNotificationProperty.getStringValue()
                                .contains("rmqPrefix")) {
                            primPrefVirtualServer = property.getVirtualServer()
                                    .getName();
                            if (itr.hasNext()) {
                                secondPrefVirtualServer = itr.next()
                                        .getVirtualServer().getName();
                            } else {
                                secondPrefVirtualServer = primPrefVirtualServer;
                            }
                        } else {
                            secondPrefVirtualServer = property.getVirtualServer()
                                    .getName();
                            if (itr.hasNext()) {
                                primPrefVirtualServer = itr.next()
                                        .getVirtualServer().getName();
                            } else {
                                primPrefVirtualServer = secondPrefVirtualServer;
                            }
                        }
                    }

                } else {
                    primPrefVirtualServer = INVALID_CONFIG;
                    secondPrefVirtualServer = INVALID_CONFIG;
                    log.warn("Notification Servers can never be started in Cluster mode;Check the NS configuration");
                }

                log.info("Resource:" + resrc
                        + ":Ideal allocation:Found:Primary VS:"
                        + primPrefVirtualServer + ":Secondary VS:"
                        + secondPrefVirtualServer);
                CompositeKeys value = CompositeKeys.getCompositeKeys(
                        primPrefVirtualServer, secondPrefVirtualServer);
                handlerToNSPreference.put(resrc, value);

            } catch (Exception e) {
                String err = "Error in initializing ideal NS cluster state for resource:"
                        + resrc + ":Cause:" + e.getMessage();
                log.error(err, e);
                throw new RuntimeException(err);
            }
        }

        for (Entry<String, CompositeKeys> entry : handlerToNSPreference
                .entrySet()) {
            log.info("Ideal allocation for:" + entry.getKey() + ":Server:"
                    + entry.getValue());
        }

    }

    public ResourceAssignment computeResourceMapping(IdealState idealState,
                                                     RebalancerConfig rebalancerConfig,
                                                     ResourceAssignment prevAssignment, Cluster cluster,
                                                     ResourceCurrentState currentState) {

        if (log.isDebugEnabled()) {
            log.info("Balancing the cluster for resource:" + idealState.getId());
        }
        
		if (!idealState.isEnabled()) {
			log.info("Skipping custom re-balancing as the resource:" + idealState.getId() +"  is disabled");
			ResourceAssignment assignment = super.computeResourceMapping(
					idealState, rebalancerConfig, prevAssignment, cluster,
					currentState);
			log.info("Updated Assignment:" + assignment);
			return assignment;
		}
        
        // Get the list of live participants in the cluster
        List<ParticipantId> liveParticipants = new ArrayList<ParticipantId>(
                cluster.getLiveParticipantMap().keySet());
        if (!liveParticipants.isEmpty()) {
            CompositeKeys preferredNSServers = handlerToNSPreference
                    .get(idealState.getId());

            String primPrefVirtualServer = null;
            String secondPrefVirtualServer = null;
            if (preferredNSServers.getKeys().length > 0)
                primPrefVirtualServer = preferredNSServers.getKeysAtIndex(0);
            if (preferredNSServers.getKeys().length > 1)
                secondPrefVirtualServer = preferredNSServers
                        .getKeysAtIndex(1);

            if (log.isDebugEnabled()) {
                log.debug("Resource:" + idealState.getId()
                        + ":Ideal allocation:Found:Primary VS:"
                        + primPrefVirtualServer + ":Secondary VS:"
                        + secondPrefVirtualServer);
            }

            // is the designated VS online? if yes, assign it to that VS
            // else assign it to any other VS
            ParticipantId preferredPrimary = null;
            ParticipantId preferredSecondary = null;
            for (ParticipantId p : liveParticipants) {
                if (p.stringify().equalsIgnoreCase(primPrefVirtualServer)) {
                    preferredPrimary = p;
                    break;
                }
            }

            // try the secondary preferred VS
            for (ParticipantId p : liveParticipants) {
                if (p.stringify().equalsIgnoreCase(secondPrefVirtualServer)) {
                    preferredSecondary = p;
                    break;
                }
            }

            Map<PartitionId, ParticipantId> errorStates = new HashMap<PartitionId, ParticipantId>();
            if (null != currentState) {
                for (PartitionId partition : idealState.getPartitionIdSet()) {
                    Map<ParticipantId, State> currentStateMap = currentState.getCurrentStateMap(idealState.getResourceId(), partition);
                    for (Map.Entry<ParticipantId, State> states : currentStateMap.entrySet()) {
                        if (states.getValue().equals(State.from(HelixDefinedState.ERROR))) {
                            errorStates.put(partition, states.getKey());
                        }
                    }
                }
            }
            if (!errorStates.isEmpty()) {
                log.info("Error state partitions and participants:" + errorStates);
            }

            ResourceAssignment assignment = new ResourceAssignment(idealState.getResourceId());
            boolean primaryNotInError;
            boolean secondaryNotInError;
            for (PartitionId partition : idealState.getPartitionIdSet()) {

                primaryNotInError = notInErrorState(errorStates, partition, preferredPrimary, "Primary", "Secondary");
                if (preferredPrimary != null && primaryNotInError) {
                    log.info("Preferred VS is online:" + primPrefVirtualServer + ",Chosen node for:" + idealState.getId() + ":"
                            + preferredPrimary.stringify());
                    // preferred primary server is live and not in error state
                    makeAssignment(partition, prevAssignment, liveParticipants, preferredPrimary, assignment);
                    continue;
                }

                secondaryNotInError = notInErrorState(errorStates, partition, preferredSecondary, "Secondary", "Random");
                if (preferredSecondary != null && secondaryNotInError) {
                    log.info("Secondary Preferred VS is online:" + secondPrefVirtualServer + ",Chosen node for:" + idealState.getId() + ":"
                            + preferredSecondary.stringify());
                    // preferred secondary server is live and not in error state
                    makeAssignment(partition, prevAssignment, liveParticipants, preferredSecondary, assignment);
                    continue;
                }

                // Primary and Secondary preferred participant is Not Live/Partition Error state. // Select non-error partition state from any one of the live instances
                ParticipantId preferredParticipant = chooseRandomWithFilters(errorStates, partition, liveParticipants, preferredPrimary, preferredSecondary);
                if (null != preferredParticipant) {
                    log.info("Neither the primary nor the secondary vs is online or non-error state. Choosing any other live instance for:" + idealState.getId());
                    makeAssignment(partition, prevAssignment, liveParticipants, preferredParticipant, assignment);
                } else {
                    log.info("Cannot find a valid assignment for the partition:" + partition);
                }
            }

            log.info("Updated Assignment:" + assignment);
            return assignment;
        } else {
            log.info("Skipping custom re-balancing as there are no live participants");
            ResourceAssignment assignment = super.computeResourceMapping(
                    idealState, rebalancerConfig, prevAssignment, cluster,
                    currentState);
            log.info("Updated Assignment:" + assignment);
            return assignment;
        }
    }

    private ParticipantId chooseRandomWithFilters(Map<PartitionId, ParticipantId> errorStates, PartitionId partition, List<ParticipantId> liveParticipants, ParticipantId preferredPrimary, ParticipantId preferredSecondary) {

        //choosing non-error , non-primary , non-secondary preferred.
        //Can be optimized to consider load in future.
        ParticipantId errorParticipantId;
        for (ParticipantId liveParticipant : liveParticipants) {
            if (!liveParticipant.equals(preferredPrimary) && !liveParticipant.equals(preferredSecondary)) {
                errorParticipantId = errorStates.get(partition);
                if (null != errorParticipantId) {
                    if (!errorParticipantId.equals(liveParticipant)) {
                        //Choosing the first node as partition holder.This logic can be optimized further to even random or round robin.
                        return liveParticipant;
                    }
                } else {
                    //Choosing the first node as partition holder.This logic can be optimized further to even random or round robin.
                    return liveParticipant;
                }
            }
        }
        return null;
    }

    private boolean notInErrorState(Map<PartitionId, ParticipantId> errorStates, PartitionId partition, ParticipantId preferred, String current, String next) {
        if (preferred == null) {
            return false;
        }

        ParticipantId participantId = errorStates.get(partition);
        if (null != participantId && participantId.equals(preferred)) {
            //Primary in error state
            log.info("Partition in error state:" + partition + ", for the ideal " + current + " vs :" + preferred + ", trying :" + next + " vs.");
            return false;
        }
        return true;
    }

    private void makeAssignment(PartitionId partition, ResourceAssignment prevAssignment, List<ParticipantId> liveParticipants, ParticipantId preferredParticipant, ResourceAssignment assignment) {

        Map<ParticipantId, State> replicaMap = assignment.getReplicaMap(partition);
        replicaMap.put(preferredParticipant, State.from("ONLINE"));
        dropPartitionFromOtherParticipants(partition, prevAssignment, liveParticipants, replicaMap, preferredParticipant);
        assignment.addReplicaMap(partition, replicaMap);
    }

    private void dropPartitionFromOtherParticipants(PartitionId partition, ResourceAssignment prevAssignment, List<ParticipantId> liveParticipants, Map<ParticipantId, State> replicaMap, ParticipantId currentParticipant) {
        if (null != prevAssignment) {
            Map<ParticipantId, State> previousReplicaMap = prevAssignment.getReplicaMap(partition);
            for (Entry<ParticipantId, State> entry : previousReplicaMap.entrySet()) {
                if (!entry.equals(currentParticipant) && liveParticipants.contains(entry.getKey())) {
                    replicaMap.put(entry.getKey(), State.from("DROPPED"));
                }
            }
        }
    }

    public static Map<String, CompositeKeys> getHandlerToNSPreferenceMap() {
        return handlerToNSPreference;
    }
}
