package com.integral.fxiapi.messages;

import com.integral.system.configuration.IdcMBean;

public interface APIStreamConfigMBean extends IdcMBean {
    public static final String APISTREAM_ENABLED = "Idc.Unity.Enabled";
    public static final String APISTREAM_MQURL = "Idc.Unity.AMQP.URL";
    public static final String APISTREAM_MQVHOST = "Idc.Unity.AMQP.VirtualHost";
    public static final String APISTREAM_MQUSERNAME = "Idc.Unity.AMQP.UserName";
    public static final String APISTREAM_MQPASSWORD = "Idc.Unity.AMQP.Password";
    public static final String APISTREAM_MQEXCHANGE_TYPE = "Idc.Unity.AMQP.ExchangeType";
    public static final String APISTREAM_PUBLISHRATE = "Idc.Unity.AMQP.PublishRate";
    public static final String APISTREAM_PUBLISHDELAY = "Idc.Unity.AMQP.PublishDelay";
    public static final String APISTREAM_THREADCOUNT =  "Idc.Unity.ThreadCount";
    public static final String API_STREAM_RATE_PUBLISH_MULTICAST = "Idc.Unity.Rate.Multicast.Enabled";
    public static final String API_MULTICAST_SOCKET_ADDRESS = "Idc.Unity.Rate.Multicast.Socket.Address";
    public static final String API_MULTICAST_SOCKET_PORT = "Idc.Unity.Rate.Multicast.Socket.Port";
    		
    
    public static final String APISTREAM_AMQ_EXCHANGE_NAME = "Idc.Unity.AMQP.ExchangeName";
    
    public static final String APISTREAM_ENABLE_ALL = "Idc.Unity.Enable.All";

    public static final String WEBSOCKET_EMBEDDED_MODE = "Idc.Unity.EmbeddedMode";
    
    /**
     * Checks if the streaming is from Unity server for the given Org
     * @param orgName
     * @return
     */
    public boolean isAPIStreamEnabled(String orgName);
    
    public String getAPIStreamMQUrl();
    
    public String getAPIStreamMQUserName();
    
    public String getAPIStreamMQPassword();
    
    public String getAPIStreamMQVHost();
    
    public String getAPIStreamMQExchangeType();
    
    public long getAPIStreamPublishDelay();
    
    public long getAPIStreamPublishRate();
    
    public int getAPIStreamThreadCount();
    
    public String getAPIStreamMQExchangeName();
    
    public boolean isRateOnMulticastEnabled();
    
    String getMulticastSocketAddress();
    
    int getMulticastSocketPort();
    
    boolean isAPIStreamEnabledForAll();

    boolean isEmbeddedMode();
    
}
