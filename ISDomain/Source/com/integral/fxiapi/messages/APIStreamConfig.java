package com.integral.fxiapi.messages;

import java.util.Hashtable;
import java.util.Map;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.IdcMBeanC;

@SuppressWarnings("serial")
public class APIStreamConfig extends IdcMBeanC implements APIStreamConfigMBean {
	private final static APIStreamConfig apiStreamConfig;
	private Map<String, Boolean> apiStreamEnabledMap = new Hashtable<String, Boolean>();
	private String apiStreamMQUrl;
	private String apiStreamMQVhost;
	private String apiStreamMQUserName;
	private String apiStreamMQPassword;
	private String apiStreamMQExchangeType;
	private long apiStreamPublishDelay;
	private long apiStreamPublishRate;
	private int apiStreamThreadCount;
	private boolean isRateOnMulticastEnabled;
	private String rateMulticastAddress;
	private int rateMulticastPort;

	private Log log = LogFactory.getLog(APIStreamConfig.class);

	private String apiStreamMQExchangeName;
	private boolean isAPIStreamEnabledAll;
	private boolean isEmbeddedMode;

	private APIStreamConfig() {
		super("com/integral/spaces/APIStreamConfig");
	}

	static {
		apiStreamConfig = new APIStreamConfig();
		apiStreamConfig.initialize();
	}

	@Override
	public void initialize() {
		super.initialize();
		apiStreamEnabledMap = initSingleSuffixBooleanPropertyMap(
				APISTREAM_ENABLED, null);
		apiStreamMQUrl = getStringProperty(APISTREAM_MQURL, null);
		apiStreamMQVhost = getStringProperty(APISTREAM_MQVHOST, null);
		apiStreamMQUserName = getStringProperty(APISTREAM_MQUSERNAME,
				"integral");
		apiStreamMQPassword = getStringProperty(APISTREAM_MQPASSWORD,
				"integral");
		apiStreamMQExchangeType = getStringProperty(APISTREAM_MQEXCHANGE_TYPE,
				null);
		apiStreamMQExchangeName = getStringProperty(
				APISTREAM_AMQ_EXCHANGE_NAME, "Unity");
		isRateOnMulticastEnabled = getBooleanProperty(
				API_STREAM_RATE_PUBLISH_MULTICAST, true);

		// The ThreadCount is configurable. Default value is 1
		apiStreamThreadCount = getIntProperty(APISTREAM_THREADCOUNT, 1);

		// The PublishRate is configurable. Default value is 10 ms
		apiStreamPublishRate = getLongProperty(APISTREAM_PUBLISHRATE, 10);

		// The PublishDelay is configurable. Default value is 3000 ms
		apiStreamPublishDelay = getLongProperty(APISTREAM_PUBLISHDELAY, 3000);

		rateMulticastAddress = getStringProperty(API_MULTICAST_SOCKET_ADDRESS,
				"***********");
		rateMulticastPort = getIntProperty(API_MULTICAST_SOCKET_PORT, 31913);
		
		isAPIStreamEnabledAll = getBooleanProperty(
				APISTREAM_ENABLE_ALL, false);

		isEmbeddedMode = getBooleanProperty(WEBSOCKET_EMBEDDED_MODE, false);
	}

	@Override
	public String getMBeanName() {
		String clz = this.getClass().getName();
		return clz.substring(clz.lastIndexOf('.') + 1);
	}

	@Override
	public String getMBeanDescription() {
		return "APIStreamReader configuration";
	}

	@Override
	public String getMBeanType() {
		return "SystemConfig";
	}

	@Override
	public void setProperty(String key, String aValue, int scope,
			String oldValue) {
		log.info("APIStreamConfig property update - Property: " + key
				+ " Old value: " + oldValue + " New value : " + aValue);
		super.setProperty(key, aValue, scope, oldValue);
	}

	public static APIStreamConfig getInstance() {
		return apiStreamConfig;
	}

	public boolean isAPIStreamEnabled(String orgName) {
		if (orgName != null) {
			Boolean res = apiStreamEnabledMap.get(orgName);
			if (res != null) {
				return res;
			}
			// check if Unity is enabled for all the orgs at the OA level
			return isAPIStreamEnabledAll;
		}
		return false;
	}

	public String getAPIStreamMQUrl() {
		return apiStreamMQUrl;
	}

	public String getAPIStreamMQUserName() {
		return apiStreamMQUserName;
	}

	public String getAPIStreamMQPassword() {
		return apiStreamMQPassword;
	}

	public String getAPIStreamMQVHost() {
		return apiStreamMQVhost;
	}

	public String getAPIStreamMQExchangeType() {
		return apiStreamMQExchangeType;
	}

	public long getAPIStreamPublishDelay() {
		return apiStreamPublishDelay;
	}

	public long getAPIStreamPublishRate() {
		return apiStreamPublishRate;
	}

	public int getAPIStreamThreadCount() {
		return apiStreamThreadCount;
	}

	/*
	 * Returns whether or not APIStreamReaderService can be enabled. Returns
	 * TRUE when the configuration flag is set to true AND the client is HTML.
	 */
	public boolean isAPIStreamEnabled(String orgName, String clientName) {
		boolean enabled = false;
		if (isAPIStreamEnabled(orgName) && clientName != null
				&& clientName.equals("HTML")) {
			enabled = true;
		}
		return enabled;
	}

	@Override
	public String getAPIStreamMQExchangeName() {
		return apiStreamMQExchangeName;
	}

	@Override
	public boolean isRateOnMulticastEnabled() {
		return isRateOnMulticastEnabled;
	}

	@Override
	public int getMulticastSocketPort() {
		return rateMulticastPort;
	}

	@Override
	public String getMulticastSocketAddress() {
		return rateMulticastAddress;
	}

	@Override
	public boolean isAPIStreamEnabledForAll() {
		return isAPIStreamEnabledAll;
	}

	public boolean isEmbeddedMode(){
		return isEmbeddedMode;
	}
}
