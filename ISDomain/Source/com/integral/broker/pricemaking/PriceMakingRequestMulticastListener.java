package com.integral.broker.pricemaking;

import com.google.gson.Gson;
import com.integral.broker.skew.SkewServiceC;
import com.integral.broker.skew.SkewServiceFactory;
import com.integral.is.configuration.ISCommonConfigFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.marketmaking.MarketMakingSkewRequest;
import com.integral.organization.MulticastAddress;
import com.integral.pipeline.metrics.Metrics;
import com.integral.pipeline.metrics.MetricsManager;
import com.integral.transport.multicast.MulticastAddressPoolService;
import org.eclipse.jetty.util.ConcurrentHashSet;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketTimeoutException;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class PriceMakingRequestMulticastListener {
    private static PriceMakingRequestMulticastListener instance;
    private static final Log log = LogFactory.getLog(PriceMakingRequestMulticastListener.class);
    private static final String MULTICAST_ADDRESS_KEY = "MarketMakingRequestService";
    private static final int MTU_SIZE = 1500;
    private static final int SOCKET_TIMEOUT = 30000;
    private MulticastSocket socket;
    private Thread listenerThread;
    private Set<String> brokers = new ConcurrentHashSet<String>();

    /**
     * singleton get instance method
     */
    public static PriceMakingRequestMulticastListener getInstance(){
        if(instance == null){
            synchronized (PriceMakingRequestMulticastListener.class){
                if(instance == null){
                    instance = new PriceMakingRequestMulticastListener();
                }
            }
        }
        return instance;
    }

    private PriceMakingRequestMulticastListener(){
        int port = ISCommonConfigFactory.getISCommonMBean().getBrokerSkewServiceMulticastPort();
        try {
            this.socket = new MulticastSocket(port);
            socket.setSoTimeout(SOCKET_TIMEOUT);
            listenerThread = new Thread(new Listener(), "PriceMakingRequestMulticastListener");
            listenerThread.setDaemon(true);
            listenerThread.start();
            log.info("PriceMakingRequestMulticastListener started with port " + port);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void addBroker(String brokerName){
        try {
            MulticastAddress multicastAddress = MulticastAddressPoolService.getInstance().createMulticastAddress(brokerName, MULTICAST_ADDRESS_KEY);
            InetAddress socketAddress = InetAddress.getByName(multicastAddress.getMulitcastAddress());
            if(!brokers.contains(brokerName)){
                socket.joinGroup(socketAddress);
                brokers.add(brokerName);
                log.info("PriceMakingRequestMulticastListener added broker " + brokerName + ", with address " + socketAddress);
            }else {
                log.info("PriceMakingRequestMulticastListener already added broker " + brokerName + ", with address " + socketAddress);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private class Listener implements Runnable{
        PriceMakingRequestMulticastListenerMetrics metrics = new PriceMakingRequestMulticastListenerMetrics();
        public Listener(){
            MetricsManager.instance().register(metrics);
        }
        @Override
        public void run() {
            log.info("PriceMakingRequestMulticastListener started listener thread with packet size " + MTU_SIZE);
            byte[] buffer = new byte[MTU_SIZE];
            Gson gson = new Gson();
            while(true){
                try{
                    DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                    try{
                        socket.receive(packet);
                    }catch (SocketTimeoutException e){
                        log.info("PriceMakingRequestMulticastListener.run:: no message for " + SOCKET_TIMEOUT + " milliseconds");
                        continue;
                    }
                    String jsonStr = new String(packet.getData(), packet.getOffset(), packet.getLength());
                    log.info("PriceMakingRequestMulticastListener received message " + jsonStr);
                    MarketMakingSkewRequest skewRequest = gson.fromJson(jsonStr, MarketMakingSkewRequest.class);
                    metrics.addSkewCount(skewRequest.getOrg(), skewRequest.getSymbol(), skewRequest.getStream(), skewRequest.getAction());
                    SkewServiceC skewService = (SkewServiceC) SkewServiceFactory.getSkewService(skewRequest.getOrg());
                    if("SET".equals(skewRequest.getAction())){
                        double bidSkew = skewRequest.getBidSkew() != null ? skewRequest.getBidSkew() : 0;
                        double offerSkew = skewRequest.getOfferSkew() != null ? skewRequest.getOfferSkew() : 0;
                        Integer ttl = skewRequest.getTtl() != null ? skewRequest.getTtl() * 1000 : null;
                        skewService.updateAsymmSkewAuto(skewRequest.getSymbol(), bidSkew, offerSkew, ttl, skewRequest.getStream());
                    }else if("RESET".equals(skewRequest.getAction())){
                        skewService.resetSkew(skewRequest.getSymbol(), skewRequest.getStream());
                    }else{
                        log.error("PriceMakingRequestMulticastListener: Unknown action " + skewRequest);
                    }
                }catch (Throwable e){
                    log.error("PriceMakingRequestMulticastListener: Exception during listening for Skew Messages", e);
                }
            }
        }
    }

    private static class PriceMakingRequestMulticastListenerMetrics implements Metrics {
        private final ConcurrentHashMap<String, Long> skewCounts = new ConcurrentHashMap<String, Long>();

        public void addSkewCount(String org, String symbol, String stream, String action){
            String key = getKey(org, symbol, stream, action);
            skewCounts.putIfAbsent(key, 0L);
            Long previousCount = skewCounts.get(key);
            skewCounts.put(key, previousCount + 1);
        }

        @Override
        public StringBuilder report() {
            //return the contents of the skewCounts map
            StringBuilder sb = new StringBuilder("SkewUpdates: ");
            sb.append(skewCounts);
            skewCounts.clear();
            return sb;
        }
        private String getKey(String org, String symbol, String stream, String action){
            String key = action + ":" + org + ":" + symbol;
            if(stream != null) key += ":" + stream;
            return key;
        }
    }
}
