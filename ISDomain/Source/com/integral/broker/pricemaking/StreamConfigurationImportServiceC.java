package com.integral.broker.pricemaking;

import com.integral.admin.services.org.pricemaking.PriceMakingService;
import com.integral.admin.services.org.pricemaking.impl.*;
import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.Stream;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.MessageFactory;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.session.IdcSessionManager;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.util.*;

public class StreamConfigurationImportServiceC {

    private static final PriceMakingService PRICE_MAKING_SERVICE = PriceMakingServiceImpl.getInstance ();

    protected static Log log = LogFactory.getLog ( StreamConfigurationImportServiceC.class );
    private boolean updateConfigIfExists = false;

    public TreeMap<Integer, WorkflowMessage> persist (List<StreamConfigurationImportTemplate> configsTemplateList, String orgName, boolean updateConfigIfExists)
    {
        TreeMap<Integer, WorkflowMessage> failedWorkflowMessagesMap = new TreeMap<Integer, WorkflowMessage> ();
        int lineNo = -1;
        try
        {
            String ccyPairName = null;

            for ( StreamConfigurationImportTemplate cfgTemplate:configsTemplateList)
            {
                WorkflowMessage workflowMessage = MessageFactory.newWorkflowMessage ();
                workflowMessage.setStatus ( MessageStatus.SUCCESS );
                try
                {
                    String brokerName = cfgTemplate.getBroker ().trim ();
                    String streamName = cfgTemplate.getStream ().trim ();
                    Organization broker = OrganizationUtil.getOrganization ( brokerName );
                    lineNo = cfgTemplate.getLineNumber();
                    if ( broker == null || !broker.isBroker () )
                    {
                        log.info ( "Broker org " + cfgTemplate.getCcyPair () + " is not found. broker=" + brokerName );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Please specify a valid broker organization. broker=" + brokerName );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }
                    else if ( !broker.isActive () )
                    {
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "This broker is not Active Broker Name = " + brokerName + "can't proceed with the Configuration" );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }
                    else if(!brokerName.equals(orgName)){
                        log.info("Org name mismatch. Allowed Org: "+ orgName + " Import template:"+ cfgTemplate.toString());
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Import is only available for broker=" + orgName);
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }
                    User brokerUser = broker.getDefaultDealingUser ();
                    if ( brokerUser == null )
                    {
                        log.info ( "Broker org default user" + cfgTemplate.getCcyPair () + " is not found. broker=" + brokerName );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Broker org has no default dealing user set. broker=" + brokerName );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }
                    IdcUtilC.setSessionContextUser ( brokerUser );
                    IdcSessionManager.getInstance ().setTransaction ( null );

                    ccyPairName = cfgTemplate.getCcyPair ().trim ();
                    CurrencyPair currencyPair = CurrencyFactory.getCurrencyPairFromString ( ccyPairName );
                    if ( currencyPair == null )
                    {
                        log.info ( "Currency pair :: " + cfgTemplate.getCcyPair () + " is not found. configuration=" + cfgTemplate.getConfigName () );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Please specify a valid currency pair. currency pair=" + ccyPairName );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }

                    String newConfigName = currencyPair.getBaseCurrency ().getShortName () + currencyPair.getVariableCurrency ().getShortName ();
                    BrokerOrganizationFunction bof = broker.getBrokerOrganizationFunction ();
                    Stream stream = bof.getStream ( streamName );
                    if ( stream == null )
                    {
                        log.info ( "No Stream found with name=" + streamName + ",brokerOrg=" + brokerName );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Please specify a valid stream. stream=" + streamName );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }

                    stream = ( Stream ) IdcUtilC.refreshObject ( stream );
                    Configuration existingConfig = null;
                    boolean newConfigExists = false;
                    if ( stream != null && stream.getConfigurations ().size () > 0 )
                    {
                        Collection<Configuration> cfgLists = stream.getConfigurations ();
                        for ( Configuration cfg : cfgLists )
                        {
                            if ( !cfg.isSynthetic () && cfg.getShortName ().equals ( cfgTemplate.getConfigName () ) )
                            {
                                if ( existingConfig == null )
                                {
                                    existingConfig = cfg;
                                }
                            }
                            if ( cfg.getShortName ().equals ( newConfigName ) )
                            {
                                newConfigExists = true;
                            }
                        }
                    }

                    if ( existingConfig == null )
                    {
                        log.info ( "Stream :: " + streamName + " doesn't have the specified configuration=" + cfgTemplate.getConfigName () + ",stream=" + stream );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Please specify a valid configuration name. source config=" + cfgTemplate.getConfigName () );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }

                    if ( existingConfig.isSynthetic () || existingConfig.isMarketMaker () )
                    {
                        log.info ( "Only direct configuration is supported for copying. config=" + cfgTemplate.getConfigName () + ",stream=" + stream );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Please specify only direct configuration as source configuration. source config=" + cfgTemplate.getConfigName () );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }

                    if (existingConfig.isLiquidityGroupOnly ()
                            || existingConfig.getEspRiskManagementProfile () != null || existingConfig.getRfsRiskManagementProfile() != null
                            || existingConfig.getEspSpreadManagementProfile () != null || existingConfig.getRfsSpreadManagementProfile () != null)
                    {
                        log.info ("Only configuration without Management Profiles (Liquidity/Risk/Spread) is supported for copying. config="
                                + cfgTemplate.getConfigName () + ",stream=" + stream.getShortName());
                        workflowMessage.setStatus(MessageStatus.FAILURE);
                        workflowMessage.addError("Please specify configuration without any Management Profiles (Liquidity/Risk/Spread) as source configuration. source config="
                                + existingConfig.getShortName());
                        failedWorkflowMessagesMap.put(lineNo, workflowMessage);
                        continue;
                    }

                    if ( newConfigExists && !updateConfigIfExists )
                    {
                        log.info ( "Config name already exists with name=" + newConfigName + " in stream=" + streamName );
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Configuration already exists with name=" + newConfigName );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }

                    StreamConfigurationParam existingCfgParam = PRICE_MAKING_SERVICE.getStreamConfig ( broker, cfgTemplate.getStream ().trim (), cfgTemplate.getConfigName () );
                    existingCfgParam.setAction ( newConfigExists ? Action.Modify : Action.Add );
                    if ( newConfigExists )
                    {
                        existingCfgParam.setSortOrder ( null );
                        existingCfgParam.setSpreadRFS ( null );
                    }
                    existingCfgParam.setConfigurationName ( newConfigName );

                    // update the currency pair
                    List<String> ccyPairList = new ArrayList<String> ();
                    ccyPairList.add ( currencyPair.getName () );
                    existingCfgParam.getCurrencyPairGroup ().setIncludedCurrencyPairs ( ccyPairList );
                    existingCfgParam.getCurrencyPairGroup ().setCurrencyGroup ( null );
                    existingCfgParam.getCurrencyPairGroup ().setCurrencyPairs ( null );
                    existingCfgParam.getCurrencyPairGroup ().setExcludedCurrencyPairs ( null );

                    ProvidersParam priceProviders = existingCfgParam.getPriceProviders ();
                    List<String> providers = new ArrayList<String> ();
                    String[] providerStrings = cfgTemplate.getProvider ().split ( ";" );
                    for ( int i = 0; i < providerStrings.length; i++ )
                    {
                        boolean isAdded = false;
                        Organization org = OrganizationUtil.getOrganization ( providerStrings[i] );
                        if ( org != null )
                        {
                            Collection<Organization> orgsList = org.getRelatedOrganizations ( "LP_to_FI" );

                            for ( Organization organization : orgsList )
                            {
                                if ( organization.getShortName ().equals ( broker.getShortName () ) )
                                {
                                    isAdded = true;
                                    providers.add ( providerStrings[i] );
                                }
                            }
                            if ( !isAdded )
                            {
                                workflowMessage.setStatus ( MessageStatus.FAILURE );
                                workflowMessage.addError ( "Provider Organization is not related to this broker org @ line no :: " + lineNo + " provider Name :: " + org.getShortName () );
                                failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                                continue;
                            }
                        }
                        else
                        {
                            workflowMessage.setStatus ( MessageStatus.FAILURE );
                            workflowMessage.addError ( "Provider Organization is null @ line no :: " + lineNo + " provider Name :: " + providerStrings[i] );
                            failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                            continue;
                        }

                    }
                    priceProviders.setProviders ( providers );
                    existingCfgParam.setPriceProviders ( priceProviders );
                    existingCfgParam.setOrderProviders ( priceProviders );
                    SpreadESPParam espParam = existingCfgParam.getSpreadESP ();
                    List<SpreadESPTierParam> tierParams = espParam.getTierParams ();
                    //TierSpreadImportTemplate tierSpreadImportTemplate = cfgTemplate.getTierSpreads().first();
                    SpreadESPTierParam tierParam;
                    try
                    {
                        int index =0;
                        Iterator<TierSpreadImportTemplate> tierSpreadIterator = cfgTemplate.getTierSpreads().iterator();
                        while (tierSpreadIterator.hasNext()) {
                            TierSpreadImportTemplate tierSpreadImportTemplate = tierSpreadIterator.next();
                            if (tierSpreadImportTemplate == null) {
                                continue;
                            }
                            if (index < tierParams.size())
                                tierParam = tierParams.get(index);
                            else {
                                tierParam = new SpreadESPTierParam();
                                tierParams.add(tierParam);
                            }

                            tierParam.setBidLimit(tierSpreadImportTemplate.getBidLimit());
                            tierParam.setOfferLimit(tierSpreadImportTemplate.getOfferLimit());

                            tierParam.setSpreadFixedPreBid(Double.parseDouble(tierSpreadImportTemplate.getBidMarkUp()));
                            tierParam.setSpreadFixedPreOffer(Double.parseDouble(tierSpreadImportTemplate.getAskMarkUp()));
                            tierParam.setSpreadMinimum(Double.parseDouble(tierSpreadImportTemplate.getBidMinSpread()));
                            tierParam.setSpreadMaximum(Double.parseDouble(tierSpreadImportTemplate.getBidMaxSpread()));
                            index++;
                        }

                        //remove extra rows
                        int newTierSize = cfgTemplate.getTierSpreads().size();
                        while(tierParams.size() > newTierSize){
                            tierParams.remove(tierParams.size()-1);
                        }
                    }
                    catch ( Exception e )
                    {
                        workflowMessage.setStatus ( MessageStatus.FAILURE );
                        workflowMessage.addError ( "Bid/Ask Markups or Min/Max Spreads are defined in wrong format @ :: " + lineNo );
                        failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                        continue;
                    }
                    List<ExecutionRuleConditionESPParam> espERParams = existingCfgParam.getExecution ().getEspExecution ().getExecutionRuleConditionESPParams ();
                    for ( ExecutionRuleConditionESPParam executionRuleConditionESPParam : espERParams )
                    {
                        if ( executionRuleConditionESPParam.getExecutionMethodParam ().getMarket () == null )
                        {
                            executionRuleConditionESPParam.getExecutionMethodParam ().setMarket ( false );
                        }
                    }

                    List<ExecutionRuleConditionRFSParam> rfsERParams = existingCfgParam.getExecution ().getRfsExecution ().getExecutionRuleConditionRFSParams ();
                    for ( ExecutionRuleConditionRFSParam executionRuleConditionRFSParam : rfsERParams )
                    {
                        if ( executionRuleConditionRFSParam.getExecutionMethodParam ().getMarket () == null )
                        {
                            executionRuleConditionRFSParam.getExecutionMethodParam ().setMarket ( false );
                        }
                    }

                    PRICE_MAKING_SERVICE.setStreamConfig ( broker, stream.getShortName (), existingCfgParam );
                    log.info ( "ConfigCSVLoader.persist - Added new configuration for currency pair=" + ccyPairName + ",stream=" + streamName + ",brokerOrg=" + brokerName );
                }
                catch ( Exception e )
                {
                    log.warn ( "Error: ConfigCSVLoader.persist - Exception while persisting the configuration for config =" + cfgTemplate);
                    workflowMessage.setStatus ( MessageStatus.FAILURE );
                    workflowMessage.addError ( "Exception happened during creating/updating the configuration." );
                    failedWorkflowMessagesMap.put ( lineNo, workflowMessage );
                    continue;
                }
            }
        }
        finally
        {

        }
        return failedWorkflowMessagesMap;
    }

}
