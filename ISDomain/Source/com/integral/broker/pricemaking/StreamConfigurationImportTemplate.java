package com.integral.broker.pricemaking;

import java.util.SortedSet;
import java.util.TreeSet;

public class StreamConfigurationImportTemplate {

    String broker;
    String stream;
    String configName;
    String ccyPair;
    String provider;
    int lineNumber;
    SortedSet<TierSpreadImportTemplate> tierSpreads = new TreeSet<TierSpreadImportTemplate>();

    public StreamConfigurationImportTemplate(){

    }
    public StreamConfigurationImportTemplate(String broker, String stream, String configName , String ccyPair, String provider, SortedSet<TierSpreadImportTemplate> tierSpreadSet) {

        this.broker = broker;
        this.stream = stream;
        this.configName=configName;
        this.ccyPair = ccyPair;
        tierSpreads.addAll(tierSpreadSet);
        this.provider = provider;
    }

    public int getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(int lineNumber){
            this.lineNumber = lineNumber;
    }

    public String getBroker() {
        return broker;
    }

    public void setBroker(String broker) {
        this.broker = broker;
    }

    public String getStream() {
        return stream;
    }

    public void setStream(String stream) {
        this.stream = stream;
    }

    public String getCcyPair() {
        return ccyPair;
    }

    public void setCcyPair(String ccyPair) {
        this.ccyPair = ccyPair;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider){
        this.provider=provider;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }
    public SortedSet<TierSpreadImportTemplate> getTierSpreads() {
        return tierSpreads;
    }

    public void setTierSpreads(SortedSet<TierSpreadImportTemplate> tierSpreads) {
        this.tierSpreads = tierSpreads;

    }

    @Override
    public String toString() {
        return "StreamConfigImportTemplate: { broker:" + broker + ", stream:" + stream + ", ccyPair:" + ccyPair + ", tierSpreads:["
                + tierSpreads + "], provider:"
                + provider +", configName:"
                +configName + "}";
    }
}
