package com.integral.broker.pricemaking;



public class TierSpreadImportTemplate implements Comparable<TierSpreadImportTemplate>{

    String bidMarkUp;
    String askMarkUp;
    String bidMinSpread;
    String bidMaxSpread;
    Double bidLimit;
    Double offerLimit;

    public String getAskMarkUp() {
        return askMarkUp;
    }

    public void setAskMarkUp(String askMarkUp) {
        this.askMarkUp = askMarkUp;
    }

    public String getBidMinSpread() {
        return bidMinSpread;
    }

    public void setBidMinSpread(String bidMinSpread) {
        this.bidMinSpread = bidMinSpread;
    }

    public String getBidMaxSpread() {
        return bidMaxSpread;
    }

    public void setBidMaxSpread(String bidMaxSpread) {
        this.bidMaxSpread = bidMaxSpread;
    }

    public Double getBidLimit() {
        return bidLimit;
    }

    public void setBidLimit(Double bidLimit) {
        this.bidLimit = bidLimit;
    }

    public Double getOfferLimit() {
        return offerLimit;
    }

    public void setOfferLimit(Double offerLimit) {
        this.offerLimit = offerLimit;
    }

    public String getBidMarkUp() {
        return bidMarkUp;
    }

    public void setBidMarkUp(String bidMarkUp) {
        this.bidMarkUp = bidMarkUp;
    }

    @Override
    public int compareTo(TierSpreadImportTemplate other) {
       double newBidLimit = (bidLimit == null)? Double.MAX_VALUE : bidLimit.doubleValue();
       double otherBidLimit =(other.bidLimit == null)? Double.MAX_VALUE : other.bidLimit.doubleValue();
        if (newBidLimit < otherBidLimit ) {
            return -1;
        }
        return newBidLimit == otherBidLimit ? 0 : 1;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        TierSpreadImportTemplate other = (TierSpreadImportTemplate) obj;

        if ( bidLimit == other.bidLimit &&  offerLimit == other.offerLimit) {
            return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return "TierSpreadImportTemplate{" +
                "bidLimit=" + bidLimit +
                ", offerLimit=" + offerLimit +
                ", bidMarkUp=" + bidMarkUp +
                ", askMarkUp=" + askMarkUp +
                ", bidMinSpread=" + bidMinSpread +
                ", bidMaxSpread=" + bidMaxSpread +
                '}';
    }

}
