package com.integral.broker;

import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.messaging.*;
import com.integral.messaging.config.MessagingConfiguration;
import com.integral.rfqticker.CustomerSpreadDetail;
import com.integral.rfqticker.RFQBroadcastCache;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.configuration.ServerMBean;
import com.integral.system.runtime.ServerRuntimeMBean;
import com.integral.util.MathUtilC;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;


public class ActiveRFSCustomerSpreadListenerC extends DefaultMessageListener {
    private static final Log log = LogFactory.getLog(ActiveRFSCustomerSpreadListenerC.class);
    private static final ServerMBean _serverMBean = ConfigurationFactory.getServerMBean();

    public static void enableActiveRFSCustomerSpreadListener(ActiveRFSCustomerSpreadListenerC listener) {
        // listen to ActiveRFS Customer Spread message if broker server.
        if (ConfigurationFactory.getServerMBean().isBrokerAdaptor()) {
            try {
                String exchangeName = ServerRuntimeMBean.CUSTOMER_SPREAD_EXCHANGE;
                String queueName = MessagingConfiguration.getInstance().getUserDBPrefix() + "_" + _serverMBean.getVirtualServerName() + "_" + exchangeName;
                log.info("ARFSCSL.enableActiveRFSCustomerSpreadListener : set up RMQ activeRFS customer spread listener=" + listener
                        + ",exchange=" + exchangeName);
                setupMessageListener(listener, queueName, exchangeName);
            } catch (Exception e) {
                log.error("ARFSCSL.enableActiveRFSCustomerSpreadListener : activeRFS customer spread message listener failed to start.", e);
            }
        }
    }

    public static void setupMessageListener(com.integral.messaging.MessageListener listener, String queueName, String exchangeName) throws MessagingException {
        MessageReceiver receiver = MessageReceiverFactory.newMessageReceiver(exchangeName, queueName, false, true, true, false, listener);
        receiver.addBinding(_serverMBean.getVirtualServerName());
    }

    @Override
    public void onMessage(final RMQMessage message) {
        try {
            ActiveRFSNotificationManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    //todo handle message
                    log.info("ARFSCSL: received message from RMQ= " + message);
                    Object payload = message.getPayload();
                    if (payload != null && payload instanceof CustomerSpreadDetail) {
                        try {
                            CustomerSpreadDetail customerSpreadDetail = (CustomerSpreadDetail) payload;
                            CustomerSpreadDetail customerSpreadDetailFromCache = RFQBroadcastCache.getInstance().
                                    getFromCustomerSpreadDetailCache(customerSpreadDetail.getId());
                            log.info("ARFSCSL: received message from RMQ CustomerSpreadDetail: " + customerSpreadDetail+", customerSpreadDetailFromCache: "+customerSpreadDetailFromCache);
                            if (customerSpreadDetailFromCache != null) {
                                if (customerSpreadDetail.getBidCustomerSpread() != 0.0) {
                                    customerSpreadDetailFromCache.setBidCustomerSpread(MathUtilC.add(customerSpreadDetail.getBidCustomerSpread(), customerSpreadDetailFromCache.getBidCustomerSpread()));
                                }
                                if (customerSpreadDetail.getBidCustomerForwardSpread() != 0.0) {
                                    customerSpreadDetailFromCache.setBidCustomerForwardSpread(MathUtilC.add(customerSpreadDetail.getBidCustomerForwardSpread(), customerSpreadDetailFromCache.getBidCustomerForwardSpread()));
                                }
                                if (customerSpreadDetail.getBidCustomerFarForwardSpread() != 0.0) {
                                    customerSpreadDetailFromCache.setBidCustomerFarForwardSpread(MathUtilC.add(customerSpreadDetail.getBidCustomerFarForwardSpread(), customerSpreadDetailFromCache.getBidCustomerFarForwardSpread()));
                                }

                                if (customerSpreadDetail.getOfferCustomerSpread() != 0.0) {
                                    customerSpreadDetailFromCache.setOfferCustomerSpread(MathUtilC.add(customerSpreadDetail.getOfferCustomerSpread(), customerSpreadDetailFromCache.getOfferCustomerSpread()));
                                }
                                if (customerSpreadDetail.getOfferCustomerForwardSpread() != 0.0) {
                                    customerSpreadDetailFromCache.setOfferCustomerForwardSpread(MathUtilC.add(customerSpreadDetail.getOfferCustomerForwardSpread(), customerSpreadDetailFromCache.getOfferCustomerForwardSpread()));
                                }
                                if (customerSpreadDetail.getOfferCustomerFarForwardSpread() != 0.0) {
                                    customerSpreadDetailFromCache.setOfferCustomerFarForwardSpread(MathUtilC.add(customerSpreadDetail.getOfferCustomerFarForwardSpread(), customerSpreadDetailFromCache.getOfferCustomerFarForwardSpread()));
                                }
                            } else {
                                RFQBroadcastCache.getInstance().addToCustomerSpreadDetailCache(customerSpreadDetail.getId(), customerSpreadDetail);
                            }
                        } catch (Exception e) {
                            log.error("ARFSCSL: Exception in processing Subscription Request", e);
                        }
                    } else {
                        log.warn("ARFSCSL: Unknown message rkey=" + message.getRoutingKey() + ", msg=" + message.getPayload());
                    }
                }
            });
        } catch (Exception e) {
            log.error("ARFSCSL.onMessage - exception while handling the activeRFS customer spread message. msg=" + message);
        }

        try {
            message.ack();
        } catch (MessagingException e) {
            log.error("ARFSCSL.onMessage - Exception in sending the ack for message:" + message.getPayload()
                    + ":routingKey=" + message.getRoutingKey(), e);
        }
    }
}

class ActiveRFSNotificationManager {
    private static ActiveRFSNotificationManager activeRFSNotificationManager = new ActiveRFSNotificationManager();

    public static ActiveRFSNotificationManager getInstance() {
        return activeRFSNotificationManager;
    }

    public static ExecutorService executorService = Executors.newFixedThreadPool(5, new ActiveRFSServiceFactory("ActiveRFSSvcThreadGroup"));

    public static void execute(Runnable r) {
        executorService.submit(r);
    }


    private static class ActiveRFSServiceFactory implements ThreadFactory {
        final AtomicInteger threadNumber = new AtomicInteger(1);
        ThreadGroup tg = null;

        public ActiveRFSServiceFactory(String name) {
            tg = new ThreadGroup(name);
        }

        public Thread newThread(Runnable runnable) {
            return new Thread(tg, runnable, "ActiveRFSSvcThread-" + threadNumber.getAndIncrement());
        }
    }
}