// Copyright (c) 2005-2008 Integral Development Corp. All rights reserved.
package com.integral.broker;

import com.integral.admin.utils.organization.OrganizationUtil;
import com.integral.broker.aggregate.BrokerQuoteAggregatorFactory;
import com.integral.broker.aggregate.QuoteAggregatorFactory;
import com.integral.broker.config.*;
import com.integral.broker.executor.PooledExecutor;
import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.ModelFactory;
import com.integral.broker.model.Product;
import com.integral.broker.rfs.RFSHandler;
import com.integral.broker.request.RequestValidator;
import com.integral.broker.request.RequestValidatorC;
import com.integral.broker.request.rfs.RFSRequestValidatorC;
import com.integral.broker.scheduler.SimpleScheduler;
import com.integral.broker.scheduler.SimpleScheduler3C;
import com.integral.finance.counterparty.LegalEntity;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.management.UnSupportedOperationException;
import com.integral.provider.ProviderOrgFunction;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.user.Organization;
import com.integral.util.Factory;

import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Timer;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Provides access to singleton instances of common broker adaptor services.
 *
 * <AUTHOR> Development Corp.
 */
public class BrokerAdaptorFactory extends Factory {

    /*
     * This static field holds onto the instance of BrokerAdaptorFactory
     */
    protected static BrokerAdaptorFactory current;

    /*
     * Instance of the BrokerAdaptorMBean
     */
    private BrokerAdaptorMBean brokerAdaptorMBean;

    private BrokerServerConfig brokerServerConfig;

    protected BrokerCustomConfigurationMBean brokerCustomConfigurationMBean;

    /*
     * Instance of the Broker Organization
     */
    private Organization brokerOrganization;

    /**
     * BrokerOrganizationFunctions
     */
    private ConcurrentMap<Organization, BrokerOrganizationFunction> brokerOrganizationFunctions = new ConcurrentHashMap<Organization, BrokerOrganizationFunction>();

    protected ConcurrentHashMap<String, BrokerAdaptorMBean> brokerAdaptorMBeans = new ConcurrentHashMap<String, BrokerAdaptorMBean>();
    protected ConcurrentHashMap<String, BrokerCustomConfigurationMBean> brokerCustomMBeans = new ConcurrentHashMap<String, BrokerCustomConfigurationMBean>();

    /*
     * Instance of a timer to schedule adaptor tasks
     */
    private final Timer timer;

    private SimpleScheduler tradeExecutionScheduler;

    private RequestValidator espRequestValidator;

    private RequestValidator rfsRequestValidator;

    static {
        BrokerAdaptorFactory.current = new BrokerAdaptorFactory();
    }

    protected BrokerAdaptorFactory(boolean isOrderAdaptor) {
        super();
        this.brokerServerConfig = new BrokerServerConfig();
        timer = new Timer();
    }

    /**
     * Default constructor.
     */
    protected BrokerAdaptorFactory() {
        super();
        this.brokerServerConfig = new BrokerServerConfig();
        timer = new Timer();
        BrokerConfigurationService configurationService = BrokerConfigurationServiceFactory.getBrokerConfigurationService();
        Collection<String> brokerOrgs = configurationService.getDeployedBrokerOrganizationNames();
        if (brokerOrgs != null && !brokerOrgs.isEmpty()) {
            for (String brokerOrg : brokerOrgs) {
                BrokerAdaptor brokerAdaptor = new BrokerAdaptor(brokerOrg);
                if (brokerAdaptorMBeans.putIfAbsent(brokerOrg, brokerAdaptor) == null) {
                    ConfigurationFactory.registerMBean(brokerAdaptor);
                }
                ;
                BrokerCustomConfiguration brokerCustomConfiguration = new BrokerCustomConfiguration(brokerOrg);
                if (brokerCustomMBeans.putIfAbsent(brokerOrg, brokerCustomConfiguration) == null) {
                    ConfigurationFactory.registerMBean(brokerCustomConfiguration);
                }
            }
        }
        /*
            For backward compatibility
         */
        if (brokerAdaptorMBeans.size() == 1) {
            brokerAdaptorMBean = brokerAdaptorMBeans.values().iterator().next();
        }
        if (brokerCustomMBeans.size() == 1) {
            brokerCustomConfigurationMBean = brokerCustomMBeans.values().iterator().next();
        }
    }

    /**
     * Get the singleton instance of the BrokerAdaptorFactory.
     *
     * @return singleton instance of BrokerAdaptorFactory
     */
    public static BrokerAdaptorFactory getInstance() {
        return current;
    }

    protected void setInstance(BrokerAdaptorFactory brokerAdaptorFactory) {
        current = brokerAdaptorFactory;
    }

    @Deprecated
    /**
     * Deprecated for multi-tenant BA support.
     * Use {@link #getBrokerAdaptorMBean(String)} method instead.
     */
    public BrokerAdaptorMBean getBrokerAdaptorMBean() {
        return brokerAdaptorMBean;
    }

    public BrokerServerConfig getBrokerServerConfig() {
        return brokerServerConfig;
    }

    /**
     * @param brokerOrgName shortName of the Broker's organization
     * @return {@link BrokerAdaptorMBean} corresponding to the {@code brokerOrgName}
     */
    public BrokerAdaptorMBean getBrokerAdaptorMBean(String brokerOrgName) {
        return brokerAdaptorMBeans.get(brokerOrgName);
    }

    @Deprecated
    public BrokerCustomConfigurationMBean getBrokerCustomConfigurationMBean() {
        throw new UnsupportedOperationException();
    }

    /**
     * Deprecated for multi-tenant BA support.
     * Use {@link #getBrokerCustomConfigurationMBean(String)} method instead.
     */
    public BrokerCustomConfigurationMBean getBrokerCustomConfigurationMBean(String brokerOrgName) {
        return brokerCustomMBeans.get(brokerOrgName);
    }

    public Timer getTimer() {
        return timer;
    }

    @Deprecated
    private Organization findBrokerOrganization() {
        String orgName = getBrokerAdaptorMBean().getProviderName();
        return ISUtilImpl.getInstance().getOrg(orgName);
    }

    @Deprecated
    public Organization getBrokerOrganization() {
        if (brokerOrganization == null) {
            brokerOrganization = findBrokerOrganization();
        }
        return brokerOrganization;
    }

    @Deprecated
    public BrokerOrganizationFunction getBrokerOrganizationFunction() {
        throw new UnsupportedOperationException();
    }

    public BrokerOrganizationFunction getBrokerOrganizationFunction(Organization organization) {
        BrokerOrganizationFunction brokerOrganizationFunction = organization.getBrokerOrganizationFunction();
        if (brokerOrganizationFunction == null) {
            brokerOrganizationFunction = brokerOrganizationFunctions.get(organization);
            if (brokerOrganizationFunction == null) {
                log.info("BrokerAdaptorfactory.getBrokerOrganizationFunction creating BrokerOrgFunction for " + organization.getShortName());
                brokerOrganizationFunction = createBrokerOrganizationFunction(organization);
                brokerOrganizationFunctions.putIfAbsent(organization, brokerOrganizationFunction);
                brokerOrganizationFunction = brokerOrganizationFunctions.get(organization);
            }
        }
        return brokerOrganizationFunction;
    }

    @Deprecated
    public ProviderOrgFunction getProviderOrganizationFunction() {
        throw new UnSupportedOperationException();
    }

    @Deprecated
    public LegalEntity getBrokerLegalEntity() {
        throw new UnSupportedOperationException();
    }


    public RequestValidator getRequestValidator(String type) {
        if (RequestValidator.QUOTED.equals(type)) {
            if (espRequestValidator == null) {
                espRequestValidator = new RequestValidatorC();
            }
            return espRequestValidator;
        } else if (RequestValidator.RFS.equals(type)) {
            if (rfsRequestValidator == null) {
                rfsRequestValidator = new RFSRequestValidatorC();
            }
            return rfsRequestValidator;
        }
        return null;
    }

    private static BrokerOrganizationFunction createBrokerOrganizationFunction(Organization organization) {
        BrokerOrganizationFunction brokerOrganizationFunction = ModelFactory.getInstance().newBrokerOrganizationFunction();
        brokerOrganizationFunction.setOrderExecutionActive(true);
        brokerOrganizationFunction.setPriceMakingActive(true);
        brokerOrganizationFunction.setOrganization(organization);
        brokerOrganizationFunction.setDisabledProviders(Collections.<Organization>emptyList());
        return brokerOrganizationFunction;
    }

    public QuoteAggregatorFactory newAggregatorFactory(Product product, RFSHandler rfsHandler) {
        return new BrokerQuoteAggregatorFactory(product, rfsHandler);
    }

    public QuoteAggregatorFactory newAggregatorFactory(Product product) {
        return new BrokerQuoteAggregatorFactory(product, null);
    }

    public SimpleScheduler getTradeExecutionScheduler() {
        if (tradeExecutionScheduler == null) {
            synchronized (BrokerAdaptorFactory.class) {
                if (tradeExecutionScheduler == null) {
                    PooledExecutor pooledExecutor =
                            com.integral.broker.configuration.ConfigurationFactory.getInstance()
                                    .getOrderExecutionPooledExecutor();
                    String className = BrokerAdaptorFactory.getInstance().getBrokerServerConfig().getTradeExecutionSchedulerClassName();
                    className = "com.integral.broker.scheduler." + className;
                    try {
                        Class<?> aClass = Class.forName(className);
                        Constructor<?> constructor = aClass.getConstructor(String.class, PooledExecutor.class);
                        tradeExecutionScheduler =
                                (SimpleScheduler) constructor.newInstance("BA-Execution", pooledExecutor);
                    } catch (Exception e) {
                        log.warn("BrokerAdaptorFactory.getTradeExecutionScheduler", e);
                        tradeExecutionScheduler = new SimpleScheduler3C("BA-Execution", pooledExecutor);
                    }
                    tradeExecutionScheduler.start();
                }
            }
        }
        return tradeExecutionScheduler;
    }

    public List<Organization> getBrokerOrganizations() {
        List<Organization> brokerOrgs = new ArrayList<Organization>();
        for (String orgName : brokerAdaptorMBeans.keySet()) {
            Organization organization = OrganizationUtil.getOrganization(orgName);
            brokerOrgs.add(organization);
        }

        return brokerOrgs;
    }

}
