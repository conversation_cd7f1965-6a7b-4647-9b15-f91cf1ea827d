package com.integral.broker;

import com.integral.finance.counterparty.LegalEntity;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.priceProvision.SpotSpreadProfile;
import com.integral.is.oms.Order;
import com.integral.is.priceprovision.PriceProvisionRulesCacheManager;
import com.integral.is.priceprovision.PriceProvisionServicesFactoryC;
import com.integral.is.priceprovision.rules.SpreadRuleParameter;
import com.integral.is.spaces.fx.esp.provision.ProvisionCache;
import com.integral.is.spaces.fx.esp.provision.RelationshipProvision;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Iterator;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 7/27/15
 */
public class HourglassPricingUtil {
    private static final Log log = LogFactory.getLog(HourglassPricingUtil.class);
    protected static HourglassPricingUtil instance = new HourglassPricingUtil();
    public static HourglassPricingUtil getInstance(){
        return instance;
    }

    public boolean isHourglassPricingSupported(Organization fiOrg, CurrencyPair cp, LegalEntity le){
        try {
            if(fiOrg == null || cp == null) return false;
            Organization broker = fiOrg.getBrokerOrganization();
            if(broker == null) return false;
            RelationshipProvision rp = ProvisionCache.getRelationshipProvision(le, broker, cp);
            if(rp == null) return false;
            boolean hourglass = rp.isHourglassPricing();
            StringBuilder sb = new StringBuilder("HourglassPricingUtil.isHourglassPricingSupported:");
            sb.append("isHourglass=").append(hourglass).append(", for fiOrg=").append(fiOrg.getShortName()).
            append(", cp=").append(cp).append(", broker=").append(broker.getShortName());
            if (!hourglass) {
            	// if hour glass is false on broker configuration page then check if spot spread profile applied is marked as hour glass
            	PriceProvisionRulesCacheManager pprc = PriceProvisionServicesFactoryC.getInstance().getCacheManager();
            	if (pprc != null) {
            		SpreadRuleParameter srp = pprc.getSpreadRuleParameter(fiOrg, broker, cp);
            		if (srp != null) {
                		SpotSpreadProfile ssp = srp.getSpotSpreadProfile();
                		if (ssp != null) {
                			hourglass = ssp.isHourGlass();
                			sb.append(", isHourglass on SpotSpreadProfile=").append(hourglass);
                		}
                	}
            	}
            }
            log.info(sb.toString());
            return hourglass;
        } catch (Exception e) {
            log.info("Some exception during checking hourglass setting for fiOrg=" + fiOrg.getShortName() + ", cp=" + cp);
            return false;
        }
    }

    public static void filterNonHourglassQuotes(Collection<Order> orders){
        Iterator<Order> iterator = orders.iterator();
        while (iterator.hasNext()){
            if(!iterator.next().isHourglassPricingSupported()) iterator.remove();
        }
    }
}
