package com.integral.broker.syntheticcross;

import java.util.List;

import com.integral.broker.util.SyntheticCrossUtil;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.DealingPrice;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.is.message.MarketPrice;
import com.integral.is.message.MarketRate;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.util.MathUtilC;

public class DefaultMeldingStrategyC implements MeldingStrategy 
{
	protected Log log = LogFactory.getLog( this.getClass() );
	
	@Override
	public Type getType() 
	{
		return MeldingStrategy.Type.DEFAULT;
	}
	
	@Override
	public void meld(MarketRate marketRate1, MarketRate marketRate2, Quote finalQuote,
			double []foreignCcyPairForwardPoints, double []localCcyPairForwardPoints)
	{
		int hasBaseCurrency = SyntheticCrossUtil.hasCurrency(finalQuote.getCurrencyPair().getBaseCurrency(), marketRate1);
		
		if ( hasBaseCurrency > 0 )
		{
			meld(marketRate1, marketRate2, finalQuote, foreignCcyPairForwardPoints, localCcyPairForwardPoints, true);
		}
		else
		{
			meld(marketRate2, marketRate1, finalQuote, localCcyPairForwardPoints, foreignCcyPairForwardPoints, true);
		}
	}
	/**
	 * Calculates bid and offer rates of synthetic cross currency pair using bid and/or offer 
	 * market rates from foreign and local currency pair.
	 */
	private void meld(MarketRate marketRate1, MarketRate marketRate2, Quote finalQuote, 
			double []forwardPoints1, double []forwardPoints2, boolean dummy)
	{
		/*
		 * 4. Find multipliers from foreign ccyp and local ccyp for both bid & offer
		 * 
		 * 3. Multipliers is rate value if dealt ccy in a ccyp is base currency 
		 *    or inverse of rate if dealt ccy in a ccyp is term currency
		 * 
		 * 2. Rate value lies in a tier which covers corresponding dealt amount
		 * 
		 * 1. Tier side in a market rate is define by dealt currency and the side (bid/offer) 
		 *    for which the rate is calculated in final quote. 
		 */
		List<FXLegDealingPrice> finalBidPrices = finalQuote.getBids();
		List<FXLegDealingPrice> finalOfferPrices = finalQuote.getOffers();
		
		int numOfTiers = finalQuote.getBids().size();
		
		Currency dealtCcyInForeignCcy = finalQuote.getCurrencyPair().getBaseCurrency();
		
		StringBuilder sb = null;
		if ( log.isDebugEnabled() )
		{
			sb = new StringBuilder(256);
			sb.append(finalQuote.getCurrencyPair().getName());
		}
		
		/*
		 * Iterate for each tier of both bid and offer
		 */
		for( int tier = 0; tier < numOfTiers; tier++ )
		{
			FXLegDealingPrice bidPrice = finalBidPrices.get(tier);
			FXLegDealingPrice offerPrice = finalOfferPrices.get(tier);
			double[] finalBidOfferRate = getBidOfferSyncRate(marketRate1, marketRate2, forwardPoints1, forwardPoints2,
					dealtCcyInForeignCcy, sb, bidPrice, offerPrice);
//			finalRate = MathUtil.round(finalRate, fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_FLOOR);
			double finalBaseRate = 0.0; // TODO Any base rate?
			double finalAmt = bidPrice.getDealtAmount();
			double fwdPoints = 0.0;
			
			finalQuote.addBidDealingPrice(tier, "FinalRate", finalQuote.getGUID(), finalQuote.getOrganization().getShortName(), finalBidOfferRate[0], finalBidOfferRate[0], fwdPoints, finalAmt, finalBaseRate, true);
			
			
			   
			/*
			 * Populate final offer rates in finalQuote object
			 */
			
//			finalRate = MathUtil.round(finalRate, fxRateBasis.getSpotPrecision(), BigDecimal.ROUND_CEILING);
			finalBaseRate = 0.0; // TODO Any base rate?
			finalAmt = offerPrice.getDealtAmount();
			fwdPoints = 0.0;
			
			finalQuote.addOfferDealingPrice(tier, "FinalRate", finalQuote.getGUID(), finalQuote.getOrganization().getShortName(), finalBidOfferRate[1], finalBidOfferRate[1], fwdPoints, finalAmt, finalBaseRate, true);
			

			
		
		} // End for loop
		
		if ( log.isDebugEnabled() )
		{
			log.debug("DefaultMeldingStrategyC.meld: " + sb.toString());
		}
	}

	public double[] getBidOfferSyncRate(MarketRate marketRate1, MarketRate marketRate2, double[] forwardPoints1,
			double[] forwardPoints2, Currency dealtCcyInForeignCcy, StringBuilder sb, FXLegDealingPrice bidPrice,
			FXLegDealingPrice offerPrice) {
		/*
		 * Find appropriate tier in foreign ccyp rate for required dealt amount
		 */
		MarketPrice foreignDealtCcyTier = SyntheticCrossUtil.findTier(dealtCcyInForeignCcy, bidPrice.getDealtAmount(), DealingPrice.BID, marketRate1);
		MarketPrice foreignSettleCcyTier = SyntheticCrossUtil.findTier(dealtCcyInForeignCcy, offerPrice.getDealtAmount(), DealingPrice.OFFER, marketRate1);
		
		Currency dealtCcyInLocalCcy = null;
		double foreignBidMultiplier = 0.0;
		double foreignOfferMultiplier = 0.0;
		
		boolean isInverseForeign = false;
		
		/*
		 *  If dealt ccy in foregin ccyp is base ccy then vehicle ccy is term ccy 
		 *  Or else if dealt ccy in foreign ccyp is term ccy then vehicle ccy is base ccy
		 *  
		 *  Dealt currency in local is always vehicle currency
		 */
		if (dealtCcyInForeignCcy.getName().equals(marketRate1.getBaseCcy())) {
			// set multiplier to 0.0 if there is no appropriate tier
			foreignBidMultiplier = foreignDealtCcyTier != null
					? foreignDealtCcyTier.getRate() + forwardPoints1[0]
					: 0.0;
			// set multiplier to 0.0 if there is no appropriate tier
			foreignOfferMultiplier = foreignSettleCcyTier != null
					? foreignSettleCcyTier.getRate() + forwardPoints1[1]
					: 0.0;

			dealtCcyInLocalCcy = CurrencyFactory.getCurrency(marketRate1.getVariableCcy());
		} else {
			// set multiplier if appropriate tier is present
			if (foreignDealtCcyTier != null && foreignDealtCcyTier.getRate() != 0.0D) {
				foreignBidMultiplier = 1 / (foreignDealtCcyTier.getRate() + forwardPoints1[1]);
			}

			// set multiplier if appropriate tier is present
			if (foreignSettleCcyTier != null && foreignSettleCcyTier.getRate() != 0.0D) {
				foreignOfferMultiplier = 1 / (foreignSettleCcyTier.getRate() + forwardPoints1[0]);
			}

			isInverseForeign = true;

			dealtCcyInLocalCcy = CurrencyFactory.getCurrency(marketRate1.getBaseCcy());
		}

		double dealtAmtInLocalCcy = foreignBidMultiplier * bidPrice.getDealtAmount();
		double settleAmtInLocalCcy = foreignOfferMultiplier * offerPrice.getDealtAmount();

		/*
		 * Find appropriate tier in local ccyp rate for require dealt amount
		 */
		MarketPrice localDealtCcyTier = SyntheticCrossUtil.findTier(dealtCcyInLocalCcy, dealtAmtInLocalCcy,
				DealingPrice.BID, marketRate2);
		MarketPrice localSettleCcyTier = SyntheticCrossUtil.findTier(dealtCcyInLocalCcy, settleAmtInLocalCcy,
				DealingPrice.OFFER, marketRate2);

		double localBidMultiplier = 0.0;
		double localOfferMultiplier = 0.0;
		boolean isInverseLocal = false;

		/*
		 * If dealt ccy in local ccyp is base ccy then
		 */
		if (dealtCcyInLocalCcy.getName().equals(marketRate2.getBaseCcy())) {
			localBidMultiplier = localDealtCcyTier != null
					? localDealtCcyTier.getRate() + forwardPoints2[0]
					: 0.0;
			localOfferMultiplier = localSettleCcyTier != null
					? localSettleCcyTier.getRate() + forwardPoints2[1]
					: 0.0;
		} else {
			// set multiplier if appropriate tier is present
			if (localDealtCcyTier != null && localDealtCcyTier.getRate() != 0.0D) {
				localBidMultiplier = 1 / (localDealtCcyTier.getRate()+ forwardPoints2[1]);
			}

			// set multiplier if appropriate tier is present
			if (localSettleCcyTier != null && localSettleCcyTier.getRate() != 0.0D) {
				localOfferMultiplier = 1 / (localSettleCcyTier.getRate() + forwardPoints2[0]);
			}

			isInverseLocal = true;
		}
		
		
		/*
		 * Populate final bid rates in finalQuote object
		 */
		double finalBidRate = MathUtilC.multiply(foreignBidMultiplier, localBidMultiplier);
		double finalOfferRate = MathUtilC.multiply(foreignOfferMultiplier, localOfferMultiplier);
		
		if ( log.isDebugEnabled() )
		{
			
			/* Populate debug log info */
			 
			populateLogInfo(sb, DealingPrice.BID, foreignDealtCcyTier != null ? foreignDealtCcyTier.getRate() : 0.0, isInverseForeign, 
					localDealtCcyTier != null ? localDealtCcyTier.getRate() : 0.0, isInverseLocal, finalBidRate, bidPrice.getDealtAmount());
		}
		if ( log.isDebugEnabled() )
		{
			
			/* Populate debug log info */
			 
			populateLogInfo(sb, DealingPrice.OFFER, foreignSettleCcyTier != null ? foreignSettleCcyTier.getRate() : 0.0, isInverseForeign, 
					localSettleCcyTier != null ? localSettleCcyTier.getRate() : 0.0, isInverseLocal, finalOfferRate, offerPrice.getDealtAmount());
		}
		double [] finalBidOfferRate = {finalBidRate,finalOfferRate};
		return finalBidOfferRate;
	}
	
	/**
	 * Add bid/offer calculation info to logs
	 * eg.
	 * EUR/JPY 0 119.54 [1.2129 X 98.56] 1000000.0 2 119.57 [1.213 X 98.57] 1000000.0 0 119.54 [1.2129 X 98.56] 5000000.0 2 119.57 [1.213 X 98.57] 5000000.0 0 119.54 [1.2129 X 98.56] 1.0E7 2 119.57 [1.213 X 98.57] 1.0E7
	 * CAD/CHF 0 0.8004 [0.8952 / 1.1184] 1000000.0 2 0.8006 [0.8953 / 1.1183] 1000000.0 0 0.8004 [0.8952 / 1.1184] 5000000.0 2 0.8006 [0.8953 / 1.1183] 5000000.0 0 0.8004 [0.8952 / 1.1184] 1.0E7 2 0.8006 [0.8953 / 1.1183] 1.0E7
	 * EUR/GBP 0 0.74379 [1.2129 / 1.6307] 1000000.0 2 0.7439 [1.213 / 1.6306] 1000000.0 0 0.74379 [1.2129 / 1.6307] 5000000.0 2 0.7439 [1.213 / 1.6306] 5000000.0 0 0.74379 [1.2129 / 1.6307] 1.0E7 2 0.7439 [1.213 / 1.6306] 1.0E7
	 * 
	 * @param sb
	 * @param bidOfferMode
	 * @param foreignTierRate
	 * @param isInverseForeignRate
	 * @param localTierRate
	 * @param isInverseLocalRate
	 * @param finalRate
	 * @param finalAmt
	 */
	private void populateLogInfo(StringBuilder sb, int bidOfferMode, double foreignTierRate, boolean isInverseForeignRate, 
			double localTierRate, boolean isInverseLocalRate, double finalRate, double finalAmt) 
	{
		sb.append(' ').append(bidOfferMode);
		sb.append(' ').append(finalRate);
		sb.append(' ').append('[');
		if ( isInverseForeignRate && isInverseLocalRate ) 
		{
			sb.append("1/(");
			sb.append(foreignTierRate).append(" X ");
			sb.append(localTierRate);
			sb.append(')');
		} 
		else if ( isInverseForeignRate ) 
		{
			sb.append(localTierRate).append(" / ");
			sb.append(foreignTierRate);
		}
		else if ( isInverseLocalRate )
		{
			sb.append(foreignTierRate).append(" / ");
			sb.append(localTierRate);
		}
		else
		{
			sb.append(foreignTierRate).append(" X ");
			sb.append(localTierRate);
		}
		sb.append(']');
		sb.append(' ').append(finalAmt);
	}
	
	public String toString()
	{
		return "Type:" + getType() + ", Name:" + this.getClass().getName();
	}
}
