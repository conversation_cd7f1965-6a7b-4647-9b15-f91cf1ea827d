package com.integral.broker.syntheticcross;

import com.integral.broker.BrokerAdaptorFactory;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.cache.BrokerQuoteCache;
import com.integral.broker.cache.QuoteCacheFactory;
import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.broker.model.Configuration;
import com.integral.broker.model.Product;
import com.integral.broker.model.Stream;
import com.integral.broker.model.enums.SpreadUnit;
import com.integral.broker.request.CustomerOrderDetails;
import com.integral.broker.request.PriceCalculationService;
import com.integral.broker.request.PriceCalculationServiceC;
import com.integral.broker.request.SpacesBrokerOrderHandler;
import com.integral.broker.rfs.RFSValidationException;
import com.integral.broker.util.SyntheticCrossUtil;
import com.integral.ems.EMSOrder;
import com.integral.ems.EMSOrderResponse;
import com.integral.ems.EMSUtil;
import com.integral.ems.OrderContext;
import com.integral.ems.api.EMSAPI;
import com.integral.ems.log.EMSEventLogger;
import com.integral.ems.log.EMSEventLoggerC;
import com.integral.ems.pipeline.order.submit.PopulateEMSExecutionRuleHelper;
import com.integral.ems.provision.OrderProvision;
import com.integral.ems.provision.ProductType;
import com.integral.ems.response.PriceCalculator;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.currency.CurrencyPairGroup;
import com.integral.finance.currency.SyntheticCurrencyPairGroup;
import com.integral.finance.fx.FXRateBasis;
import com.integral.finance.fx.FXRateConvention;
import com.integral.finance.marketData.fx.FXMarketDataSet;
import com.integral.finance.trade.Tenor;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.util.YMUtil;
import com.integral.is.finance.businessCenter.EndOfDayService;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.is.message.BrokerOrderResponse;
import com.integral.is.message.TradeRequest;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.math.MathUtil;
import com.integral.message.ErrorMessage;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.OrderType;
import com.integral.model.dealing.descriptor.CoveredTradeDescriptor;
import com.integral.model.ems.ClientChannelType;
import com.integral.model.ems.CustomExecutionRule;
import com.integral.model.ems.EMSExecutionRule;
import com.integral.time.IdcDate;
import com.integral.util.MathUtilC;

import java.math.BigDecimal;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 1/2/14
 */
public class SyntheticCrossOrderService
{
    private static final Log LOG = LogFactory.getLog(SyntheticCrossOrderService.class);
    private static final EMSEventLogger emsLogger = new EMSEventLoggerC();
    private ConcurrentHashMap<String, SyntheticCrossOrder> orderMap = new ConcurrentHashMap<String, SyntheticCrossOrder>();
    protected static SyntheticCrossOrderService _instance = new SyntheticCrossOrderService();
    private SyntheticCrossUtil syntheticCrossUtil = SyntheticCrossUtil.getInstance();
    private SpacesBrokerOrderHandler spacesBrokerOrderHandler = SpacesBrokerOrderHandler.getInstance();
    private PriceCalculationService priceCalculationService;
    private PopulateEMSExecutionRuleHelper populateEMSExecutionRuleHelper = new PopulateEMSExecutionRuleHelper();

    public static SyntheticCrossOrderService getInstance()
    {
        return _instance;
    }

    public boolean submitForeignCCYOrder(String orderId)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        long recd = System.currentTimeMillis();
        long recdNanos = System.nanoTime();
        if(syntheticCrossOrder.getForeignCcyRequest() == null)
        {
            populateForeignCCYRequest(syntheticCrossOrder);
        }
        BrokerOrderRequest foreignCcyRequest = syntheticCrossOrder.getForeignCcyRequest();
        foreignCcyRequest.getCustomExecutionRule().setSyntheticCross(true);
        foreignCcyRequest.getCustomExecutionRule().setEspRfs(syntheticCrossOrder.isEspRfs());
        Stream stream = syntheticCrossUtil.getStream(foreignCcyRequest);
        CustomerOrderDetails customerOrderDetails = spacesBrokerOrderHandler.getCustomerOrderDetails(foreignCcyRequest, stream);
        EMSOrder emsOrder = spacesBrokerOrderHandler.createAndPopulateEMSOrder(foreignCcyRequest, recd, recdNanos, stream, customerOrderDetails);
        EMSUtil.setFullAmountAttributesSyntheticForeign(emsOrder,syntheticCrossOrder);
        emsOrder.getSingleLegOrder().setSynXComp(ISConstantsC.SYNTHETICCROSSCOMPONENT_PRIMARY);
        SpreadUnit espSpreadUnit = syntheticCrossOrder.getForeignCCYProduct().getConfiguration().getEspSpreadUnit();
		if (espSpreadUnit == SpreadUnit.BPS) {
			double referenceRate = BrokerAdaptorUtil.getInstance().getLiveFXMDSMidRate(syntheticCrossOrder.getForeignCCYProduct().getCurrencyPair());
			emsOrder.setReferenceRate(referenceRate);
		}else if(espSpreadUnit == SpreadUnit.Spread_Percent) {
            	BrokerQuoteCache brokerQuoteCache= QuoteCacheFactory.getInstance().getBrokerQuoteCache(syntheticCrossOrder.getForeignCCYProduct());
            	emsOrder.setBidOfferSpread(brokerQuoteCache.getBidOfferSpread());
         }
        EMSExecutionRule executionRule = emsOrder.getEMSExecutionRule();
        executionRule.setResponseType(EMSExecutionRule.ResponseType.AGGREGATE_FILL);
        String emsOrderId = emsOrder.getSingleLegOrder().get_id();
        syntheticCrossOrder.setForeignEmsOrderId(emsOrderId);
        ErrorMessage errorMessage = submitToEMS(emsOrder);
        if (errorMessage != null) {
            LOG.error("submitForeignCCYOrder: EMS order submission failed; ctId: " + orderId + ", reason: " + errorMessage);
            return false;
        }
        else {
            LOG.info("submitForeignCCYOrder: submitted to EMS; customer ctId: " + orderId + ", EMS orderId: " + emsOrderId);
            return true;
        }
    }

    public boolean submitLocalCCYOrder(String clOrdId,EMSOrder emsOrder)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(clOrdId);
        EMSUtil.setFullAmountAttributesSyntheticLocal(emsOrder,syntheticCrossOrder);
        ErrorMessage errorMessage = submitToEMS(emsOrder);
        if (errorMessage != null) {
            LOG.error("submitLocalCCYOrder: EMS order submission failed; ctId - " + clOrdId + " reason: " + errorMessage);
            return false;
        }
        else {
            String emsOrderId = emsOrder.getSingleLegOrder().get_id();
            syntheticCrossOrder.setLocalEmsOrderId(emsOrderId);
            LOG.info("submitLocalCCYOrder: submitted to EMS; ctId - " + clOrdId + ", ems order: " + emsOrderId);
            return true;
        }
    }

    public EMSOrder createLocalEMSOrder(String orderId){
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        long recd = System.currentTimeMillis();
        long recdNanos = System.nanoTime();
        if(syntheticCrossOrder.getLocalCcyRequest() == null) populateLocalCCYRequest(syntheticCrossOrder);
        BrokerOrderRequest localCcyRequest = syntheticCrossOrder.getLocalCcyRequest();
        localCcyRequest.getCustomExecutionRule().setSyntheticCross(true);
        localCcyRequest.getCustomExecutionRule().setEspRfs(syntheticCrossOrder.isEspRfs());
        Stream stream = syntheticCrossUtil.getStream(localCcyRequest);
        CustomerOrderDetails customerOrderDetails = spacesBrokerOrderHandler.getCustomerOrderDetails(localCcyRequest, stream);
        EMSOrder emsOrder = spacesBrokerOrderHandler.createAndPopulateEMSOrder(localCcyRequest, recd, recdNanos, stream, customerOrderDetails);
        emsOrder.getSingleLegOrder().setSynXComp(ISConstantsC.SYNTHETICCROSSCOMPONENT_SECONDARY);
        EMSExecutionRule refRule = syntheticCrossUtil.getExecutionRuleLocal(syntheticCrossOrder);
        if ( refRule != null )
        {
            emsLogger.referenceExecutionRule(emsOrder, refRule);
            if(LOG.isDebugEnabled()) LOG.debug("submitLocalCCYOrder: ctId=" + orderId + ", BookId from RefRule=" + refRule.getBookId()
                    + ", RefRule=" + refRule + ", RefRuleHashCode=" + System.identityHashCode(refRule));
        }
        else
        {
            LOG.warn(".submitLocalCCYOrder: submitLocalCCYOrder: EMS order submission failed, No reference rule found for ctId=" + orderId);
            return null;
        }
        OrderProvision orderProvision = syntheticCrossUtil.createOrderProvision(syntheticCrossOrder.getLocalCCYProduct().getCurrencyPair(), syntheticCrossOrder.getTradingParty(), stream.getShortName());
        OrderContext orderContext = new OrderContext(emsOrder.getSingleLegOrder(), emsOrder);
        orderContext.setOrderProvision(orderProvision);
		SpreadUnit espSpreadUnit = syntheticCrossOrder.getLocalCCYProduct().getConfiguration().getEspSpreadUnit();
		if (espSpreadUnit == SpreadUnit.BPS) {
			double referenceRate = BrokerAdaptorUtil.getInstance().getLiveFXMDSMidRate(syntheticCrossOrder.getLocalCCYProduct().getCurrencyPair());
			emsOrder.setReferenceRate(referenceRate);
		}else if(espSpreadUnit == SpreadUnit.Spread_Percent) {
            	BrokerQuoteCache brokerQuoteCache= QuoteCacheFactory.getInstance().getBrokerQuoteCache(syntheticCrossOrder.getLocalCCYProduct());
            	emsOrder.setBidOfferSpread(brokerQuoteCache.getBidOfferSpread());
         }
        //emsLogger.synXReceived(orderContext);
        populateEMSExecutionRuleHelper.populateEMSExecutionRule(orderContext, refRule);
        EMSExecutionRule executionRule = emsOrder.getEMSExecutionRule();
        emsOrder.setExecutionRuleUpdateDisabled(true);
        emsOrder.setReferenceExecutionRule(refRule);
        CustomExecutionRule customExecutionRule = syntheticCrossOrder.getSyntheticCcyRequest().getCustomExecutionRule();
        executionRule.setExcludedProviders(customExecutionRule.getExcludedProviders());
        executionRule.setResponseType(EMSExecutionRule.ResponseType.NO_RESPONSE);
        executionRule.setOrderCompletionHandlerType(EMSExecutionRule.OrderCompletionHandlerType.SYNTHETIC_CROSS_LOCAL);
        if(LOG.isDebugEnabled()) LOG.debug("submitLocalCCYOrder: ctId=" + orderId + ", BookId from EMSExecutionRule=" + executionRule.getBookId());
        EMSUtil.setExecutionType(emsOrder);
        return emsOrder;
    }

    protected ErrorMessage submitToEMS(EMSOrder emsOrder)
    {
        return EMSAPI.submitOrder(emsOrder);
    }

    private SyntheticCrossOrder populateLocalCCYRequest(SyntheticCrossOrder syntheticCrossOrder)
    {
        BrokerOrderRequest syntheticCcyRequest = syntheticCrossOrder.getSyntheticCcyRequest();
        BrokerOrderRequest localCCYRequest = new BrokerOrderRequest();
        syntheticCrossOrder.setLocalCcyRequest(localCCYRequest);
        populateCommonAttributes(syntheticCcyRequest, localCCYRequest, ISCommonConstants.SYNTHETICCROSSCOMPONENT_SECONDARY);
        localCCYRequest.setOrderExpiryTime(0); //force to use TIF configured in stream
        CustomExecutionRule customExecutionRule = localCCYRequest.getCustomExecutionRule();
        customExecutionRule.setOrderTIF(EMSExecutionRule.OrderTIF.GTD);
        customExecutionRule.setPriceTakingTIF(EMSExecutionRule.PriceTakingTIF.IOC);
        customExecutionRule.setCoverEnabled(syntheticCrossOrder.isCoverEnabled());
        customExecutionRule.setNoCoverEnabled(syntheticCrossOrder.isNoCover());
        customExecutionRule.setWarehouseEnabled(syntheticCrossOrder.isWarehouseEnabled());

        Stream syntheticStream = syntheticCrossUtil.getStream(syntheticCcyRequest);
        CurrencyPair syntheticCCYPair = syntheticCrossOrder.getSyntheticCCYPair();
        Product syntheticProduct = syntheticStream.getProduct(syntheticCCYPair);
        Product foreignCCYPProduct = syntheticProduct.getForeignCCYPProduct();
        Product localCCYProduct = syntheticProduct.getLocalCCYPProduct();
        Configuration localCCYConfiguration = localCCYProduct.getConfiguration();
        Stream localCCYStream = localCCYConfiguration.getStream();
        Configuration syntheticConfiguration = syntheticProduct.getConfiguration();
        CurrencyPairGroup currencyPairGroup = syntheticConfiguration.getCurrencyPairGroup();
        FXRateConvention fxRateConvention = currencyPairGroup.getFXRateConvention();
        FXRateBasis rateBasis = fxRateConvention.getFXRateBasis(foreignCCYPProduct.getCurrencyPair());
        int spotPrecision = rateBasis.getSpotPrecision();
        localCCYRequest.setCustomerSpotPrecision(spotPrecision);

        Currency dealtCcy = syntheticCrossOrder.getSecondaryDealtCcy();
        int buySellLocal = syntheticCrossOrder.getLocalBuySell();
        localCCYRequest.setBuySell(buySellLocal);
        double localDealtAmount = getLocalDealtAmount(syntheticCrossOrder);
        localCCYRequest.setAmount(localDealtAmount);
        localCCYRequest.setDealtCurrency(dealtCcy);
        localCCYRequest.setBaseCurrency(localCCYProduct.getBaseCurrency());
        localCCYRequest.setVariableCurrency(localCCYProduct.getVariableCurrency());
        localCCYRequest.setStreamId(localCCYStream.getShortName());
        double localCCYRate = syntheticCrossOrder.getLocalCCYRate();
        String tradeId = syntheticCcyRequest.getTradeId();
        LOG.info("populateLocalCCYRequest: ctId - " + tradeId + ", dealtAmt= " + localDealtAmount + ", rate= " + localCCYRate + ", dealtCCY= " + dealtCcy + ", buySell= " + buySellLocal);
        localCCYRequest.setRate(localCCYRate);

        localCCYRequest.setClientChannelType(ClientChannelType.SYNTHETIC_CROSS);
        OrderRequest.Type orderType;
        if(OrderType.MARKET.equals(syntheticConfiguration.getSyntheticLocalOrderType())) {
            orderType = OrderRequest.Type.MARKET;
            localCCYRequest.setCustomerOrderPureMarketOrder(true);
        }
        else orderType = OrderRequest.Type.LIMIT;
        localCCYRequest.setOrderType(orderType);
        if(orderType.equals(OrderRequest.Type.LIMIT)) {
            FXRateBasis localRateBasis = localCCYConfiguration.getRateBasis(localCCYProduct.getCurrencyPair());
            double pipsFactor = localRateBasis.getPipsFactor();
            double localRange = syntheticConfiguration.getSyntheticLocalRange();
            localRange = MathUtilC.subtract( MathUtilC.add( localRange / pipsFactor, 1 ), 1 );// this is to avoid 5.700000000000001E-4 (refer ProductProvisionC)
            localCCYRequest.setMarketRange(localRange);
        }

        return syntheticCrossOrder;
    }

    public void populateForeignCCYRequest(SyntheticCrossOrder syntheticCrossOrder)
    {
        BrokerOrderRequest syntheticCcyRequest = syntheticCrossOrder.getSyntheticCcyRequest();
        BrokerOrderRequest foreignCcyRequest = new BrokerOrderRequest();
        syntheticCrossOrder.setForeignCcyRequest(foreignCcyRequest);
        populateCommonAttributes(syntheticCcyRequest, foreignCcyRequest, ISCommonConstants.SYNTHETICCROSSCOMPONENT_PRIMARY);
        //todo add configuration here for rollout
        CustomExecutionRule customExecutionRule = foreignCcyRequest.getCustomExecutionRule();
        customExecutionRule.setCoverEnabled(syntheticCrossOrder.isCoverEnabled());
        customExecutionRule.setNoCoverEnabled(syntheticCrossOrder.isNoCover());
        customExecutionRule.setWarehouseEnabled(syntheticCrossOrder.isWarehouseEnabled());
        customExecutionRule.setIncludeCoverTradeFees(true);
        customExecutionRule.setMultifillEnabled(false);

        String tradeId = syntheticCcyRequest.getTradeId();

        Stream syntheticStream = syntheticCrossUtil.getStream(syntheticCcyRequest);
        CurrencyPair syntheticCCYPair = syntheticCrossOrder.getSyntheticCCYPair();
        Product syntheticProduct = syntheticStream.getProduct(syntheticCCYPair);
        Product foreignCCYPProduct = syntheticCrossOrder.getForeignCCYProduct();
        Product localCCYProduct = syntheticCrossOrder.getLocalCCYProduct();
        Configuration foreignCCYConfiguration = foreignCCYPProduct.getConfiguration();
        Stream foreignCCYStream = foreignCCYConfiguration.getStream();
        Configuration syntheticConfiguration = syntheticProduct.getConfiguration();

        foreignCcyRequest.setBaseCurrency(foreignCCYPProduct.getBaseCurrency());
        foreignCcyRequest.setVariableCurrency(foreignCCYPProduct.getVariableCurrency());
        foreignCcyRequest.setStreamId(foreignCCYStream.getShortName());
        double syntheticDealtAmount = syntheticCcyRequest.getAmount();
        double syntheticSettleAmount = SyntheticCrossUtil.getOtherCurrencyAmount(syntheticCcyRequest.getBaseCurrency(), syntheticCcyRequest.getDealtCurrency(), syntheticDealtAmount, syntheticCcyRequest.getRate());
        SyntheticCurrencyPairGroup currencyPairGroup = (SyntheticCurrencyPairGroup)syntheticConfiguration.getCurrencyPairGroup();
        Currency vehicleCurrency = currencyPairGroup.getVehicleCurrency();
        syntheticCrossOrder.setVehicleCCY(vehicleCurrency);
        FXRateConvention fxRateConvention = currencyPairGroup.getFXRateConvention();
        FXRateBasis rateBasis = fxRateConvention.getFXRateBasis(foreignCCYPProduct.getCurrencyPair());
        int spotPrecision = rateBasis.getSpotPrecision();
        foreignCcyRequest.setCustomerSpotPrecision(spotPrecision);

        Currency foreignDealtCCY = syntheticCrossOrder.getPrimaryDealtCcy();
        foreignCcyRequest.setDealtCurrency(foreignDealtCCY);
        int foreignBuySell = syntheticCrossOrder.getForeignBuySell();
        foreignCcyRequest.setBuySell(foreignBuySell);
        
        //Calculate foreign and local currency forward point        
		
		double[] foreignCcyPairForwardPoints = { 0.0, 0.0 };
		double[] localCcyPairForwardPoints = { 0.0, 0.0 };
		FXMarketDataSet mds = syntheticProduct.getConfiguration().getMarketDataSet();
		FXRateConvention conv = syntheticProduct.getConfiguration().getCurrencyPairGroup().getFXRateConvention();
		if (BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(syntheticProduct.getBroker().getShortName())
				.isSyntheticCcyPairPresent(syntheticProduct.getBroker().getShortName(),
				syntheticCCYPair.getName())) {
            try {
                foreignCcyPairForwardPoints = SyntheticCrossUtil.getValueDateMismatchForwardPointsForCurrencyPair(
                        foreignCCYPProduct.getCurrencyPair(), syntheticCCYPair, conv, mds);
                localCcyPairForwardPoints = SyntheticCrossUtil.getValueDateMismatchForwardPointsForCurrencyPair(
                        localCCYProduct.getCurrencyPair(), syntheticCCYPair, conv, mds);
            } catch (RFSValidationException e) {
                LOG.warn("SyntheticCrossOrderService.populateForeignCCYRequest() error while getting MDS point "+e.getMessage());
            }
        }

        double localCCYRate = syntheticCrossOrder.getLocalCCYRate();
        double localForwardPoints = 0.0;
        int localBuySell = syntheticCrossOrder.getLocalBuySell();
        if(localBuySell == TradeRequest.SELL){
        	localForwardPoints = localCcyPairForwardPoints[0];
        }
        else{
        	localForwardPoints = localCcyPairForwardPoints[1];
        }
        LOG.info("populateForeignCCYRequest: TradeId - " + tradeId + ", LocalCCY Rate: " + localCCYRate + " (" + localCCYProduct.getCurrencyPair() + ")");
        if(priceCalculationService == null) priceCalculationService = PriceCalculationServiceC.getInstance();
        double syntheticRateSpreaded = syntheticCrossOrder.getEmsOrder().getOrderRate();
        LOG.info("populateForeignCCYRequest: TradeId - " + tradeId + ", SyntheticCCYMatchRate: " + syntheticRateSpreaded);
        double foreignCcyRate = SyntheticCrossUtil.calculateForeignCCYRate(syntheticCcyRequest, foreignCCYPProduct, localCCYProduct, vehicleCurrency, syntheticRateSpreaded, localCCYRate+localForwardPoints);
        double foreignForwardPoints = 0.0;       
        int roundingMode;
		if (foreignBuySell == TradeRequest.SELL) {
			foreignForwardPoints = foreignCcyPairForwardPoints[0];
			roundingMode = BigDecimal.ROUND_CEILING;
		} else {
			foreignForwardPoints = foreignCcyPairForwardPoints[1];
			roundingMode = BigDecimal.ROUND_FLOOR;
		}
		//subtract forward point for order matching
		double adjustedRate = foreignCcyRate - foreignForwardPoints;
        adjustedRate = MathUtilC.correctFloatingPointsCalculationPrecision(adjustedRate);
        adjustedRate = MathUtil.round(adjustedRate, spotPrecision, roundingMode);
        foreignCcyRequest.setRate(adjustedRate);
        double marketRange = syntheticCcyRequest.getMarketRange();
        if(marketRange > 0.0D && BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(syntheticCcyRequest.getProviderShortName()).isPassRangeToPrimaryEnabled()) {
            boolean isBuy = syntheticCcyRequest.getBuySell() == TradeRequest.BUY;
            double limitPrice = isBuy ? syntheticRateSpreaded + marketRange : syntheticRateSpreaded - marketRange;
            double foreignLimitPrice = SyntheticCrossUtil.calculateForeignCCYRate(syntheticCcyRequest, foreignCCYPProduct, localCCYProduct, vehicleCurrency, limitPrice, localCCYRate+localForwardPoints);
            double foreignRange;
            if(foreignBuySell == TradeRequest.BUY){
                foreignRange = foreignLimitPrice - foreignCcyRate;
            }else {
                foreignRange = foreignCcyRate - foreignLimitPrice;
            }
            foreignRange = MathUtilC.correctFloatingPointsCalculationPrecision(foreignRange);
            foreignRange = MathUtil.round(foreignRange, spotPrecision, BigDecimal.ROUND_FLOOR); //always round to smaller range
            if(foreignRange > 0.0D) {
                foreignCcyRequest.setMarketRange(foreignRange);
            }else {
                LOG.error("Error calculating primary range - syn=" + marketRange + ", pr=" + foreignRange);
            }
        }
        
		// Log forward points
		if (BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(syntheticProduct.getBroker().getShortName())
				.isSyntheticCcyPairPresent(syntheticProduct.getConfiguration().getStream()
						.getBrokerOrganizationFunction().getOrganization().getShortName(),
				syntheticCCYPair.getName())) {
			if (LOG.isInfoEnabled()) {
				FXRateBasis localCCYRateBasis = fxRateConvention.getFXRateBasis(localCCYProduct.getCurrencyPair());
				String formattedForeignForwardPoints = getFormattedForwardPoints(rateBasis.getPipsFactor(),
						foreignForwardPoints);
				String formattedLocalForwardPoints = getFormattedForwardPoints(localCCYRateBasis.getPipsFactor(),
						localForwardPoints);

				IdcDate synCcyPairValueDate = SyntheticCrossUtil.getTenorDate(syntheticCCYPair, conv, Tenor.SPOT_TENOR);
				IdcDate localCcyPairValueDate = SyntheticCrossUtil.getTenorDate(localCCYProduct.getCurrencyPair(), conv,
						Tenor.SPOT_TENOR);
				IdcDate foreignCcyPairValueDate = SyntheticCrossUtil.getTenorDate(foreignCCYPProduct.getCurrencyPair(),
						conv, Tenor.SPOT_TENOR);

				LOG.info("populateValueDateMismatchDetails: OrderId - " + syntheticCrossOrder.getSyntheticEmsOrderId()
						+ ", TransactionId - " + tradeId);
				LOG.info("Syn currency value date(YYYY_MM_DD) :"
						+ synCcyPairValueDate.getFormattedDate(IdcDate.YYYY_MM_DD));
				if (synCcyPairValueDate.compare(foreignCcyPairValueDate) != 0) {
					LOG.info("Primary currency value date(YYYY_MM_DD) :"
							+ foreignCcyPairValueDate.getFormattedDate(IdcDate.YYYY_MM_DD));
				}
				if (synCcyPairValueDate.compare(localCcyPairValueDate) != 0) {
					LOG.info("Secondary currency value date(YYYY_MM_DD) :"
							+ localCcyPairValueDate.getFormattedDate(IdcDate.YYYY_MM_DD));
				}
				LOG.info("Primary currency spot rate :" + adjustedRate + ", Secondary currency spot rate :"
						+ localCCYRate);
				LOG.info("MDS name: " + mds.getShortName());
				LOG.info("ForeignCcyPairForwardPoints: " + formattedForeignForwardPoints);
				LOG.info("LocalCcyPairForwardPoints: " + formattedLocalForwardPoints);
				int roundingType = localBuySell == TradeRequest.SELL ? BigDecimal.ROUND_FLOOR
						: BigDecimal.ROUND_CEILING;
				LOG.info("Primary " + foreignCCYPProduct.getCurrencyPair().getDisplayName() + " final rate: " + MathUtil
						.round(adjustedRate + foreignForwardPoints, rateBasis.getSpotPrecision(), roundingType));
				LOG.info("Secondary " + localCCYProduct.getCurrencyPair().getDisplayName() + " final rate: " + MathUtil
						.round(localCCYRate + localForwardPoints, localCCYRateBasis.getSpotPrecision(), roundingType));
				LOG.info("SynCcyPair Final Rate: " + syntheticRateSpreaded + ", SynCcyPair Rate without forward poits: "
						+ MathUtil.round(localCCYRate * adjustedRate,
								fxRateConvention.getFXRateBasis(syntheticProduct.getCurrencyPair()).getSpotPrecision(),
								roundingType));
			}
		}
		syntheticCrossOrder.setForeignCCYForwardPoints(foreignForwardPoints);
		syntheticCrossOrder.setLocalCCYForwardPoints(localForwardPoints);
        double foreignCCYAmount;
        double vehicleCcyAmt = SyntheticCrossUtil.calculateVehicleCcyAmount(syntheticCrossOrder, syntheticDealtAmount, adjustedRate);
        syntheticCrossOrder.setVehicleCCYAmount(vehicleCcyAmt);
        foreignCCYAmount = SyntheticCrossUtil.calculatePrimaryDealtAmount(syntheticCrossOrder);
        foreignCcyRequest.setAmount(foreignCCYAmount);
        LOG.info("populateForeignCCYRequest: ForeignCCYTrade - ctId= " + tradeId + ", cp= " + foreignCCYPProduct.getCurrencyPair() + " , rate= " + adjustedRate + " , amount= " + foreignCCYAmount + ", buySell= " + foreignBuySell + " , dealtCCY= " + foreignDealtCCY + ", vehCcyAmt=" + vehicleCcyAmt);


        foreignCcyRequest.setClientChannelType(ClientChannelType.SYNTHETIC_CROSS);
        EMSExecutionRule.PriceTakingTIF priceTakingTIF = syntheticCrossOrder.isParialFillAllowed() ? syntheticCcyRequest.getCustomExecutionRule().getPriceTakingTIF() : EMSExecutionRule.PriceTakingTIF.FOK;
        EMSExecutionRule.OrderTIF orderTIF = syntheticCrossOrder.isParialFillAllowed() ? syntheticCcyRequest.getCustomExecutionRule().getOrderTIF() : EMSExecutionRule.OrderTIF.FOK;
        customExecutionRule.setPriceTakingTIF(priceTakingTIF);
        customExecutionRule.setOrderTIF(orderTIF);
    }

	private String getFormattedForwardPoints(double pipsFactor, double points) {
		return String.format("%.2f", pipsFactor * points);
	}

    private void populateCommonAttributes(BrokerOrderRequest fromRequest, BrokerOrderRequest toRequest,String crossComponentType)
    {
        toRequest.setAsWarmupMessage(fromRequest.isWarmupMessage());
        // toRequest.setBatchNumber(); TODO: what is this?
        toRequest.setClientChannelType(fromRequest.getClientChannelType());
        toRequest.setCoveredNetTradeId(fromRequest.getCoveredNetTradeId());
        toRequest.setCoveredOrder(fromRequest.getCoveredOrder());
        toRequest.setCoveredTrade(fromRequest.getCoveredTrade());
        toRequest.setCustomerOrderPureMarketOrder(fromRequest.isCustomerOrderPureMarketOrder());
        toRequest.getCoveredTrade().setSythenticCrossComponent(crossComponentType);
        //toRequest.setCustomerSpotPrecision(fromRequest.getCustomerSpotPrecision());
        // toRequest.setCustomerVwap(); TODO: what is this?
        CustomExecutionRule customExecutionRule = new CustomExecutionRule();
        customExecutionRule.setOrderTIF(fromRequest.getCustomExecutionRule().getOrderTIF());
        customExecutionRule.setNoCoverEnabled(fromRequest.getCustomExecutionRule().isNoCoverEnabled());
        customExecutionRule.setWarehouseEnabled(fromRequest.getCustomExecutionRule().isWarehouseEnabled());
        toRequest.setCustomExecutionRule(customExecutionRule);
        toRequest.setLegalEntity(fromRequest.getLegalEntity());
        toRequest.setOrderExpiryTime(fromRequest.getOrderExpiryTime());
        toRequest.setOrderTxnId(fromRequest.getOrderTxnId());
        toRequest.setOrderType(fromRequest.getOrderType());
        toRequest.setOrganization(fromRequest.getOrganization());
        //toRequest.setOriginatingDealId(fromRequest.getOriginatingDealId());
        toRequest.setOriginatingLP(fromRequest.getOriginatingLP());
        toRequest.setOriginatingOrder(fromRequest.getOriginatingOrder());
        toRequest.setOriginatingOrderId(fromRequest.getOriginatingDealId());
        toRequest.setOriginatingTrade(fromRequest.getOriginatingTrade());
        toRequest.setProviderShortName(fromRequest.getProviderShortName());
        // toRequest.setPriceNumber(); TODO: what is this?
        toRequest.setPriceType(fromRequest.getPriceType());
        toRequest.setProviderKey(fromRequest.getProviderKey());
        toRequest.setProviderQuoteId(fromRequest.getProviderQuoteId());
        toRequest.setRequestId(fromRequest.getRequestId());
        toRequest.setSeqNo(fromRequest.getSeqNo());
        toRequest.setServerId(fromRequest.getServerId());
        toRequest.setState(fromRequest.getState());
        toRequest.setTakerReferenceId(fromRequest.getTakerReferenceId());
        toRequest.setTotalInBatch(fromRequest.getTotalInBatch());
        toRequest.setTradeId(fromRequest.getTradeId());
        toRequest.setTraderExternalId(fromRequest.getTraderExternalId());
        toRequest.setTradingPartyIdChain(fromRequest.getTradingPartyIdChain());
        toRequest.setUser(fromRequest.getUser());
        toRequest.setUTI(fromRequest.getUTI());
        toRequest.setUTINamespace(fromRequest.getUTINamespace());
        toRequest.setValueDateLong(fromRequest.getValueDateLong());
    }

    public boolean processSyntheticTradeVerification(EMSOrderResponse emsOrderResponse)
    {
        return submitForeignCCYOrder(emsOrderResponse.getReferenceId());
    }

    public EMSOrderResponse processForeignCCYTradeVerification(EMSOrderResponse emsOrderResponse)
    {
        String orderId = emsOrderResponse.getReferenceId();
        double rate = emsOrderResponse.getRate();
        double coverExecRate = emsOrderResponse.getCoverExecutionRate();
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        StringBuilder sb = new StringBuilder(".processPrimary: oId=");
        sb.append(orderId).append(",pr=").append(rate).append(",pcvexr=").append(coverExecRate).append(",pfp=").append(syntheticCrossOrder.getForeignCCYForwardPoints());
        if(coverExecRate == 0) coverExecRate = rate;
        Product foreignCCYProduct = syntheticCrossOrder.getForeignCCYProduct();
        Product localCCYProduct = syntheticCrossOrder.getLocalCCYProduct();
        BrokerOrderRequest syntheticCcyRequest = syntheticCrossOrder.getSyntheticCcyRequest();
        Currency vehicleCCY = syntheticCrossOrder.getVehicleCCY();
        double syntheticOrderAmount = syntheticCcyRequest.getAmount();
        emsOrderResponse.setOrderAmount(syntheticOrderAmount);
        double localCCYRate = syntheticCrossOrder.getLocalCCYRate();
        double syntheticCCYRate = syntheticCrossUtil.calculateSyntheticCCYRate(syntheticCcyRequest, foreignCCYProduct,
				localCCYProduct, vehicleCCY, rate + syntheticCrossOrder.getForeignCCYForwardPoints(),
				localCCYRate + syntheticCrossOrder.getLocalCCYForwardPoints());
        sb.append(",lr=").append(localCCYRate).append(",lfp=").append(syntheticCrossOrder.getLocalCCYForwardPoints()).append(",srbs=").append(syntheticCCYRate);
        if(syntheticCrossOrder.isParialFillAllowed()) {
            boolean partialFill = emsOrderResponse.getLeavesAmount() >= emsOrderResponse.getMinAmount();
            if(partialFill) {
                double filledAmountSynthetic = SyntheticCrossUtil.calculateAmountSynthetic(syntheticCrossOrder.getSyntheticCCYPair(), syntheticCrossOrder.getForeignCcyPair(), syntheticCrossOrder.getLocalCcyPair(),
                        syntheticCrossOrder.getPrimaryDealtCcy(), syntheticCcyRequest.getDealtCurrency(), syntheticCCYRate, rate + syntheticCrossOrder.getForeignCCYForwardPoints(),
                        localCCYRate + syntheticCrossOrder.getLocalCCYForwardPoints(), emsOrderResponse.getDealtCurrencyAmount());
                syntheticCcyRequest.getDealtCurrency().round(filledAmountSynthetic);
                emsOrderResponse.setDealtCurrencyAmount(filledAmountSynthetic);
                double filledAmountCumulative = syntheticCrossOrder.addFilledAmount(filledAmountSynthetic);
                emsOrderResponse.setLeavesAmount(MathUtilC.subtract(syntheticCrossOrder.getOrderAmountSynthetic(), filledAmountCumulative));
            }else {
                double filledAmount = MathUtilC.subtract(syntheticOrderAmount, syntheticCrossOrder.getFilledAmount());
                emsOrderResponse.setDealtCurrencyAmount(filledAmount);
                syntheticCrossOrder.addFilledAmount(filledAmount);
                emsOrderResponse.setLeavesAmount(0);
            }
        }else {
            emsOrderResponse.setDealtCurrencyAmount(syntheticOrderAmount);
            syntheticCrossOrder.addFilledAmount(syntheticOrderAmount);
            emsOrderResponse.setLeavesAmount(0);
        }
        String brokerName = syntheticCcyRequest.getProviderShortName();
        double coverExecRateSyn;
        if(BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(brokerName).useLPFilledRateForCvrExRate(brokerName)) {
            coverExecRateSyn = syntheticCrossUtil.calculateSyntheticCCYRate(syntheticCcyRequest, foreignCCYProduct, localCCYProduct, vehicleCCY, coverExecRate, localCCYRate);
        }else {
            coverExecRateSyn = syntheticCCYRate;
        }
        Currency primarySettledCcy = SyntheticCrossUtil.getOtherCurrency(syntheticCrossOrder.getForeignCcyPair(), syntheticCrossOrder.getPrimaryDealtCcy());
        Currency settleCurrency = SyntheticCrossUtil.getOtherCurrency(syntheticCrossOrder.getSyntheticCCYPair(), syntheticCcyRequest.getDealtCurrency());
        double primaryFees = emsOrderResponse.getFees();
        double synFees = SyntheticCrossUtil.calculateSyntheticSettledAmount(syntheticCrossOrder.getSyntheticCCYPair(), syntheticCrossOrder.getForeignCcyPair(), syntheticCrossOrder.getLocalCcyPair(), primarySettledCcy, settleCurrency, primaryFees, syntheticCCYRate, rate, localCCYRate);
        emsOrderResponse.setFees(synFees);
        sb.append(",fa=").append(emsOrderResponse.getDealtCurrencyAmount()).append(",scvexr=").append(coverExecRateSyn).append(",pcvfees=").append(primaryFees).append(",scvfees").append(synFees);
        EMSOrder emsOrder = syntheticCrossOrder.getEmsOrder();
        PriceCalculator priceCalculator = emsOrder.getPriceCalculator();
        double responsePrice = priceCalculator.calculateResponsePrice(emsOrder, emsOrderResponse, syntheticCCYRate, sb);
        emsOrderResponse.setCoverExecutionRate(coverExecRateSyn);
        emsOrderResponse.setRate(responsePrice);
        double settledCCYAmountSynthetic = SyntheticCrossUtil.getOtherCurrencyAmount(syntheticCcyRequest.getBaseCurrency(), syntheticCcyRequest.getDealtCurrency(), emsOrderResponse.getDealtCurrencyAmount(), responsePrice);
        settledCCYAmountSynthetic = settleCurrency.round(settledCCYAmountSynthetic);
        settledCCYAmountSynthetic = emsOrder.isBuy() ? MathUtilC.add(settledCCYAmountSynthetic, emsOrderResponse.getFees()) : MathUtilC.subtract(settledCCYAmountSynthetic, emsOrderResponse.getFees());
        emsOrderResponse.setSettledCurrencyAmount(settledCCYAmountSynthetic);
        sb.append(",ssamt=").append(settledCCYAmountSynthetic);
        LOG.info(sb.toString());
        return emsOrderResponse;
    }

    public double getLocalDealtAmount(SyntheticCrossOrder syntheticCrossOrder)
    {
        double dealtAmt;
        if(syntheticCrossOrder.getSecondaryDealtCcy().isSameAs(syntheticCrossOrder.getVehicleCCY())) {
            dealtAmt = SyntheticCrossUtil.calculateVehicleCcyAmount(syntheticCrossOrder, syntheticCrossOrder.getFilledAmount(), syntheticCrossOrder.getForeignCcyRequest().getRate());
        }else {
            Currency localCCY = syntheticCrossUtil.getOtherCurrency(syntheticCrossOrder.getLocalCCYProduct().getCurrencyPair(), syntheticCrossOrder.getVehicleCCY());
            BrokerOrderRequest syntheticCcyRequest = syntheticCrossOrder.getSyntheticCcyRequest();
            Currency dealtCurrency = syntheticCcyRequest.getDealtCurrency();
            if(dealtCurrency.isSameAs(localCCY)){
                dealtAmt = syntheticCrossOrder.getFilledAmount();
            }else {
                dealtAmt = SyntheticCrossUtil.getOtherCurrencyAmount(syntheticCrossOrder.getSyntheticCCYPair().getBaseCurrency(), dealtCurrency, syntheticCrossOrder.getFilledAmount(), syntheticCcyRequest.getRate());
            }
        }
        dealtAmt = syntheticCrossOrder.getSecondaryDealtCcy().round(dealtAmt);
        return dealtAmt;
    }

    public void updateSyntheticDetails(String orderId, BrokerOrderResponse response)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        if(syntheticCrossOrder == null)
        {
            LOG.error("SyntheticCrossOrder for the given TradeId " + orderId + " not found");
        }
        BrokerOrderRequest foreignCcyRequest = syntheticCrossOrder.getForeignCcyRequest();
        if(foreignCcyRequest == null)
        {
            populateForeignCCYRequest(syntheticCrossOrder);
            foreignCcyRequest = syntheticCrossOrder.getForeignCcyRequest();
        }
        response.setSyntheticCross(true);
        boolean partialFill = response.getTradeAmount() != response.getAcceptedAmount();
        if(!partialFill) {
            response.setVehicleCCYAmount(syntheticCrossOrder.getVehicleCCYAmount()); //vehicle ccy amount base on order amount
        }else {
            double vehicleCcyAmount = SyntheticCrossUtil.calculateVehicleCcyAmount(syntheticCrossOrder, response.getAcceptedAmount(), foreignCcyRequest.getRate());
            response.setVehicleCCYAmount(vehicleCcyAmount);
        }
        response.setVehicleCurrency(syntheticCrossOrder.getVehicleCCY().getObjectID());
        response.setPrimaryDealtCcyId(syntheticCrossOrder.getPrimaryDealtCcy().getObjectID());
        response.setSecondaryDealtCcyId(syntheticCrossOrder.getSecondaryDealtCcy().getObjectID());
        String foreignCurrencyPair = syntheticCrossOrder.getForeignCCYProduct().getCurrencyPair().getName();
        String localCurrencyPair = syntheticCrossOrder.getLocalCCYProduct().getCurrencyPair().getName();
        response.setForeignCurrencyPair(foreignCurrencyPair);
        response.setLocalCurrencyPair(localCurrencyPair);
        if(syntheticCrossOrder.getForeignCcyRequest() == null) populateForeignCCYRequest(syntheticCrossOrder);
        if(syntheticCrossOrder.getLocalCcyRequest() == null) populateLocalCCYRequest(syntheticCrossOrder);
        FXRateBasis foreignRateBasis = syntheticCrossOrder.getForeignCCYProduct().getConfiguration().getRateBasis(syntheticCrossOrder.getForeignCCYProduct().getCurrencyPair());
        IdcDate idcTradeDate = EndOfDayService.getCurrentTradeDate();
        IdcDate foreignValueDate = foreignRateBasis.getValueDate(idcTradeDate, Tenor.SPOT_TENOR);
        response.setPrimaryValueDate(foreignValueDate.asJdkDate().getTime());
        FXRateBasis localRateBasis = syntheticCrossOrder.getLocalCCYProduct().getConfiguration().getRateBasis(syntheticCrossOrder.getLocalCCYProduct().getCurrencyPair());
        IdcDate localValueDate = localRateBasis.getValueDate(idcTradeDate, Tenor.SPOT_TENOR);
        response.setSecondaryValueDate(localValueDate.asJdkDate().getTime());
    }



    public SyntheticCrossOrder initializeSyntheticCrossOrder(BrokerOrderRequest request, EMSOrder emsOrder)
    {
        String orderId = emsOrder.getClientReferenceId();
        SyntheticCrossOrder syntheticCrossOrder = new SyntheticCrossOrder(orderId, request);
        Product syntheticProduct = syntheticCrossUtil.getProduct(request);
        syntheticCrossOrder.setForeignCcyPair(syntheticProduct.getForeignCCYPProduct().getCurrencyPair());
        syntheticCrossOrder.setLocalCcyPair(syntheticProduct.getLocalCCYPProduct().getCurrencyPair());
        boolean partialFillAllowed = !emsOrder.isEspRfs() && ConfigurationFactory.getInstance().getPriceConfigurationMBean().isSyntheticCrossPartialFillAllowed(request.getProviderShortName());
        syntheticCrossOrder.setParialFillAllowed(partialFillAllowed);
        Currency primaryDealtCcy = syntheticCrossUtil.findPrimaryDealtCurrency(syntheticProduct);
        Currency secondaryDealtCcy = syntheticCrossUtil.findSecondaryDealtCurrency(syntheticProduct);
        syntheticCrossOrder.setPrimaryDealtCcy(primaryDealtCcy);
        syntheticCrossOrder.setSecondaryDealtCcy(secondaryDealtCcy);
        syntheticCrossOrder.setSyntheticEmsOrderId(emsOrder.getSingleLegOrder().get_id());
        syntheticCrossOrder.setEmsOrder(emsOrder);
        orderMap.put(orderId, syntheticCrossOrder);
        CoveredTradeDescriptor coveredTrade = request.getCoveredTrade();
        coveredTrade.setCurrencyPair(emsOrder.getSingleLegOrder().getCurrencyPair());
        coveredTrade.setDealtCurrency(request.getDealtCurrency());
        coveredTrade.setDealtAmount(request.getAmount());
        coveredTrade.setOrderId(request.getCoveredOrder().getOrderRequestRef().getUid());
        coveredTrade.setTradeId(request.getTradeId());
        coveredTrade.setBuy(request.getBuySell() == TradeRequest.BUY);
        coveredTrade.setOrderRate(request.getRate());
        syntheticCrossOrder.setEspRfs(emsOrder.isEspRfs());
        populateExecutionMethodRelatedAttributes(syntheticCrossOrder);
        syntheticCrossOrder.setForeignCCYProduct(syntheticProduct.getForeignCCYPProduct());
        syntheticCrossOrder.setLocalCCYProduct(syntheticProduct.getLocalCCYPProduct());
        return syntheticCrossOrder;
    }

    public BrokerOrderResponse populateRejectDetails(String orderId, BrokerOrderResponse response)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        response.setCancelledAmount(syntheticCrossOrder.getSyntheticCcyRequest().getAmount());
        return response;
    }

    public void clean(String orderId)
    {
        orderMap.remove(orderId);
    }

    public boolean isSyntheticEmsOrder(String orderId, String emsOrderId)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        if(syntheticCrossOrder == null) return false;
        return emsOrderId.equals(syntheticCrossOrder.getSyntheticEmsOrderId());
    }

    public boolean isForeignEmsOrder(String orderId, String emsOrderId)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        if(syntheticCrossOrder == null) return false;
        return emsOrderId.equals(syntheticCrossOrder.getForeignEmsOrderId());
    }

    public boolean isLocalEmsOrder(String orderId, String emsOrderId)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        if(syntheticCrossOrder == null) return false;
        return emsOrderId.equals(syntheticCrossOrder.getLocalEmsOrderId());
    }

    public boolean isCoverTradingDisabled(String orderId)
    {
        SyntheticCrossOrder syntheticCrossOrder = orderMap.get(orderId);
        if(syntheticCrossOrder == null) {
            return false;
        }
        CustomExecutionRule customExecutionRule = syntheticCrossOrder.getSyntheticCcyRequest().getCustomExecutionRule();
        if (customExecutionRule != null && customExecutionRule.isNoCoverEnabled()) {
            return true;
        }
        if(BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(syntheticCrossOrder.getSyntheticCCYProduct().getBroker().getShortName()).isSynOrderMatchUseMatchingExecRule() && syntheticCrossOrder.isEspRfs()){
        	//ESPRFS
        	return syntheticCrossOrder.isNoCover();
        }else{
        	//ESP
        	EMSExecutionRule executionRule = syntheticCrossUtil.getExecutionRuleSynthetic(syntheticCrossOrder, syntheticCrossOrder.isEspRfs());
        	return executionRule.isNoCoverEnabled();
        }
    }

    public void populateExecutionMethodRelatedAttributes(SyntheticCrossOrder syntheticCrossOrder){
        CustomExecutionRule customExecutionRule = syntheticCrossOrder.getSyntheticCcyRequest().getCustomExecutionRule();
        if (customExecutionRule != null){
            if(customExecutionRule.isNoCoverEnabled()) {
                syntheticCrossOrder.setNoCover(true);
                return;
            }
            if( customExecutionRule.isWarehouseEnabled()){
                if(isRiskWarehouseEnabled(syntheticCrossOrder)) {
                    syntheticCrossOrder.setWarehouseEnabled(true);
                }
                else{
                    syntheticCrossOrder.setCoverEnabled(true);
                }
                return;
            }
            /**
             * Cover is requested explicitly. This is from BrokerOrderRequest.
             */
            if( customExecutionRule.isCoverEnabled() ){
                syntheticCrossOrder.setCoverEnabled(true);
                return;
            }
        }
        EMSExecutionRule executionRule = syntheticCrossUtil.getExecutionRuleSynthetic(syntheticCrossOrder, syntheticCrossOrder.isEspRfs());
        if( executionRule != null ) {
            if (executionRule.isNoCoverEnabled()) {
                syntheticCrossOrder.setNoCover(true);
                return;
            }
            if( executionRule.isWarehouseEnabled()){
                if(isRiskWarehouseEnabled(syntheticCrossOrder,executionRule)) {
                    syntheticCrossOrder.setWarehouseEnabled(true);
                }
                else{
                    syntheticCrossOrder.setCoverEnabled(true);
                }
            }
        }
    }

    public boolean isRiskWarehouseEnabled(SyntheticCrossOrder syntheticCrossOrder) {
        return isRiskWarehouseEnabled(syntheticCrossOrder,null);
    }

    public boolean isRiskWarehouseEnabled(SyntheticCrossOrder syntheticCrossOrder, EMSExecutionRule executionRule) {
        String ymOrgName = syntheticCrossOrder.getSyntheticCcyRequest().getProviderShortName();
        ProductType productType = syntheticCrossOrder.isEspRfs() ? ProductType.RFS_ESP : ProductType.ESP ;
        String ymBookName = BrokerAdaptorUtil.getInstance().getYMBookName(executionRule,ymOrgName,productType);
        return YMUtil.isRiskWarehouseEnabledForSyntheticCross(ymOrgName,ymBookName,syntheticCrossOrder.getSyntheticCCYPair().getName()
                ,syntheticCrossOrder.getForeignCcyPair().getName(),syntheticCrossOrder.getLocalCcyPair().getName());
    }


    public void setSyntheticCrossUtil(SyntheticCrossUtil obj)
    {
        this.syntheticCrossUtil = obj;
    }

    public void setSpacesBrokerOrderHandler(SpacesBrokerOrderHandler obj)
    {
        this.spacesBrokerOrderHandler = obj;
    }

    public void setPriceCalculationService(PriceCalculationService obj)
    {
        this.priceCalculationService = obj;
    }

    public void setPopulateEMSExecutionRuleHelper(PopulateEMSExecutionRuleHelper obj)
{
    this.populateEMSExecutionRuleHelper = obj;
}

    public SyntheticCrossOrder getSyntheticCrossOrder(String orderId)
    {
        return orderMap.get(orderId);
    }
}
