package com.integral.broker.syntheticcross;

import com.integral.broker.model.Product;

public class SyntheticCrossCalculatorFactory {
	
	private SyntheticCrossCalculatorFactory(){
	}
	
	private static class Holder {
		public static final SyntheticCrossCalculatorFactory INSTANCE = new SyntheticCrossCalculatorFactory();
	}
	
	public static SyntheticCrossCalculatorFactory getInstance(){
		return Holder.INSTANCE;
	}
	
	public SyntheticCrossCalculator getSyntheticCrossCalculator(SyntheticCrossCalculator.Type type, Product product){
		SyntheticCrossCalculator calculator = null;
		switch(type){
			case BASIC:
				calculator = getBasicSyntheticCrossCalculator(product);
				break;
			default:
				calculator = null;
		}
		return calculator;
	}
	
	private SyntheticCrossCalculator getBasicSyntheticCrossCalculator(Product product){
		return new SyntheticCrossCalculatorC(product);
	}
}
