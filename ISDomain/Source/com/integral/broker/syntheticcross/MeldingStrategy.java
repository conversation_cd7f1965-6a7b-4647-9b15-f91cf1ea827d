package com.integral.broker.syntheticcross;

import com.integral.finance.dealing.Quote;
import com.integral.is.message.MarketRate;

public interface MeldingStrategy 
{
	public enum Type 
	{
		DEFAULT;
	}
	
	/**
	 * Returns type of meld strategy
	 * @return
	 */
	public Type getType();
	
	/**
	 * Merge rates from two marketRates to form a quote
	 * @param rate1
	 * @param rate2
	 * @param finalQuote
	 */
	public void meld(MarketRate rate1, MarketRate rate2, Quote finalQuote, double []foreignCcyPairForwardPoints, double []localCcyPairForwardPoints);	

}
