package com.integral.broker.syntheticcross;

import com.integral.adaptor.manager.BrokerAdaptorTradeManager;
import com.integral.admin.utils.StringUtils;
import com.integral.broker.model.Stream;
import com.integral.broker.request.RFSTradeLogger;
import com.integral.broker.request.RFSTradeLoggerCache;
import com.integral.broker.request.rfs.ESPRFSBrokerTradeHandlerC;
import com.integral.broker.request.rfs.ESPRFSBrokerTradeHandlerProxy;
import com.integral.broker.response.OAResponseSender;
import com.integral.broker.util.SyntheticCrossUtil;
import com.integral.ems.EMSOrder;
import com.integral.ems.EMSOrderResponse;
import com.integral.is.ISCommonConstants;
import com.integral.is.message.BrokerOrderResponse;
import com.integral.is.message.MessageFactory;
import com.integral.is.message.rfs.RFSTradeResponse;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.ErrorMessage;
import com.integral.model.ems.EMSExecutionRule;
import com.integral.model.ems.EMSExecutionType;
import com.integral.util.MathUtilC;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 1/2/14
 */
public class SyntheticCrossResponseSender extends OAResponseSender
{
    private static final Log LOG = LogFactory.getLog(SyntheticCrossResponseSender.class);
    private SyntheticCrossOrderService syntheticCrossOrderService = SyntheticCrossOrderService.getInstance();

    @Override
    public ErrorMessage sendOrderResponse(EMSOrderResponse responseOriginal) {
        EMSOrderResponse emsResponse = cloneEMSResponse(responseOriginal);
        if(isSynthetic(emsResponse)) {
            return processSynthetic(emsResponse);
        }
        else if(isForeign(emsResponse)) {
            return processForeign(emsResponse);
        }else if(isLocal(emsResponse)) {
            LOG.info("sendOrderResponse: Response for local CCY Trade; ignoring it");
            return null;
        } else {
            LOG.error("sendOrderResponse: Unknown Response from EMS - customer order: " + emsResponse.getReferenceId() + ", ems order: " + emsResponse.getOrderId());
            return null;
            //throw new IllegalArgumentException("Unknown response: " + emsResponse);
        }
    }

    public ErrorMessage sendOrderResponse( RFSTradeResponse rfsTradeResponse )
    {
        throw new UnsupportedOperationException ( "Synthetic Cross Not Supported" );
    }

    protected ErrorMessage processSynthetic(EMSOrderResponse emsOrderResponse)
    {
        String ctId = emsOrderResponse.getReferenceId();
        String orderId = emsOrderResponse.getOrderId();
        EMSOrderResponse.Type responseType = emsOrderResponse.getType();
        LOG.info("processSynthetic: ctId - " + ctId + ", orderId - " + orderId + ", response - " + responseType + ",amt=" + emsOrderResponse.getDealtCurrencyAmount());
        BrokerOrderResponse brokerResponse = MessageFactory.newBrokerOrderResponse();
        populateCommonAttributes(brokerResponse, emsOrderResponse);
        ErrorMessage errorMessage = null;
        boolean sentCoverRequest = false;
        boolean lastResponse = false;
        SyntheticCrossOrder syntheticCrossOrder = syntheticCrossOrderService.getSyntheticCrossOrder(ctId);
        if( syntheticCrossOrder == null ){
            LOG.error("processSynthetic : SyntheticCross Order not found for ctId="+ctId+", emsOrderResponse"+emsOrderResponse);
            errorMessage = MessageFactory.newErrorMessage();
            errorMessage.setErrorCode("SyntheticCrossOrderNotFound");
            return errorMessage;
        }
        switch (responseType)
        {
            case FILL:
                //primary and secondary ccy pair rates are required (primary is calculated from syn and secondary rates)
                double localCCYRate = SyntheticCrossUtil.getLocalCCYRate(syntheticCrossOrder);
                if(localCCYRate < 0)
                {
                    LOG.warn("processSynthetic: LocalCCY Rate is not available. Rejecting the trade. RequestId: " + ctId);
                    errorMessage = rejectOrder(brokerResponse, emsOrderResponse);
                    syntheticCrossOrderService.clean(ctId);
                    return errorMessage;
                }else
                {
                    syntheticCrossOrder.setLocalCCYRate(localCCYRate);
                    LOG.info("processSynthetic: TradeId - " + ctId + ", LocalCCY Rate: " + localCCYRate);
                }
                // if NO_COVER is selected, send response directly
                if(syntheticCrossOrder.isNoCover())
                {
                    populateFillAttributes(brokerResponse, emsOrderResponse);
                    brokerResponse.setPrimaryExecutionType(EMSExecutionType.NO_COVER);
                    brokerResponse.setSecondaryExecutionType(EMSExecutionType.NO_COVER);
                    LOG.info("processSynthetic: NoCover, ctId - " + ctId + ", sending verification to customer, orderId - " + orderId);
                    syntheticCrossOrderService.updateSyntheticDetails(ctId, brokerResponse);
                    errorMessage = sendBrokerOrderResponse(brokerResponse);
                    double filledAmount = syntheticCrossOrder.addFilledAmount(brokerResponse.getAcceptedAmount());
                    lastResponse = MathUtilC.equal(syntheticCrossOrder.getOrderAmountSynthetic(), filledAmount, emsOrderResponse.getMinAmount());
                    if(errorMessage != null)
                    {
                        LOG.error("processSynthetic: NoCover, ctId - " + ctId + ", sending customer trade verification failed, orderId - " + orderId + ", error - " + errorMessage);
                    } else
                    {
                        if(!isEspRfs(ctId)) sendBrokerOrderResponseStatus(brokerResponse.getTradeId(), brokerResponse.getTradeAmount(), 0D, emsOrderResponse.isWarmupResponse());
                    }
                }
                else if(syntheticCrossOrder.isWarehouseEnabled()){
                    populateFillAttributes(brokerResponse, emsOrderResponse);
                    brokerResponse.setPrimaryExecutionType(EMSExecutionType.WAREHOUSE);
                    brokerResponse.setSecondaryExecutionType(EMSExecutionType.WAREHOUSE);
                    populateBookNames(syntheticCrossOrder,brokerResponse);
                    LOG.info("processSynthetic: Warehouse, ctId - " + ctId + ", sending verification to customer, orderId - " + orderId);
                    syntheticCrossOrderService.updateSyntheticDetails(ctId, brokerResponse);
                    errorMessage = sendBrokerOrderResponse(brokerResponse);
                    syntheticCrossOrder.addFilledAmount(brokerResponse.getAcceptedAmount());
                    if(errorMessage != null)
                    {
                        LOG.error("processSynthetic: Warehouse, ctId - " + ctId + ", sending customer trade verification failed, orderId - " + orderId + ", error - " + errorMessage);
                    }
                }
                else if(syntheticCrossOrder.isSentToPrimary()){
                    LOG.info("processSynthetic: Cover, ctId - " + ctId + " already sent to primary for execution");
                }else {
                    sentCoverRequest = syntheticCrossOrderService.processSyntheticTradeVerification(emsOrderResponse);
                    if(!sentCoverRequest){ //reject order
                        populateRejectAttributes(brokerResponse, emsOrderResponse);
                        syntheticCrossOrderService.populateRejectDetails(ctId, brokerResponse);
                        LOG.info("processSynthetic: Cover, ctId - " + ctId + ", sending rejection to customer, orderId - " + orderId);
                        errorMessage = sendBrokerOrderResponse(brokerResponse);
                        if(errorMessage != null)
                        {
                            LOG.error("processSynthetic: Cover, ctId - " + ctId + ", sending customer trade rejection failed, orderId - " + orderId + ", error - " + errorMessage);
                        }
                    }else {
                        syntheticCrossOrder.setSentToPrimary(true);
                        // trade verification will be send when foreign CCY trade is verified
                    }
                }
                break;
            default:
                //if partial fill sent status response
                double filledAmount = syntheticCrossOrder.getFilledAmount();
                double orderAmount = syntheticCrossOrder.getOrderAmountSynthetic();
                if(filledAmount > 0.0D && filledAmount < orderAmount && !isEspRfs(ctId)){
                    double unfilledAmount = MathUtilC.subtract(orderAmount, filledAmount);
                    sendBrokerOrderResponseStatus(brokerResponse.getTradeId(), orderAmount, unfilledAmount, emsOrderResponse.isWarmupResponse());
                }else {
                    errorMessage = rejectOrder(brokerResponse, emsOrderResponse);
                }
                lastResponse = true;
        }
        if(!sentCoverRequest && lastResponse){
            syntheticCrossOrderService.clean(ctId);
        }
        return errorMessage;
    }

    //Flow when the syntheticCrossOrder is warehoused
    private void populateBookNames(SyntheticCrossOrder syntheticCrossOrder, BrokerOrderResponse brokerResponse) {

        StringBuilder sb = new StringBuilder("SCRS.populateBookNames:{");

        if(syntheticCrossOrder.isWarehouseEnabled()) {
            EMSExecutionRule refExecRule = null;
            EMSOrder emsOrder = syntheticCrossOrder.getEmsOrder();
            if(emsOrder!=null && emsOrder.getReferenceExecutionRule()!=null){
                refExecRule = emsOrder.getReferenceExecutionRule();
                sb.append("refExecRuleSource=EMSOrder");
            }else{
                refExecRule = SyntheticCrossUtil.getInstance().getExecutionRuleSynthetic(syntheticCrossOrder, syntheticCrossOrder.isEspRfs());
                sb.append("refExecRuleSource=").append(refExecRule!=null ? "SyntheticCrossUtil":"Null");
            }

            if(refExecRule!=null){
                sb.append(",refExecRuleType=").append(refExecRule.getExecutionType());
                sb.append(",refExecRuleBookName=").append(refExecRule.getBookName());
                String configBookName = refExecRule.getBookName();
                //As per existing behaviour if the Synthetic ccyPair config is Warehoused,
                //the underlying trades are stamped with the synthetic ccyPair configured bookName.
                if(!StringUtils.isNullOrEmptyString(configBookName)){
                    brokerResponse.setPrimaryBookName(configBookName);
                    brokerResponse.setSecondaryBookName(configBookName);
                }
            }
        }

        sb.append(",primaryBookName=").append(brokerResponse.getPrimaryBookName());
        sb.append(",secondaryBookName=").append(brokerResponse.getSecondaryBookName());
        sb.append(",isWarehouseEnabled=").append(syntheticCrossOrder.isWarehouseEnabled());
        sb.append("}");
        LOG.debug(sb.toString());
    }

    public void processEspRfsResponse(EMSOrderResponse emsOrderResponse)
    {
        String ctId = emsOrderResponse.getReferenceId();
        String orderId = emsOrderResponse.getOrderId();
        EMSOrderResponse.Type responseType = emsOrderResponse.getType();
        LOG.info("processEspRfsResponse: ctId - " + ctId + ", orderId - " + orderId + ", response - " + responseType);
        SyntheticCrossOrder syntheticCrossOrder = syntheticCrossOrderService.getSyntheticCrossOrder(ctId);
        if(syntheticCrossOrder == null){
            LOG.error("processEspRfsResponse: SyntheticCrossOrder not found for " + ctId + " " + orderId);
            sendRejectionToEspRfsHandler(ctId);
            syntheticCrossOrderService.clean(ctId);
            return;
        }

        switch (responseType)
        {
            case FILL:
                //primary and secondary ccy pair rates are required (primary is calculated from syn and secondary rates)
                double localCCYRate = SyntheticCrossUtil.getLocalCCYRate(syntheticCrossOrder);
                if(localCCYRate < 0)
                {
                    LOG.warn("processEspRfsResponse: LocalCCY Rate is not available. Rejecting the trade. RequestId: " + ctId);
                    sendRejectionToEspRfsHandler(ctId);
                    syntheticCrossOrderService.clean(ctId);
                    return;
                }else
                {
                    syntheticCrossOrder.setLocalCCYRate(localCCYRate);
                    LOG.info("processEspRfsResponse: TradeId - " + ctId + ", LocalCCY Rate: " + localCCYRate);
                }
                if(syntheticCrossOrderService.isCoverTradingDisabled(ctId)){
                    BrokerOrderResponse brokerResponse = MessageFactory.newBrokerOrderResponse();
                    populateCommonAttributes(brokerResponse, emsOrderResponse);
                    populateFillAttributes(brokerResponse, emsOrderResponse);
                    LOG.info("processEspRfsResponse: NoCover, ctId - " + ctId + ", sending verification to customer, orderId - " + orderId);
                    syntheticCrossOrderService.updateSyntheticDetails(ctId, brokerResponse);
                    sendToEspRfsHander(emsOrderResponse, ctId, EMSExecutionType.NO_COVER, EMSExecutionType.NO_COVER);
                }
                else if(syntheticCrossOrder.isWarehouseEnabled()){
                    BrokerOrderResponse brokerResponse = MessageFactory.newBrokerOrderResponse();
                    populateCommonAttributes(brokerResponse, emsOrderResponse);
                    populateFillAttributes(brokerResponse, emsOrderResponse);
                    brokerResponse.setPrimaryExecutionType(EMSExecutionType.WAREHOUSE);
                    brokerResponse.setSecondaryExecutionType(EMSExecutionType.WAREHOUSE);
                    LOG.info("processEspRfsResponse: Warehouse, ctId - " + ctId + ", sending verification to customer, orderId - " + orderId);
                    syntheticCrossOrderService.updateSyntheticDetails(ctId, brokerResponse);
                    sendToEspRfsHander(emsOrderResponse, ctId, EMSExecutionType.WAREHOUSE, EMSExecutionType.WAREHOUSE);
                }
                else {
                    boolean primaryCpRequestSubmitted = syntheticCrossOrderService.processSyntheticTradeVerification(emsOrderResponse);
                    if (!primaryCpRequestSubmitted) { //reject order
                        LOG.info("processEspRfsResponse: Cover, ctId - " + ctId + ", sending rejection to customer, orderId - " + orderId);
                        sendRejectionToEspRfsHandler(ctId);
                        syntheticCrossOrderService.clean(ctId);
                    }
                }
                break;
            default:
                sendRejectionToEspRfsHandler(ctId);
                syntheticCrossOrderService.clean(ctId);
        }
    }

    protected ErrorMessage processForeign(EMSOrderResponse emsOrderResponse)
    {
    	double primaryCoverRate = emsOrderResponse.getCoverExecutionRate();
        String ctId = emsOrderResponse.getReferenceId();
        String orderId = emsOrderResponse.getOrderId();
        LOG.info("processForeign: ctId - " + ctId + ", orderId - " + orderId + ", response - " + emsOrderResponse.getType() + ",amt=" + emsOrderResponse.getDealtCurrencyAmount());
        BrokerOrderResponse brokerResponse = MessageFactory.newBrokerOrderResponse();
        populateCommonAttributes(brokerResponse, emsOrderResponse);
        SyntheticCrossOrder syntheticCrossOrder = syntheticCrossOrderService.getSyntheticCrossOrder(ctId);

        //update the maker user on the broker order response from the synthetic currency pair stream.
        Stream syntheticCcyPairStream = syntheticCrossOrder.getSyntheticCCYProduct ().getConfiguration ().getStream ();
        brokerResponse.setMakerUser ( syntheticCcyPairStream.getUser () );
        LOG.info ( "SCRS.processForeign - set the maker user from synthetic currency pair stream="
                + syntheticCcyPairStream + " with user=" + syntheticCcyPairStream.getUser () + ",primaryStreamUser="
                + emsOrderResponse.getMakerUser () );

        ErrorMessage errorMessage = null;

        switch (emsOrderResponse.getType())
        {
            case FILL:
                EMSOrderResponse syntheticResponse = syntheticCrossOrderService.processForeignCCYTradeVerification(emsOrderResponse);
                populateFillAttributes(brokerResponse, syntheticResponse);
                syntheticCrossOrderService.updateSyntheticDetails(ctId, brokerResponse);
                brokerResponse.setPrimaryExecutionType(emsOrderResponse.getExecutionType());

                String primaryBookName = emsOrderResponse.getBookName();
                if(!StringUtils.isNullOrEmptyString(primaryBookName)){
                    brokerResponse.setPrimaryBookName(primaryBookName);
                }

                if(LOG.isDebugEnabled()){
                    LOG.debug("SCRS.processForeign : BrokerOrderResponse with primaryBookName="+((null!=brokerResponse.getPrimaryBookName())?brokerResponse.getPrimaryBookName():"Null"));
                }

                RFSTradeLogger rfsTradeLogger  = RFSTradeLoggerCache.getInstance().getRFSTradeLogger(emsOrderResponse.getId());
    			if(rfsTradeLogger != null){
    				if(emsOrderResponse.getCoverTradeIds() != null){
    					rfsTradeLogger.setPrimaryCoverOrderType("Cover");
    				}else{
    					rfsTradeLogger.setPrimaryCoverOrderType("NoCover");
    				}
    				rfsTradeLogger.setPrimaryCoverRateSpot(primaryCoverRate);
    				if(rfsTradeLogger.getPrimaryCoverRateSpot() > 0)
    		        {
    			        //Recalculate Primary Pm SpotSpread
    					rfsTradeLogger.setPrimaryPmSpotSpread(MathUtilC.subtract(rfsTradeLogger.getPrimaryPmRateSpot(),rfsTradeLogger.getPrimaryCoverRateSpot()) );
    		        }
    			}
                EMSOrder emsOrder = syntheticCrossOrderService.createLocalEMSOrder(ctId);
                if( emsOrder != null ) {
                    brokerResponse.setSecondaryExecutionType(emsOrder.getEMSExecutionRule().getExecutionType());
                    EMSExecutionRule refExecRule = emsOrder.getReferenceExecutionRule();
                    if(refExecRule!=null){
                        brokerResponse.setSecondaryBookName(refExecRule.getBookName());
                    }
                }

                if(LOG.isDebugEnabled()){
                    LOG.debug("SCRS.processForeign : BrokerOrderResponse with secondaryBookName="+((null!=brokerResponse.getSecondaryBookName())?brokerResponse.getSecondaryBookName():"Null"));
                }

                if(!isEspRfs(ctId)) {
                    errorMessage = sendBrokerOrderResponse(brokerResponse);
                }
                else {
                    sendToEspRfsHander(syntheticResponse, ctId, emsOrderResponse.getExecutionType(), emsOrder.getEMSExecutionRule().getExecutionType());
                }
                LOG.info("processForeign: Cover, ctId - " + ctId + ", sending verification to customer, orderId - " + orderId + ",amt=" + brokerResponse.getAcceptedAmount());
                if(errorMessage == null && isLastResponse(emsOrderResponse))
                {
                    if(!isEspRfs(ctId)) sendBrokerOrderResponseStatus(brokerResponse.getTradeId(), brokerResponse.getTradeAmount(), 0D, emsOrderResponse.isWarmupResponse());
                    // else do nothing
                    // to cover trade for local currency
                    if(emsOrder != null) syntheticCrossOrderService.submitLocalCCYOrder(ctId,emsOrder);
                }
                if(errorMessage != null)
                {
                    LOG.error("processForeign: ctId - " + ctId + ", sending customer trade verification failed, orderId - " + orderId + ", error - " + errorMessage);
                }
                break;
            default:
                //if partially filled send status response
                boolean isPartialFill = !isEspRfs(ctId) && MathUtilC.subtract(emsOrderResponse.getOrderAmount(), emsOrderResponse.getDealtCurrencyAmount()) >= emsOrderResponse.getMinAmount();
                if(isPartialFill){
                    double orderAmount = syntheticCrossOrder.getOrderAmountSynthetic();
                    double filledAmount = syntheticCrossOrder.getFilledAmount();
                    double unfilledAmount = MathUtilC.subtract(orderAmount, filledAmount);
                    errorMessage = sendBrokerOrderResponseStatus(brokerResponse.getTradeId(), orderAmount, unfilledAmount,emsOrderResponse.isWarmupResponse());
                    //submit local order
                    emsOrder = syntheticCrossOrderService.createLocalEMSOrder(ctId);
                    if(emsOrder != null) syntheticCrossOrderService.submitLocalCCYOrder(ctId, emsOrder);
                }else {
                    //if not filled at all send reject response
                    if (!isEspRfs(ctId)) errorMessage = rejectOrder(brokerResponse, emsOrderResponse);
                    else sendRejectionToEspRfsHandler(ctId);
                }
        }
        if(errorMessage == null && isLastResponse(emsOrderResponse))
        {
            syntheticCrossOrderService.clean(ctId);
            BrokerAdaptorTradeManager.getInstance().removeTrade(brokerResponse.getTradeId());
        }
        return errorMessage;
    }

    private void sendToEspRfsHander(EMSOrderResponse emsOrderResponse, String ctId, EMSExecutionType primaryExecType, EMSExecutionType secondaryExecType)
    {
        SyntheticCrossOrder syntheticCrossOrder = syntheticCrossOrderService.getSyntheticCrossOrder(ctId);
        String synthEmsOrderId = syntheticCrossOrder.getEmsOrder().getOrderId();
        emsOrderResponse.setOrderId(synthEmsOrderId);
        ESPRFSBrokerTradeHandlerProxy espRfsBrokerTradeHandlerProxy = ESPRFSBrokerTradeHandlerProxy.getEspRfsBrokerTradeHandlerProxy(synthEmsOrderId);
        if(espRfsBrokerTradeHandlerProxy == null){
            LOG.warn("ESPRFSBrokerTraderHandlerProxy do not exist for the ESPRFS trade orderId: " + synthEmsOrderId);
            return;
        }
        ESPRFSBrokerTradeHandlerC tradeHandler = (ESPRFSBrokerTradeHandlerC)espRfsBrokerTradeHandlerProxy.getTradeHandler();
        tradeHandler.setPrimaryExecutionType(primaryExecType);
        tradeHandler.setSecondaryExecutionType(secondaryExecType);
        tradeHandler.handleESP(emsOrderResponse);
        LOG.info("SyntheticCrossResponseSender.sendToEspRfsHandler:: txid=" + ctId + ", pmrExecType=" + primaryExecType + ", scrExecType=" + secondaryExecType + ", msg=" + emsOrderResponse);
    }

    private void sendRejectionToEspRfsHandler(String ctId)
    {
        EMSOrderResponse rejectResponse = new EMSOrderResponse();
        rejectResponse.setType(EMSOrderResponse.Type.REJECT);
        sendToEspRfsHander(rejectResponse, ctId, null, null);
    }

    private ErrorMessage rejectOrder(BrokerOrderResponse brokerResponse, EMSOrderResponse emsOrderResponse)
    {
        String ctId = emsOrderResponse.getReferenceId();
        String orderId = emsOrderResponse.getOrderId();
        populateRejectAttributes(brokerResponse, emsOrderResponse);
        syntheticCrossOrderService.populateRejectDetails(ctId, brokerResponse);
        ErrorMessage errorMessage = sendBrokerOrderResponse(brokerResponse);
        LOG.info("rejectOrder: ctId - " + ctId + ", orderId - " + orderId);
        return errorMessage;
    }

    private boolean isEspRfs(String ctId)
    {
        SyntheticCrossOrder syntheticCrossOrder = syntheticCrossOrderService.getSyntheticCrossOrder(ctId);
        return syntheticCrossOrder.isEspRfs();
    }

    private boolean isSynthetic(EMSOrderResponse emsOrderResponse)
    {
        return syntheticCrossOrderService.isSyntheticEmsOrder(emsOrderResponse.getReferenceId(), emsOrderResponse.getOrderId());
    }

    private boolean isForeign(EMSOrderResponse emsOrderResponse)
    {
        return syntheticCrossOrderService.isForeignEmsOrder(emsOrderResponse.getReferenceId(), emsOrderResponse.getOrderId());
    }

    private boolean isLocal(EMSOrderResponse emsOrderResponse)
    {
        return syntheticCrossOrderService.isLocalEmsOrder(emsOrderResponse.getReferenceId(), emsOrderResponse.getOrderId());
    }

    private boolean isLastResponse(EMSOrderResponse emsOrderResponse)
    {
        return emsOrderResponse.getLeavesAmount() < emsOrderResponse.getMinAmount();
    }

    public void setSyntheticCrossOrderService(SyntheticCrossOrderService obj)
    {
        this.syntheticCrossOrderService = obj;
    }

    private EMSOrderResponse cloneEMSResponse(EMSOrderResponse src){
        EMSOrderResponse dst = new EMSOrderResponse();
        dst.setReferenceId(src.getReferenceId());
        dst.setRate(src.getRate());
        dst.setQuoteId(src.getQuoteId());
        dst.setType(src.getType());
        dst.setAcceptanceReceived(src.getAcceptanceReceived());
        dst.setAcceptanceSent(src.getAcceptanceSent());
        dst.setAcceptedTier(src.getAcceptedTier());
        dst.setBookId(src.getBookId());
        dst.setCancelReason(src.getCancelReason());
        dst.setClientChannelType(src.getClientChannelType());
        dst.setCounterParty(src.getCounterParty());
        dst.setCoverExecutionRate(src.getCoverExecutionRate());
        dst.setCoverLegalEntity(src.getCoverLegalEntity());
        dst.setLegalEntity(src.getLegalEntity());
        dst.setCoverMkrReferenceIds(src.getCoverMkrReferenceIds());
        dst.setCoverTradeIds(src.getCoverTradeIds());
        dst.setDealtCurrencyAmount(src.getDealtCurrencyAmount());
        dst.setId(src.getId());
        dst.setExternalReferenceId(src.getExternalReferenceId());
        dst.setLeavesAmount(src.getLeavesAmount());
        dst.setCoverExecutionRate(src.getCoverExecutionRate());
        dst.setMakerUser(src.getMakerUser());
        dst.setOrderAmount(src.getOrderAmount());
        dst.setOrderId(src.getOrderId());
        dst.setProviderOrg(src.getProviderOrg());
        dst.setRiskPosition(src.isRiskPosition());
        dst.setSettledCurrencyAmount(src.getSettledCurrencyAmount());
        dst.setTradeDate(src.getTradeDate());
        dst.setValueDate(src.getValueDate());
        dst.setVwap(src.getVwap());
        dst.setWarmupResponse(src.isWarmupResponse());
        dst.setBookName(src.getBookName());
        dst.setExecutionType(src.getExecutionType());
        dst.setMinAmount(src.getMinAmount());
        dst.setFees(src.getFees());
        dst.setPPSpotSpread(src.getPPSpotSpread());
        dst.setPPPreTradeSpread(src.getPPPreTradeSpread());
        dst.setPPPostTradeSpread(src.getPPPostTradeSpread());
        dst.setPMSpotSpread(src.getPMSpotSpread());
        dst.setPMPreTradeSpread(src.getPMPreTradeSpread());
        dst.setPMPostTradeSpread(src.getPMPostTradeSpread());
        dst.setSkew(src.getSkew());
        dst.setMinSpreadEnabled(src.isMinSpreadEnabled());
        dst.setMaxSpreadEnabled(src.isMaxSpreadEnabled());
        dst.setPMMinSpread(src.getPMMinSpread());
        dst.setPMMaxSpread(src.getPMMaxSpread());
        dst.setWarmupResponse(src.isWarmupResponse());
        dst.setCustomerDealtCurrencyAmount(src.getCustomerDealtCurrencyAmount());
        dst.setOriginalSpotRate(src.getOriginalSpotRate());
        dst.setCurrencyPair(src.getCurrencyPair());
        dst.setTermCurrencyOrder(src.isTermCurrencyOrder());
        dst.setSettledAndHomeCurrencyDifferent(src.isSettledAndHomeCurrencyDifferent());
        dst.setClientReferenceIdLong(src.getClientReferenceIdLong());
        dst.setOriginatingOrderId(src.getOriginatingOrderId());
        dst.setVenueServerID(src.getVenueServerID());
        dst.setForwardPoints(src.getForwardPoints());
        return dst;
    }
}
