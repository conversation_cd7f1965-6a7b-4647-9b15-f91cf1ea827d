package com.integral.broker.syntheticcross;

import java.util.HashMap;
import java.util.Map;

import com.integral.log.Log;
import com.integral.log.LogFactory;

public class MeldingStrategyFactory 
{
	private static Map<MeldingStrategy.Type, MeldingStrategy> mapper;
	protected Log log = LogFactory.getLog( this.getClass() );
	
	private MeldingStrategyFactory()
	{
		mapper = new HashMap<MeldingStrategy.Type, MeldingStrategy>();
	}
	
	private static class Holder
	{
		public static final MeldingStrategyFactory INSTANCE = new MeldingStrategyFactory();
	}
	
	public static MeldingStrategyFactory getInstance()
	{
		return Holder.INSTANCE;
	}
	
	public MeldingStrategy getMeldingStrategy(MeldingStrategy.Type type)
	{
		MeldingStrategy strategy = null;
		switch(type)
		{
			case DEFAULT:
				strategy = mapper.get(type);
				if ( strategy == null )
				{
					strategy = new DefaultMeldingStrategyC();
					mapper.put(type, strategy);
				}
				break;
			default:
				log.error(this.getClass().getName() + ".getMeldingStrategy : Unsupported type, type - " + type);
		}
		
		if ( log.isDebugEnabled() )
		{
			log.debug("getMeldingStrategy: type - " + type + ", strategy - " + strategy);
		}
		return strategy;
	}

}
