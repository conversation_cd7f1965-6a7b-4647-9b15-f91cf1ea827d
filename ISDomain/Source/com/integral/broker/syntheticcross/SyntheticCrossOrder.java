package com.integral.broker.syntheticcross;

import com.google.common.util.concurrent.AtomicDouble;
import com.integral.broker.model.Product;
import com.integral.broker.util.SyntheticCrossUtil;
import com.integral.ems.EMSOrder;
import com.integral.finance.counterparty.TradingParty;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.message.BrokerOrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.model.dealing.descriptor.CoveredOrderRequestDescriptor;
import com.integral.model.ems.EMSExecutionType;
import com.integral.util.MathUtilC;

/**
 * Copyright (c) 1999-2013 Integral Development Corp. All rights reserved
 * User: <EMAIL>
 * Date: 1/2/14
 */
public class SyntheticCrossOrder
{
    private String orderId;
    private BrokerOrderRequest syntheticCcyRequest;
    private BrokerOrderRequest foreignCcyRequest;
    private BrokerOrderRequest localCcyRequest;
    private Currency vehicleCCY;
    private Product foreignCCYProduct;
    private Product localCCYProduct;
    private Product syntheticCCYProduct;
    private String syntheticEmsOrderId;
    private String foreignEmsOrderId;
    private String localEmsOrderId;
    private double localCCYRate;
    private CurrencyPair syntheticCCYPair;
    private Integer localBuySell;
    private Integer foreignBuySell;
    private EMSOrder emsOrder;
    private double syntheticOrderAmtInBase;
    private TradingParty tradingParty;
    private boolean isEspRfs;
    private boolean noCover;
    private EMSExecutionType localEMSExecutionType;
    private Currency primaryDealtCcy;
    private Currency secondaryDealtCcy;
    private double vehicleCCYAmount;
	private double foreignCCYForwardPoints;
    private double localCCYForwardPoints;
    private AtomicDouble filledAmount = new AtomicDouble(0.0D);
    private boolean sentToPrimary;
    private boolean parialFillAllowed;

    /**
     * Set to true to force warehousing of synthetic cross components
     */
    private boolean warehouseEnabled;

    /**
     * Set to true to force cover execution of synthetic cross components
     */
    private boolean coverEnabled;
    private CurrencyPair foreignCcyPair;
    private CurrencyPair localCcyPair;

    public double getForeignCCYForwardPoints() {
		return foreignCCYForwardPoints;
	}

	public void setForeignCCYForwardPoints(double foreignCCYForwardPoints) {
		this.foreignCCYForwardPoints = foreignCCYForwardPoints;
	}

	public double getLocalCCYForwardPoints() {
		return localCCYForwardPoints;
	}

	public void setLocalCCYForwardPoints(double localCCYForwardPoints) {
		this.localCCYForwardPoints = localCCYForwardPoints;
	}

    public SyntheticCrossOrder(String orderId, BrokerOrderRequest syntheticCcyRequest)
    {
        this.orderId = orderId;
        this.syntheticCcyRequest = syntheticCcyRequest;
    }

    public String getOrderId() {
        return orderId;
    }

    public BrokerOrderRequest getSyntheticCcyRequest() {
        return syntheticCcyRequest;
    }

    public BrokerOrderRequest getForeignCcyRequest() {
        return foreignCcyRequest;
    }

    public void setForeignCcyRequest(BrokerOrderRequest foreignCcyRequest) {
        this.foreignCcyRequest = foreignCcyRequest;
    }

    public Currency getVehicleCCY() {
        return vehicleCCY;
    }

    public void setVehicleCCY(Currency vehicleCCY) {
        this.vehicleCCY = vehicleCCY;
    }

    public Product getForeignCCYProduct() {
        return foreignCCYProduct;
    }

    public void setForeignCCYProduct(Product foreignCCYProduct) {
        this.foreignCCYProduct = foreignCCYProduct;
    }

    public Product getLocalCCYProduct() {
        return localCCYProduct;
    }

    public void setLocalCCYProduct(Product localCCYProduct) {
        this.localCCYProduct = localCCYProduct;
    }

    public String getSyntheticEmsOrderId() {
        return syntheticEmsOrderId;
    }

    public void setSyntheticEmsOrderId(String syntheticEmsOrderId) {
        this.syntheticEmsOrderId = syntheticEmsOrderId;
    }

    public String getForeignEmsOrderId() {
        return foreignEmsOrderId;
    }

    public void setForeignEmsOrderId(String foreignEmsOrderId) {
        this.foreignEmsOrderId = foreignEmsOrderId;
    }

    public BrokerOrderRequest getLocalCcyRequest() {
        return localCcyRequest;
    }

    public void setLocalCcyRequest(BrokerOrderRequest localCcyRequest) {
        this.localCcyRequest = localCcyRequest;
    }

    public double getLocalCCYRate() {
        return localCCYRate;
    }

    public void setLocalCCYRate(double localCCYRate) {
        this.localCCYRate = localCCYRate;
    }

    public String getLocalEmsOrderId() {
        return localEmsOrderId;
    }

    public void setLocalEmsOrderId(String localEmsOrderId) {
        this.localEmsOrderId = localEmsOrderId;
    }

    public boolean isEspRfs() {
        return isEspRfs;
    }

    public void setEspRfs(boolean isEspRfs) {
        this.isEspRfs = isEspRfs;
    }

    public boolean isNoCover() {
        return noCover;
    }

    public void setNoCover(boolean noCover) {
        this.noCover = noCover;
    }

    public CurrencyPair getSyntheticCCYPair()
    {
        if(syntheticCCYPair == null) {
            syntheticCCYPair = CurrencyFactory.getCurrencyPair(syntheticCcyRequest.getBaseCurrency(), syntheticCcyRequest.getVariableCurrency());
        }
        return syntheticCCYPair;
    }

    public void setSyntheticCCYPair(CurrencyPair pair)
    {
        this.syntheticCCYPair = pair;
    }

    public int getLocalBuySell() {
        if(localBuySell == null)
        {
            foreignBuySell = SyntheticCrossUtil.getForeignBuySell(syntheticCcyRequest.getBuySell(), getSyntheticCCYPair(), foreignCCYProduct.getCurrencyPair());
            localBuySell = SyntheticCrossUtil.getLocalBuySell(foreignBuySell, foreignCCYProduct.getCurrencyPair(), localCCYProduct.getCurrencyPair());
        }
        return localBuySell;
    }

    public int getForeignBuySell() {
        if(foreignBuySell == null)
        {
            foreignBuySell = SyntheticCrossUtil.getForeignBuySell(syntheticCcyRequest.getBuySell(), getSyntheticCCYPair(), foreignCCYProduct.getCurrencyPair());
            localBuySell = SyntheticCrossUtil.getLocalBuySell(foreignBuySell, foreignCCYProduct.getCurrencyPair(), localCCYProduct.getCurrencyPair());
        }
        return foreignBuySell;
    }

    public EMSOrder getEmsOrder() {
        return emsOrder;
    }

    public void setEmsOrder(EMSOrder emsOrder) {
        this.emsOrder = emsOrder;
    }

    public Product getSyntheticCCYProduct() {
        if(syntheticCCYProduct == null){
            syntheticCCYProduct = SyntheticCrossUtil.getInstance().getProduct(syntheticCcyRequest);
        }
        return syntheticCCYProduct;
    }

    public double getSyntheticOrderAmtInBase(){
        if(syntheticOrderAmtInBase == 0.0D){
            if(syntheticCcyRequest.getDealtCurrency().equals(syntheticCcyRequest.getBaseCurrency())){
                syntheticOrderAmtInBase = syntheticCcyRequest.getAmount();
            }else {
                syntheticOrderAmtInBase = SyntheticCrossUtil.getOtherCurrencyAmount(syntheticCcyRequest.getBaseCurrency(), syntheticCcyRequest.getDealtCurrency(), syntheticCcyRequest.getAmount(), syntheticCcyRequest.getRate());
                syntheticOrderAmtInBase = MathUtilC.correctFloatingPointsCalculationPrecision(syntheticOrderAmtInBase);
            }
        }
        return syntheticOrderAmtInBase;
    }

    public TradingParty getTradingParty(){
        if(tradingParty == null) {
            SingleLegOrder singleLegOrder = emsOrder.getSingleLegOrder();
            CoveredOrderRequestDescriptor coveredOrderRequest = singleLegOrder.getCoveredOrderRequest();
            tradingParty = coveredOrderRequest.getTradingParty();
        }
        return tradingParty;
    }

    public EMSExecutionType getLocalEMSExecutionType() {
        return localEMSExecutionType;
    }

    public void setLocalEMSExecutionType(EMSExecutionType localEMSExecutionType) {
        this.localEMSExecutionType = localEMSExecutionType;
    }

    public Currency getPrimaryDealtCcy() {
        return primaryDealtCcy;
    }

    public void setPrimaryDealtCcy(Currency primaryDealtCcy) {
        this.primaryDealtCcy = primaryDealtCcy;
    }

    public Currency getSecondaryDealtCcy() {
        return secondaryDealtCcy;
    }

    public void setSecondaryDealtCcy(Currency secondaryDealtCcy) {
        this.secondaryDealtCcy = secondaryDealtCcy;
    }

    public double getVehicleCCYAmount() {
        return vehicleCCYAmount;
    }

    public void setVehicleCCYAmount(double vehicleCCYAmount) {
        this.vehicleCCYAmount = vehicleCCYAmount;
    }

    public void setWarehouseEnabled(boolean warehouseEnabled) {
        this.warehouseEnabled = warehouseEnabled;
    }

    public boolean isWarehouseEnabled() {
        return warehouseEnabled;
    }

    public boolean isCoverEnabled() {
        return coverEnabled;
    }

    public void setCoverEnabled(boolean coverEnabled) {
        this.coverEnabled = coverEnabled;
    }

    public void setForeignCcyPair(CurrencyPair foreignCcyPair) {
        this.foreignCcyPair = foreignCcyPair;
    }

    public CurrencyPair getForeignCcyPair() {
        return foreignCcyPair;
    }

    public void setLocalCcyPair(CurrencyPair localCcyPair) {
        this.localCcyPair = localCcyPair;
    }

    public CurrencyPair getLocalCcyPair() {
        return localCcyPair;
    }

    public double getFilledAmount(){
        return filledAmount.get();
    }
    public double addFilledAmount(double amt){
        return filledAmount.addAndGet(amt);
    }

    public double getOrderAmountSynthetic(){
        return syntheticCcyRequest.getAmount();
    }

    public boolean isSentToPrimary() {
        return sentToPrimary;
    }

    public void setSentToPrimary(boolean sentToPrimary) {
        this.sentToPrimary = sentToPrimary;
    }

    public boolean isParialFillAllowed() {
        return parialFillAllowed;
    }

    public void setParialFillAllowed(boolean parialFillAllowed) {
        this.parialFillAllowed = parialFillAllowed;
    }
}
