package com.integral.broker;

import com.integral.broker.config.*;
import com.integral.is.common.mbean.ISFactory;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.system.configuration.ConfigurationFactory;

import java.util.Collection;

public class LocalBrokerAdaptorFactory extends BrokerAdaptorFactory {

    protected static LocalBrokerAdaptorFactory current;
    private static Log log = LogFactory.getLog(LocalBrokerAdaptorFactory.class);
    static {
        current = new LocalBrokerAdaptorFactory();

    }

    protected LocalBrokerAdaptorFactory() {
        super(true);

    }

    public static void init() {

        BrokerAdaptorFactory.current = current;

    }

    public BrokerAdaptorMBean getBrokerAdaptorMBean(String brokerOrg) {
        BrokerAdaptorMBean brokerAdaptorMBean = super.getBrokerAdaptorMBean(brokerOrg);
        if (brokerAdaptorMBean == null) {
            BrokerAdaptor brokerAdaptor = new BrokerAdaptor(brokerOrg);
            boolean isProvisioned =   ISFactory.getInstance().getServicProvidersMBean().isProvisioned(brokerOrg);
            if (brokerAdaptorMBeans.putIfAbsent(brokerOrg, brokerAdaptor) == null) {
               if(!isProvisioned){
                   ConfigurationFactory.registerMBean(brokerAdaptor);
               }
            }
            BrokerCustomConfiguration brokerCustomConfiguration = new BrokerCustomConfiguration(brokerOrg);
            if (brokerCustomMBeans.putIfAbsent(brokerOrg, brokerCustomConfiguration) == null) {
                if(!isProvisioned) {
                    ConfigurationFactory.registerMBean(brokerCustomConfiguration);
                }
            }
            return super.getBrokerAdaptorMBean(brokerOrg);
        }
        return brokerAdaptorMBean;
    }
}
