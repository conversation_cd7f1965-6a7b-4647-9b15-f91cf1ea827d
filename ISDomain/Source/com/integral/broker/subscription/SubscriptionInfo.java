package com.integral.broker.subscription;

import com.integral.finance.currency.CurrencyPair;
import com.integral.user.Organization;
import com.integral.user.User;

public class SubscriptionInfo
{

    private final User user;
    private final CurrencyPair ccyPair;
    private final Organization providerOrg;

    private final long createdTime;

    public User getUser ( )
    {
        return user;
    }

    public CurrencyPair getCcyPair ( )
    {
        return ccyPair;
    }

    public Organization getProviderOrg ( )
    {
        return providerOrg;
    }

    public long getCreatedTime ( )
    {
        return createdTime;
    }

    public SubscriptionInfo ( User user, CurrencyPair ccyPair, Organization providerOrg )
    {
        this.user = user;
        this.ccyPair = ccyPair;
        this.providerOrg = providerOrg;
        createdTime = System.currentTimeMillis ();
    }

    public boolean isSelfSubscription()
    {
        return getUser ().getOrganization ().isSameAs ( getProviderOrg () );
    }

    @Override
    public int hashCode ( )
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((user.getOrganization () == null) ? 0 : user.getOrganization ().hashCode ());
        result = prime * result + ((ccyPair == null) ? 0 : ccyPair.hashCode ());
        result = prime * result + ((providerOrg == null) ? 0 : providerOrg.hashCode ());
        return result;
    }

    /**
     * Add user if needed
     */
    @Override
    public boolean equals ( Object obj )
    {
        if ( this == obj )
        {
            return true;
        }
        if ( obj == null )
        {
            return false;
        }
        if ( getClass () != obj.getClass () )
        {
            return false;
        }
        SubscriptionInfo other = ( SubscriptionInfo ) obj;

        if ( user == null )
        {
            if ( other.user != null )
            {
                return false;
            }
        }
        else if ( !user.getOrganization ().isSameAs ( other.user.getOrganization () ) )
        {
            return false;
        }
        if ( ccyPair == null )
        {
            if ( other.ccyPair != null )
            {
                return false;
            }
        }
        else if ( !ccyPair.isMatch ( other.ccyPair ) )
        {
            return false;
        }

        if ( providerOrg == null )
        {
            if ( other.providerOrg != null )
            {
                return false;
            }
        }
        else if ( !providerOrg.isSameAs ( other.providerOrg ) )
        {
            return false;
        }
        return true;
    }

    @Override
    public String toString ( )
    {
        StringBuilder stringBuilder = new StringBuilder ( 200 );
        stringBuilder.append ( "[SubscriptionInfo-" ).append ( "User=" ).append ( user != null ? user.getFullyQualifiedName () : "" )
                .append ( ",ccyPair=" ).append ( ccyPair != null ? ccyPair.getName () : "" )
                .append ( ",provider=" ).append ( providerOrg != null ? providerOrg.getShortName () : "" ).append ( ']' );
        return stringBuilder.toString ();
    }
}
