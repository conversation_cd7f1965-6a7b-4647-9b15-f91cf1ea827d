package com.integral.broker.subscription;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;

import com.integral.aggregation.config.AggregationServiceFactory;
import com.integral.aggregation.config.AggregationServiceMBean;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.liquidityProvision.LiquidityProvision;
import com.integral.fx.query.SupportedQuickTradeCurrencyPairQueryC;
import com.integral.is.common.Provider;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.pool.ThreadPoolFactory;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.oms.RateSubscriptionManagerC;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.ProviderManagerC;
import com.integral.user.Organization;
import com.integral.user.User;

public class BrokerAutoSubsriber
{
	private static Log log = LogFactory.getLog(BrokerAutoSubsriber.class);

	private SupportedQuickTradeCurrencyPairQueryC quickTradeCcyPairs;
	private AggregationServiceMBean aggregationServiceMBean ;

	/**
	 * Subscribes for rates for the currency pairs of user
	 *
	 * @param org
	 */
	
	private final Organization brokerOrg;
	
	public BrokerAutoSubsriber(Organization brokerOrg) {
		this.brokerOrg = brokerOrg;
		this.quickTradeCcyPairs = new SupportedQuickTradeCurrencyPairQueryC();
		this.aggregationServiceMBean =AggregationServiceFactory.getInstance().getAggregationMBean();
	}
	
	public void autoSubscribe()
	{
		autoSubscribe(null, null);
	}
	public void autoSubscribe( List<String> ccyPairs, List<String> providers)
	{
		try {
			if ( !ISFactory.getInstance().getISMBean().isBrokerAdaptorAutoSubscriptionEnabled(brokerOrg.getShortName()) ) {
				log.info("BrokerAutoSubsriber.autoSubscribe : Auto rates subscription is not enabled");
				return;
			}
			User brokerSubsUser = ISUtilImpl.getInstance().getSystemSubscriptionUser(brokerOrg);
			log.info("BrokerAutoSubsriber.autoSubscribe : Auto Subscribing with user " + brokerSubsUser);

			quickTradeCcyPairs.setOrganizationName(brokerOrg.getShortName());
			Collection<CurrencyPair> currencyPairs = new HashSet<CurrencyPair>(100);
			currencyPairs = quickTradeCcyPairs.findAll();
			
			Collection<CurrencyPair> filteredCCYPairs = new HashSet<CurrencyPair>(100);
			if ( ccyPairs != null && ccyPairs.size() > 0 ) {
				for ( CurrencyPair ccyPair : currencyPairs ) {
					if ( ccyPairs.contains(ccyPair.getName()) ) {
						filteredCCYPairs.add(ccyPair);
					}
				}
			}
			else {
				filteredCCYPairs = currencyPairs;
			}
			
			BatchAutoSubscriptionWorker worker = new BatchAutoSubscriptionWorker(brokerSubsUser, brokerOrg, filteredCCYPairs ,providers);

			ThreadPoolFactory.getInstance().getSecondaryThreadPool().execute(worker);
		}
		catch ( Exception e ) {
			log.error(new StringBuilder(200).append("BrokerAutoSubsriber.autoSubscribe : Exception while subscribing for rates for request=").append("subscriptionOnStartup").toString(), e);
		}
	}

	/**
	 * This class is used to run the rate auto subscription workflow asynchronously.
	 */
	private class BatchAutoSubscriptionWorker implements Runnable
	{
		private User user;
		private Organization fiOrg;
		private Collection<CurrencyPair> ccyPairs;
		List<String> providersList;

		private BatchAutoSubscriptionWorker( User user, Organization fiOrg, Collection<CurrencyPair> ccyPairs  ,List<String> providersList)
		{
			this.user = user;
			this.fiOrg = fiOrg;
			this.ccyPairs = ccyPairs;
			this.providersList = providersList;
		}

		public void run()
		{
			try {
				Collection<Provider> providers = ProviderManagerC.getInstance().getProviderMap().values();
				log.info("BrokerAutoSubsriber.BatchSubscribe.run :  Org= " + fiOrg.getShortName() + ", user= " + user.getFullName() + " ,ccypairs= " + ccyPairs + " , providersList= " + providersList  + " , providers= " + providers);

				for ( Provider provider : providers ) {
					if ( providersList != null && providersList.size() > 0 && !providersList.contains(provider.getProviderOrg().getShortName()) ) {
						continue;
					}
					String providerName = provider.getName();
					Organization providerOrg = ISUtilImpl.getInstance().getOrg(providerName);
					if ( providerOrg != null ) {
						for ( CurrencyPair ccyPair : ccyPairs ) {
							if ( ISUtilImpl.getInstance().isVenueProvider(providerOrg) ) {
								if ( RateSubscriptionManagerC.isValidVenueRelationship(fiOrg, providerOrg) ) {
									LiquidityProvision liquidityProvision = ISUtilImpl.getLiquidityProvision(fiOrg, ccyPair);
									if ( aggregationServiceMBean.isClobSubscriptionEnabled(fiOrg.getShortName()) || ((liquidityProvision != null) && (liquidityProvision.isAllowClobMatch())) ) {
										BrokerSubscriptionManager.getInstance().subscribe(user, ccyPair, providerOrg, user);
									}
									else if ( aggregationServiceMBean.isRiskNetSubscriptionEnabled(fiOrg.getShortName()) || ((liquidityProvision != null) && (liquidityProvision.isAllowRiskNetMatch())) ) {
										BrokerSubscriptionManager.getInstance().subscribe(user, ccyPair, providerOrg, user);
									}
								}
								else {
									log.info(new StringBuilder(200).append("BrokerAutoSubsriber.BatchSubscribe.run:DEBUG : Rates subscription failed. Venue relationship doesn't exist for given key. FI Org=").append(fiOrg).append(",ccyPair=").append(ccyPair).toString());
								}
							}
							else {
								if ( RateSubscriptionManagerC.isValidTradingRelationship(fiOrg, providerOrg) && RateSubscriptionManagerC.isValidExecutionProvider(fiOrg, providerOrg, ccyPair) ) {
									BrokerSubscriptionManager.getInstance().subscribe(user, ccyPair, providerOrg, user);
								}
								else {
									log.info(new StringBuilder(200).append("BrokerAutoSubsriber.BatchSubscribe.run:DEBUG : Rates subscription failed. Trading relationship doesn't exist for given key. FI Org=").append(fiOrg).append(",ccyPair=").append(ccyPair).toString());
								}
							}

						}
					}
				}
			}
			catch ( Exception e ) {
				log.error("BrokerAutoSubsriber.BatchSubscribe.run: Error while auto subscribing for broker Org=" + fiOrg, e);
			}

		}
	}
}
