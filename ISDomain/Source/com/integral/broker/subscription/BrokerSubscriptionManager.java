package com.integral.broker.subscription;

import com.integral.broker.model.BrokerOrganizationFunction;
import com.integral.broker.model.QuoteDistributionManager;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.quote.QuoteHandler;
import com.integral.broker.subscribe.SubscribeFactory;
import com.integral.broker.subscribe.Subscriber;
import com.integral.broker.subscribe.SubscriberManager;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.service.AutoSubscriptionService;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.SubscriptionManager;
import com.integral.lp.UserSubscriptionInfo;
import com.integral.message.MessageHandler;
import com.integral.message.MessageStatus;
import com.integral.message.WorkflowMessage;
import com.integral.message.WorkflowMessageC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.session.IdcSessionContext;
import com.integral.session.IdcSessionManager;
import com.integral.system.configuration.ConfigurationProperty;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.IdcUtilC;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Singleton class to handle all subscriptions of broker to IS layer.
 * Once subscribed for currency pair will not be unsubscribed.
 *
 * <AUTHOR>
 */
public class BrokerSubscriptionManager
{
    protected static BrokerSubscriptionManager INSTANCE = new BrokerSubscriptionManager ();

    public static BrokerSubscriptionManager getInstance ( )
    {
        return INSTANCE;
    }

    private static final Log log = LogFactory.getLog ( BrokerSubscriptionManager.class );
    private final ConcurrentHashMap<SubscriptionInfo, Boolean> subscriptionMap = new ConcurrentHashMap<SubscriptionInfo, Boolean> ();

    /**
     * Subscribe to IS layer for given currency pair & organization combination
     *
     * @param user        user
     * @param ccyPair     currency pair
     * @param providerOrg provider
     * @param ctxUser     context user
     */
    public void subscribe ( User user, CurrencyPair ccyPair, Organization providerOrg, User ctxUser )
    {
        try
        {
            if ( user == null || providerOrg == null || ccyPair == null )
            {
                log.warn ( new StringBuilder ( 200 ).append ( "BSM.subscribe : Invalid parameters in Subscription request" )
                        .append ( user ).append ( "ccyPair=" ).append ( ccyPair ).append ( "providerOrg=" )
                        .append ( providerOrg ).toString () );
                return;
            }
            String brokerOrgName = user.getOrganization ().getShortName ();
            boolean isForceSubscribeEnabled = ISFactory.getInstance ().getISMBean ().isNewSubscriptionForceSubscribeEnabled ( brokerOrgName );
            SubscriptionInfo subscriptionInfo = new SubscriptionInfo ( user, ccyPair, providerOrg );
            Boolean subscribed = subscriptionMap.get ( subscriptionInfo );
            boolean isSelfSubscription = subscriptionInfo.isSelfSubscription ();
            UserSubscriptionInfo userSubscriptionInfo = null;
            if ( !isSelfSubscription )
            {
                userSubscriptionInfo = SubscriptionManager.getInstance ().getUserSubscriptionInfo ( user, providerOrg, ccyPair );
                if ( userSubscriptionInfo == null && (subscribed == null || !subscribed) )
                {
                    log.warn ( "BSM.subscribe : No IS layer subscription found. Force subscribing ccyPair=" + ccyPair
                            + ",broker=" + brokerOrgName + ",lp=" + providerOrg );
                    isForceSubscribeEnabled = true;
                }
            }
            log.info ( new StringBuilder ( 200 ).append ( "BSM.subscribe : Received New Subscription for " )
                    .append ( subscriptionInfo ).append ( ",brokerOrgName=" ).append ( brokerOrgName )
                    .append ( ",isForceSubscribeEnabled=" ).append ( isForceSubscribeEnabled ).append ( ",isSelfSubscription=" )
                    .append ( isSelfSubscription ).append ( ",subscribed=" ).append ( subscribed )
                    .append ( ",userSubscriptionInfo=" ).append ( userSubscriptionInfo ).toString () );


            String printStackTraceStr = ISFactory.getInstance ().getISMBean ().getPropertyForScope ( "Idc.BrokerAdaptor.Subscription.Stack.Trace.Enabled", ConfigurationProperty.DATABASE_LOCAL_SCOPE );
            if ( Boolean.TRUE.toString ().equalsIgnoreCase ( printStackTraceStr ) )
            {
                log.info ( "############################################ BSM.subscribe - BEGIN Stack trace. user="
                        + ",ccyPair=" + ccyPair + ",lp=" + providerOrg + ",userSubInfo=" + userSubscriptionInfo
                        + ",subscribed=" + subscribed );
                Thread.dumpStack ();
                log.info ( "############################################ BSM.subscribe - END Stack trace. user="
                        + ",ccyPair=" + ccyPair + ",lp=" + providerOrg + ",userSubInfo=" + userSubscriptionInfo
                        + ",subscribed=" + subscribed );
            }
            if ( !isForceSubscribeEnabled && subscribed != null && subscribed )
            {
                log.info ( new StringBuilder ( 200 ).append ( "BSM.subscribe : Already subscribed " )
                        .append ( subscriptionInfo ).append ( ",isForceSubscribeEnabled=" )
                        .append ( isForceSubscribeEnabled ).toString () );
            }
            else
            {
                synchronized ( providerOrg )
                {
                    subscribed = subscriptionMap.get ( subscriptionInfo );
                    if ( !isSelfSubscription )
                    {
                        userSubscriptionInfo = SubscriptionManager.getInstance ().getUserSubscriptionInfo ( user, providerOrg, ccyPair );
                        if ( userSubscriptionInfo == null && (subscribed == null || !subscribed) )
                        {
                            log.warn ( "BSM.subscribe : IS layer subscription not found for ccyPair=" + ccyPair
                                    + ",broker=" + brokerOrgName + ",lp=" + providerOrg );
                            isForceSubscribeEnabled = true;
                        }
                    }
                    if ( !isForceSubscribeEnabled && subscribed != null && subscribed )
                    {
                        log.info ( new StringBuilder ( 200 ).append ( "BSM.subscribe : Already subscribed " )
                                .append ( subscriptionInfo ).append ( ",isForceSubscribeEnabled=" )
                                .append ( isForceSubscribeEnabled ).toString () );
                        return;
                    }
                    log.info ( new StringBuilder ( 200 ).append ( "BSM.subscribe : Subscribing for rates for " )
                            .append ( subscriptionInfo ).append ( ",isSelfSubscription=" )
                            .append ( isSelfSubscription ).toString () );
                    doSubscribe ( subscriptionInfo, ctxUser );
                }
            }
        }
        catch ( Exception e )
        {
            log.error ( new StringBuilder ( 200 ).append ( "BSM.subscribe : Exception on rate subscription " )
                    .append ( user ).append ( ",ccyPair=" ).append ( ccyPair ).append ( ",providerOrg=" )
                    .append ( providerOrg ).toString (), e );
        }

    }

    /**
     * Unsubscribe. This is not implemented as subscriptions will always be kept alive
     *
     * @param user        user
     * @param ccyPair     currency pair
     * @param providerOrg provider org
     * @param ctxUser     context user
     */
    public void unsubscribe ( User user, CurrencyPair ccyPair, Organization providerOrg, User ctxUser )
    {
        log.info ( new StringBuilder ( 200 ).append ( "BSM.unsubscribe : Will not unsubscribe user=" )
                .append ( user ).append ( "ccyPair=" ).append ( ccyPair ).append ( "providerOrg=" ).append ( providerOrg )
                .append ( ",contextUser=" ).append ( ctxUser ).toString () );
    }

    private boolean doSubscribe ( SubscriptionInfo subscriptionInfo, User ctxUser )
    {
        try
        {
            IdcSessionContext ctx = IdcSessionManager.getInstance ().getSessionContext ( subscriptionInfo.getUser () );
            IdcSessionManager.getInstance ().setSessionContext ( ctx );
            QuoteHandler quoteHandler = BrokerQuoteFactory.getInstance ().getQuoteHandler ( subscriptionInfo.getUser ().getOrganization () );
            WorkflowMessage wm;
            if ( !subscriptionInfo.isSelfSubscription () )
            {
                wm = AutoSubscriptionService.getInstance ().subscribeCurrencyPairRates ( subscriptionInfo.getUser (), subscriptionInfo.getProviderOrg (), subscriptionInfo.getCcyPair (), true, ( MessageHandler ) quoteHandler, true );
            }
            else
            {
                log.info ( "BSM.doSubscribe : Not sending subscription request to IS layer as the provider is broker itself provider=" + subscriptionInfo.getProviderOrg () );
                wm = new WorkflowMessageC ();
                wm.setStatus ( MessageStatus.SUCCESS );
            }
            if ( wm == null || MessageStatus.FAILURE.equals ( wm.getStatus () ) )
            {
                subscriptionMap.remove ( subscriptionInfo );
                log.info ( new StringBuilder ( 200 ).append ( "BSM.doSubscribe : Rates subscription failed for request=" ).append ( " Key=" ).append ( subscriptionInfo ).toString () );
            }
            else
            {
                subscriptionMap.put ( subscriptionInfo, Boolean.TRUE );
                quoteHandler.handleEvent ( Subscriber.SUBSCRIBE_EVENT, subscriptionInfo.getProviderOrg (), subscriptionInfo.getCcyPair () );
                log.info ( new StringBuilder ( 200 ).append ( "BSM.doSubscribe : Successfully Subscribed for =" ).append ( " Key=" ).append ( subscriptionInfo ).toString () );
                return true;
            }
        }
        catch ( Exception e )
        {
            subscriptionMap.remove ( subscriptionInfo );
            log.error ( new StringBuilder ( 200 ).append ( "BSM.doSubscribe : Exception while subscribing for rates " ).append ( " Key=" ).append ( subscriptionInfo ).toString (), e );
        }
        finally
        {
            if ( ctxUser != null )
            {
                IdcUtilC.setSessionContextUser ( ctxUser );
            }
        }
        return false;
    }

    //used by the dev app utilities
    public ConcurrentHashMap<SubscriptionInfo, Boolean> getSubscriptionsMap ( )
    {
        return subscriptionMap;
    }

    //used by the dev app utilities
    public boolean forceSubscribe ( String userName, String brokerOrgName, String ccyPairName, String providerName )
    {
        try
        {
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( brokerOrgName );
            User brokerUser = brokerOrg.getUser ( userName );
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString ( ccyPairName );
            Organization providerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( providerName );
            return doSubscribe ( new SubscriptionInfo ( brokerUser, ccyPair, providerOrg ), null );
        }
        catch ( Exception e )
        {
            log.error ( "BSM.forceSubscribe : Exception while force for subscribing", e );
        }
        return false;
    }

    private boolean doUnSubscribe ( SubscriptionInfo subscriptionInfo, User ctxUser )
    {
        try
        {
            IdcSessionContext ctx = IdcSessionManager.getInstance ().getSessionContext ( subscriptionInfo.getUser () );
            IdcSessionManager.getInstance ().setSessionContext ( ctx );
            QuoteHandler quoteHandler = BrokerQuoteFactory.getInstance ().getQuoteHandler ( subscriptionInfo.getUser ().getOrganization () );
            WorkflowMessage wm;
            if ( !subscriptionInfo.getUser ().getOrganization ().isSameAs ( subscriptionInfo.getProviderOrg () ) )
            {
                wm = AutoSubscriptionService.getInstance ().unsubscribeCurrencyPairRates ( subscriptionInfo.getUser (), subscriptionInfo.getProviderOrg (), subscriptionInfo.getCcyPair () );
            }
            else
            {
                log.info ( "BSM.doUnSubscribe : Not sending un-subscription request to IS layer as the provider is broker itself provider=" + subscriptionInfo.getProviderOrg () );
                wm = new WorkflowMessageC ();
                wm.setStatus ( MessageStatus.SUCCESS );
            }
            if ( wm == null || MessageStatus.FAILURE.equals ( wm.getStatus () ) )
            {
                log.info ( new StringBuilder ( 200 ).append ( "BSM.doUnSubscribe : Rates un-subscription failed for request=" ).append ( ",Key=" ).append ( subscriptionInfo ).toString () );
            }
            else
            {
                subscriptionMap.remove ( subscriptionInfo );
                quoteHandler.handleEvent ( Subscriber.UNSUBSCRIBE_EVENT, subscriptionInfo.getProviderOrg (), subscriptionInfo.getCcyPair () );
                BrokerOrganizationFunction organizationFunction =
                        subscriptionInfo.getUser ().getOrganization ().getBrokerOrganizationFunction ();
                QuoteDistributionManager quoteDistributionManager =
                        organizationFunction.getQuoteDistributionManager ();
                quoteDistributionManager.quotesUnsubscribed ( subscriptionInfo.getCcyPair ().getName (), subscriptionInfo.getProviderOrg ().getShortName () );
                log.info ( new StringBuilder ( 200 ).append ( "BSM.doUnSubscribe : Successfully unsubscribed for =" ).append ( ",Key=" ).append ( subscriptionInfo ).toString () );
                return true;
            }
        }
        catch ( Exception e )
        {
            log.error ( new StringBuilder ( 200 ).append ( "BSM.doUnSubscribe : Exception while unsubscribing for rates " ).append ( ",Key=" ).append ( subscriptionInfo ).toString (), e );
        }
        finally
        {
            if ( ctxUser != null )
            {
                IdcUtilC.setSessionContextUser ( ctxUser );
            }
        }
        return false;
    }

    //used by the dev app utilities
    public boolean forceUnsubscribe ( String userName, String brokerOrgName, String ccyPairName, String providerName )
    {
        try
        {
            Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( brokerOrgName );
            User brokerUser = brokerOrg.getUser ( userName );
            CurrencyPair ccyPair = CurrencyFactory.getCurrencyPairFromString ( ccyPairName );
            Organization providerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( providerName );
            return doUnSubscribe ( new SubscriptionInfo ( brokerUser, ccyPair, providerOrg ), null );
        }
        catch ( Exception e )
        {
            log.error ( "BSM.forceUnsubscribe : Exception while force unsubscribing", e );
        }
        return false;
    }

    public static boolean isNullOrEmpty ( final Collection<?> c )
    {
        return c == null || c.isEmpty ();
    }

    /**
     * This method is used by JSP
     *
     * @param userName      user
     * @param brokerOrgName broker
     * @param ccyPairs      currency pairs
     * @param providers     providers
     * @return result
     */
    @Deprecated
    public boolean forceUnsubscribeAllFromCache ( String userName, String brokerOrgName, List<String> ccyPairs, List<String> providers )
    {
        try
        {
            Set<SubscriptionInfo> subInfos = subscriptionMap.keySet ();
            for ( SubscriptionInfo subInfo : subInfos )
            {
                if ( !subInfo.getUser ().getOrganization ().getShortName ().equals ( brokerOrgName ) )
                {
                    continue;
                }
                else if ( !isNullOrEmpty ( ccyPairs ) && !ccyPairs.contains ( subInfo.getCcyPair ().getDisplayName () ) )
                {
                    continue;
                }
                else if ( !isNullOrEmpty ( providers ) && !providers.contains ( subInfo.getProviderOrg ().getShortName () ) )
                {
                    continue;
                }
                doUnSubscribe ( subInfo, null );
            }
            return true;
        }
        catch ( Exception e )
        {
            log.error ( "BSM.forceUnsubscribeAllFromCache : Exception while force unsubscribing", e );
        }
        return false;
    }

    /***
     * This method is used by JSP
     * @param userName user name
     * @param brokerOrgName broker
     * @param ccyPairs currency pairs
     * @param providers providers
     * @return result
     */
    @Deprecated
    public boolean forceResubscribeAllFromCache ( String userName, String brokerOrgName, List<String> ccyPairs, List<String> providers )
    {
        try
        {
            Set<SubscriptionInfo> subInfos = subscriptionMap.keySet ();
            for ( SubscriptionInfo subInfo : subInfos )
            {
                if ( !subInfo.getUser ().getOrganization ().getShortName ().equals ( brokerOrgName ) )
                {
                    continue;
                }
                else if ( ccyPairs != null && !ccyPairs.contains ( subInfo.getCcyPair ().getDisplayName () ) )
                {
                    continue;
                }
                else if ( providers != null && !providers.contains ( subInfo.getProviderOrg ().getShortName () ) )
                {
                    continue;
                }
                doSubscribe ( subInfo, null );
            }
            return true;
        }
        catch ( Exception e )
        {
            log.error ( "BSM.forceResubscribeAllFromCache : Exception while force resubscribing", e );
        }
        return false;
    }

    /**
     * This method is used by JSP
     *
     * @param brokerOrgName broker org name
     * @return result
     */
    @Deprecated
    public String revert ( String brokerOrgName )
    {
        if ( brokerOrgName != null && brokerOrgName.trim ().isEmpty () )
        {
            return "Invalid Broker Name : " + brokerOrgName;
        }
        Organization brokerOrg = ReferenceDataCacheC.getInstance ().getOrganization ( brokerOrgName );
        if ( brokerOrg == null )
        {
            return "Invalid Broker Org";
        }
        if ( ISFactory.getInstance ().getISMBean ().isNewSubscriptionEnabled ( brokerOrgName ) )
        {
            return "Cannot perform revert as new broker subscription is still enabled for broker :" + brokerOrgName;
        }
        Set<SubscriptionInfo> subInfos = subscriptionMap.keySet ();
        Set<SubscriptionInfo> successfulUnsubscriptions = new HashSet<SubscriptionInfo> ();
        Set<SubscriptionInfo> failedUnsubscriptions = new HashSet<SubscriptionInfo> ();
        for ( SubscriptionInfo subInfo : subInfos )
        {

            if ( brokerOrgName != null && !subInfo.getUser ().getOrganization ().getShortName ().equals ( brokerOrgName ) )
            {
                continue;
            }

            boolean success = doUnSubscribe ( subInfo, null );
            if ( success )
            {
                log.info ( "BSM.revert: Unsubscribed Successful for " + subInfo );
                successfulUnsubscriptions.add ( subInfo );
            }
            else
            {
                log.warn ( "BSM.revert: Unsubscribe failed for " + subInfo );
                failedUnsubscriptions.add ( subInfo );
            }
        }
        if ( successfulUnsubscriptions.isEmpty () && failedUnsubscriptions.isEmpty () )
        {
            return "No subscriptions were found in cache. Revert should work fine";
        }
        if ( !failedUnsubscriptions.isEmpty () && successfulUnsubscriptions.isEmpty () )
        {
            return "Unsubscritions unsuccessful. Please check logs";
        }
        if ( !successfulUnsubscriptions.isEmpty () && failedUnsubscriptions.isEmpty () )
        {
            log.info ( "BSM.revert - successfully unsubscribed " + successfulUnsubscriptions.size () + " subscriptions" );
        }

        SubscriberManager subsManager = SubscribeFactory.getInstance ().getSubscriberManager ( brokerOrg );

        if ( subsManager.getSubscribers () == null || subsManager.getSubscribers ().isEmpty () )
        {
            return "No subscriptions found in old workflow";
        }

        for ( Subscriber subscriber : subsManager.getSubscribers () )
        {
            log.info ( "revert.revert :  Resubscribing with old module. Product is :" + subscriber.getProduct () );

            if ( subscriber.getProduct () == null || !subscriber.getProduct ().isESP () )
            {
                continue;
            }
            subscriber.stop ();
            subsManager.start ( subscriber, true, "BSM.revert" );
        }
        return null;
    }
}
