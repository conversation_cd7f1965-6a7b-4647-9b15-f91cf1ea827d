// Copyright (c) 2005-2007 Integral Development Corp. All rights reserved.
package com.integral.broker;

import com.integral.adaptor.config.AdaptorConfiguration;
import com.integral.adaptor.config.AdaptorConfigurationFactory;
import com.integral.adaptor.order.configuration.OrderConfiguration;
import com.integral.adaptor.request.RequestHandlerFactoryRegistry;
import com.integral.broker.aggregate.*;
import com.integral.broker.aggregate.rfs.RFSBestPriceQuoteAggregatorC;
import com.integral.broker.aggregate.rfs.SwapBestPriceQuoteAggregatorC;
import com.integral.broker.cache.EMSQuoteCacheFactory;
import com.integral.broker.cache.QuoteCacheCleaner;
import com.integral.broker.config.*;
import com.integral.broker.config.organization.BrokerOrganizationFacade;
import com.integral.broker.config.organization.BrokerOrganizationFacadeC;
import com.integral.broker.filter.BrokerFilterFactory;
import com.integral.broker.fixed.pricing.FixedPricingSchedulerServiceUtility;
import com.integral.broker.log.OrderExecutionLoggerC;
import com.integral.broker.log.RFSOrderExecutionLoggerC;
import com.integral.broker.log.RFSQuoteAggregatorLoggerC;

import com.integral.broker.notify.BrokerPricingMakingEntityUpdateHandler;
import com.integral.broker.notify.NotificationFactory;
import com.integral.broker.pricemaking.PriceMakingRequestMulticastListener;
import com.integral.broker.publish.PublisherHealthChecker;
import com.integral.broker.subscribe.BrokerMMAutoSubscribeUpdateHandler;
import com.integral.cluster.broker.BAClusterService;
import com.integral.ems.LGPriceBookHandler;
import com.integral.ems.LGPriceBookPriceElementHandler;

import com.integral.is.common.mbean.ISFactory;
import com.integral.system.configuration.ConfigRemoteNotificationFunctorC;
import com.integral.broker.model.*;
import com.integral.broker.publish.PublisherFactory;
import com.integral.broker.publish.WatcherFactory;
import com.integral.broker.quote.BrokerQuoteFactory;
import com.integral.broker.request.SpacesBrokerRequestHandlerFactory;
import com.integral.broker.request.matcher.*;
import com.integral.broker.request.matcher.rfs.LimitTOBPriceMatcherC;
import com.integral.broker.request.matcher.rfs.RFSNoCoverNPVMatcherC;
import com.integral.broker.request.matcher.rfs.RFSPriceMatcherC;
import com.integral.broker.status.StatusFactory;
import com.integral.broker.subscribe.SubscribeFactory;
import com.integral.broker.util.StartStop;
import com.integral.ems.EMSServerFactory;
import com.integral.ems.engine.EMSEngineConfiguration;
import com.integral.ems.engine.EMSServer;
import com.integral.ems.provision.EMSProvisionManager;
import com.integral.ems.provision.EMSProvisionManagerFactory;
import com.integral.ems.provision.PMMasterControlChangeListener;
import com.integral.ems.snapshot.MarketSnapshotService;
import com.integral.facade.FacadeFactory;
import com.integral.finance.calculator.CalculatorFactory;
import com.integral.finance.creditLimit.CreditMessageEvent;
import com.integral.finance.creditLimit.logger.CreditLoggerManagerC;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.ISConstantsC;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.rfs.RFSTradeRequest;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.lp.AdaptorManagerC;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.marketmaker.rule.MasterControl;
import com.integral.marketmaker.rule.execution.FullFill;
import com.integral.marketmaker.rule.execution.PersistentFullFillControl;
import com.integral.marketmaker.rule.pricecontrol.PersistentPriceControl;
import com.integral.marketmaker.rule.pricecontrol.PriceControl;
import com.integral.marketmaker.startup.MarketMakerServerStartupC;
import com.integral.persistence.cache.ReferenceDataCacheC;
import com.integral.pipeline.metrics.ServerStatistic;
import com.integral.rds.service.marketmaker.MarketMakerPersistenceService;
import com.integral.system.runtime.StartupTask;
import com.integral.system.server.VirtualServer;
import com.integral.uig.LGPriceDistributionManager;
import com.integral.uig.listener.PriceBookMulticastListener;
import com.integral.user.Organization;
import com.integral.broker.configuration.ConfigurationFactory;
import com.integral.xems.aeron.TradingVenueIntegration;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Hashtable;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Start services for Broker Adaptor.
 *
 * <AUTHOR> Sep 30, 2005 1:38:46 PM
 */
public class BrokerAdaptorStartupC implements StartupTask {
    /**
     * Logger for this class and its descendants.
     */
    protected static final Log log = LogFactory.getLog( BrokerAdaptorStartupC.class );
    private static final String QUOTED = "QUOTED";

    public String startup(String aName, Hashtable args) throws Exception
    {
        try {
            BrokerConfigurationService configurationService = BrokerConfigurationServiceFactory.getBrokerConfigurationService();
            Collection<Organization> brokers = configurationService.getDeployedBrokerOrganizations();
            if( brokers == null || brokers.isEmpty()){
                if(OrderConfiguration.getInstance().isLocalV4Enabled()){
                    EMSQuoteCacheFactory.init();
                    MarketSnapshotService.getInstance();
                    EMSServerFactory.getInstance().init( new EMSEngineConfiguration() );
                    EMSServer emsServer = EMSServerFactory.getInstance().getEMSServer();
                    emsServer.start();
                }
                return "No Broker Organization configured";
            }
            StringBuilder sb = new StringBuilder(100);
            sb.append("Started Broker Adaptor for");
            registerFacades();
            registerCalculators();
            registerCreditLoggers();
            /*
                Start the EMSServer and engines.
                Initialize the EMSQuoteCacheFactory to override QuoteCacheFactory for spaces.
                Initialize the server.
            */
            EMSQuoteCacheFactory.init();
            MarketSnapshotService.getInstance();
            EMSServerFactory.getInstance().init( new EMSEngineConfiguration() );
            EMSServer emsServer = EMSServerFactory.getInstance().getEMSServer();
            emsServer.start();
            boolean isVenueIntegrationEnabled = false;
            TradingVenueIntegration tradingVenueIntegration= null;
            try {
                AdaptorConfiguration config =  AdaptorConfigurationFactory.getAdaptorConfigurationMBean();
                isVenueIntegrationEnabled = config.isVenueIntegrationEnabled() 	 ;

                if (isVenueIntegrationEnabled) {
                    log.info("Venue Integration enabled for the container");
                    Class<?> c =  Class.forName("com.integral.is.common.tradingvenue.aeron.TradingVenueIntegrationManager");
                    Method method = c.getDeclaredMethod("getInstance",null);
                    tradingVenueIntegration = (TradingVenueIntegration) method.invoke(null);
                    tradingVenueIntegration.initialize();
                    // TradingVenueIntegrationManager.getInstance().initialize();
                } else {
                    log.info("Venue Integration not enabled for the container");
                }
            } catch (Exception e) {
                log.error(" Error Initializing Venue Integration",e);
            }

            for(Organization broker : brokers ){
                setRequestHandlerFactory(broker);
                startManagers(broker);
                setOffmarketQuoteObserver(broker);
                AdaptorManagerC.getInstance().getOrLoadAdaptor(broker.getShortName(), true);
                EMSProvisionManager emsProvisionManager = EMSProvisionManagerFactory.getEMSProvisionManager( broker );
                SubscribeFactory.getInstance().getSubscriptionManager(broker).addObserver( emsProvisionManager );
                MarketMakerServerStartupC.startMMServicesForBroker(broker.getShortName());
                startFixedPricingServicesForBroker(broker.getShortName());
				startPeriodicMDSServicesForBroker(broker.getShortName());
                registerBrokerStartup(broker);
                startHeartbeat(broker);
                registerPMMasterControlChangeListener(broker);
                PriceMakingRequestMulticastListener.getInstance().addBroker(broker.getShortName());
                try{
                    if (isVenueIntegrationEnabled) {
                        tradingVenueIntegration.startup(broker);
                    }
                }catch(Exception e){
                    log.error(" Error 2 Initializing Venue Integration",e);
                }
                sb.append(" ").append(broker.getShortName());
            }
            startCleaner();
            ServerStatistic.getInstance().setBrokerVM(true);
            ServerStatistic.getInstance().setBrokerList(brokers);
            BrokerMMAutoSubscribeUpdateHandler propertyUpdateHandler = new BrokerMMAutoSubscribeUpdateHandler();
            ConfigRemoteNotificationFunctorC.addConfigUpdateHandler(propertyUpdateHandler);
            // setup listener for activeRFS customer spread messages.
            ActiveRFSCustomerSpreadListenerC.enableActiveRFSCustomerSpreadListener ( new ActiveRFSCustomerSpreadListenerC() );
            BrokerRDSConfigServiceC.getInstance().addObserver(BrokerRdsConfigurationChangeListener.getInstance());
            if (BrokerAdaptorFactory.getInstance().getBrokerServerConfig().isV4BrokerEnabled()){
                log.info("MDF priceBook Listening property is Enabled");
                PriceBookMulticastListener.getInstance().init(LGPriceDistributionManager.getInstance());
                LGPriceDistributionManager.getInstance().registerPriceDistributionLine(brokers);
                LGPriceDistributionManager.getInstance().addHandler(LGPriceBookHandler.getInstance());
                LGPriceDistributionManager.getInstance().addHandler(LGPriceBookPriceElementHandler.getInstance());

            }

            if ( com.integral.system.configuration.ConfigurationFactory.getServerMBean ().isBrokerAdaptor ()  )
            {
                log.info ( "BAS.startup set up the default broken remote notification update handler to update the provision objects." );
                NotificationFactory.getInstance ().getNotificationHandler ().setDefaultEntityUpdateHandler ( new BrokerPricingMakingEntityUpdateHandler () );

                log.info ( "BAS.startup setup the broker publisher health checker thread." );
                Thread healthCheckerThread = new Thread ( new PublisherHealthChecker (), "BrokerPublisherHealthChecker" );
                healthCheckerThread.setDaemon ( true );
                healthCheckerThread.start ();
            }
            //VirtualServer virtualServer = ISFactory.getInstance().getISMBean().getVirtualServer();
            //new BAClusterService().joinCluster(virtualServer.getServerGroup());
            return sb.toString();
        }
        catch (Exception e) {
            String error = "Failed to start Broker Adaptor";
            log.error(error, e);
            throw e;
        }
    }

    public void startFixedPricingServicesForBroker(String brokerName) {
        try {
            Collection<String> deployedBrokers = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizationNames();
            if (deployedBrokers.contains(brokerName)) {
                Organization org = ReferenceDataCacheC.getInstance().getOrganization(brokerName);
                if (org == null) {
                    log.info("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Not a valid broker org name: " + brokerName);
                } else if (!org.isActive()) {
                    log.info("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Not a active broker org: " + brokerName);
                } else if (org.getBrokerOrganizationFunction() != null) {
                    BrokerAdaptorMBean brokerAdaptorMBean = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(brokerName);
                    Collection<Stream> streams = org.getBrokerOrganizationFunction().getStreams();
                    StringBuilder fixedPricingStreamsName = new StringBuilder(400);
                    for (Stream stream : streams) {
                        if (brokerAdaptorMBean != null && brokerAdaptorMBean.isStreamSupported(stream) && stream.isActive() && stream.isFixedPeriodPricingEnabled()) {
                            fixedPricingStreamsName.append(stream.getName()).append(", ");
                            
                            FixedPricingSchedulerServiceUtility.startup(stream, true);
                        } else {
                            log.info(new StringBuilder(200).append("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Not a Fixed Pricing enabled Stream: ")
                                    .append(stream.getName()).append(", Stream virtual server: ").append(stream.getVirtualServerName())
                                    .append(", Broker name: ").append(brokerName).toString());
                        }
                    }
                    log.info("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Started Fixed Pricing Services for broker: "
                            + brokerName + ", streams= " + fixedPricingStreamsName.toString());
                } else {
                    log.info("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Fixed Pricing is not available for Org: " + brokerName);
                }
            } else {
                log.info("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Not a Fixed Pricing enabled Broker " + brokerName);
            }

        } catch (Exception e) {
            log.error("BrokerAdaptorStartupC.startFixedPricingServicesForBroker:: Exception during initialization", e);
        }
    }

    public void startPeriodicMDSServicesForBroker(String brokerName) {
        try {
            Collection<String> deployedBrokers = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizationNames();
            if (deployedBrokers.contains(brokerName)) {
                Organization org = ReferenceDataCacheC.getInstance().getOrganization(brokerName);
                if (org == null) {
                    log.info("BrokerAdaptorStartupC.startPeriodicMDSServicesForBroker:: Not a valid broker org name: " + brokerName);
                } else if (!org.isActive()) {
                    log.info("BrokerAdaptorStartupC.startPeriodicMDSServicesForBroker:: Not a active broker org: " + brokerName);
                } else if (org.getBrokerOrganizationFunction() != null && ConfigurationFactory.getInstance().getConfigurationMBean().isPeriodicMDSEnabled(org)) {
                    BrokerAdaptorMBean brokerAdaptorMBean = BrokerAdaptorFactory.getInstance().getBrokerAdaptorMBean(brokerName);
                    Collection<Stream> streams = org.getBrokerOrganizationFunction().getStreams();
                    for (Stream stream : streams) {
                        if (brokerAdaptorMBean != null && brokerAdaptorMBean.isStreamSupported(stream) && FixedPricingSchedulerServiceUtility.isStreamValidForRealtimeMDSUpdate(stream) ) {
                            
                            FixedPricingSchedulerServiceUtility.startup(stream, false);
                            log.info("BrokerAdaptorStartupC.startPeriodicMDSServicesForBroker:: Started Periodic MDS Services for broker: "
                                    + brokerName + ", streams= " + stream.getName());
                            break;
                        }
                    }
                } else {
                    log.info("BrokerAdaptorStartupC.startPeriodicMDSServicesForBroker:: Periodic MDS is not enabled for Org: " + brokerName);
                }
            } else {
                log.info("BrokerAdaptorStartupC.startPeriodicMDSServicesForBroker:: Not a Periodic MDS enabled Broker " + brokerName);
            }
        } catch (Exception e) {
            log.error("BrokerAdaptorStartupC.startPeriodicMDSServicesForBroker:: Exception during initialization", e);
        }
    }


    private void registerPMMasterControlChangeListener(Organization broker){
        try {
            new PMMasterControlChangeListener().registerWithRDS(broker.getShortName());
        }catch (Exception e){
            log.error("BrokerAdaptorStartupC.registerPMMasterControlChangeListener:: Exception encountered.", e);
        }
    }

    private void setRequestHandlerFactory(Organization broker) {
        RequestHandlerFactoryRegistry.register(ISCommonConstants.PROVIDER_TYPE_BA, broker.getShortName(), SpacesBrokerRequestHandlerFactory.getInstance(broker));
        RequestHandlerFactoryRegistry.register(broker.getShortName(), SpacesBrokerRequestHandlerFactory.getInstance(broker));
    }

	private void startCleaner() {
        QuoteCacheCleaner.getInstance().start();
    }

    private void registerCreditLoggers(){
        try {
            System.out.println(this + "registerCreditLoggers is called ");
            CreditLoggerManagerC loggerManager = CreditLoggerManagerC.getInstance();

            Log rfsAggregationLog = LogFactory.getLog(RFSQuoteAggregatorLoggerC.class);
            Log rfsExecutionLog = LogFactory.getLog(RFSOrderExecutionLoggerC.class);
            Log espExecutionLog = LogFactory.getLog(OrderExecutionLoggerC.class);

            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.CHECK.getName() + ISConstantsC.REQ_CLSF_RFQ, rfsAggregationLog);
            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.UPDATE.getName() + ISConstantsC.REQ_CLSF_RFQ, rfsExecutionLog);
            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.UPDATE.getName() + QUOTED, espExecutionLog);
            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.REMOVE.getName() + ISConstantsC.REQ_CLSF_RFQ, rfsExecutionLog);
            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.REMOVE.getName() + QUOTED, espExecutionLog);
            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.USE.getName() + ISConstantsC.REQ_CLSF_RFQ, rfsExecutionLog);
            loggerManager.setCreditWorkflowLogger(CreditMessageEvent.USE.getName() + QUOTED, espExecutionLog);
        } catch (Exception e) {
            log.error(this + ".registerCreditLoggers failed", e);
        }
    }
    
    public static void registerFacades() {
        FacadeFactory.setFacade( BrokerOrganizationFacade.class.getName(), Organization.class, BrokerOrganizationFacadeC.class);
    }

    public  static void registerCalculators() {
        // price matchers
        CalculatorFactory.putCalculator(ExecutionMethod.LimitIOC.name(), TradeRequest.class, IOCPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.WeightedAverage.name(), TradeRequest.class, IOCPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.LimitIOCSingle.name(), TradeRequest.class, IOCSinglePriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.LimitFOK.name(), TradeRequest.class, FOKPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketFOK.name(), TradeRequest.class, FOKPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketIOC.name(), TradeRequest.class, IOCPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.TopOfTheBook.name(), TradeRequest.class, TOBIOCPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.LimitMarket.name(), TradeRequest.class, LimitTOBPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketIOCSingle.name(), TradeRequest.class, IOCSinglePriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketRangeFOK.name(), TradeRequest.class, FOKPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketRangeIOC.name(), TradeRequest.class, IOCPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketRangeIOCSingle.name(), TradeRequest.class, IOCSinglePriceMatcherC.class);                
        CalculatorFactory.putCalculator(ExecutionMethod.NoCover.name(), TradeRequest.class, NoCoverPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.NoCoverNoPriceValidation.name(), TradeRequest.class, NoCoverNPVMatcher.class);

        CalculatorFactory.putCalculator(ExecutionMethod.LimitIOC.name(), RFSTradeRequest.class, RFSPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.LimitFOK.name(), RFSTradeRequest.class, RFSPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.MarketIOC.name(), RFSTradeRequest.class, RFSPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.NoCover.name(), RFSTradeRequest.class, RFSPriceMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.NoCoverNoPriceValidation.name(), RFSTradeRequest.class, RFSNoCoverNPVMatcherC.class);
        CalculatorFactory.putCalculator(ExecutionMethod.Manual.name(), RFSTradeRequest.class, ManualPriceMatcherC.class);

        CalculatorFactory.putCalculator(PricingMethod.WORST.name(), PricingMethod.class, MultiTierWorstQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.VWAP.name(), PricingMethod.class, MultiTierAverageQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.MULTI_TIER_MARKET.name(), PricingMethod.class, MultiTierMarketQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.MQ_VWAP.name(), PricingMethod.class, FullBookMTAQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.MQ_MULTI_PRICE_TIERS.name(), PricingMethod.class, FullBookMTWQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.FULL_BOOK.name(), PricingMethod.class, FullBookQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.ESP_BEST_PRICE.name(), PricingMethod.class, BestPriceQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.RAW_BOOK.name(), PricingMethod.class, RawFullBookQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.FOK.name(), PricingMethod.class, FOKQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.FULL_BOOK_TIER.name(), PricingMethod.class, FullBookTierQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.PRICE_DEPTH.name(), PricingMethod.class, MultiTierWorstQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.RFS_BEST_PRICE.name(), PricingMethod.class, RFSBestPriceQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.SWAP_BEST_PRICE.name(), PricingMethod.class, SwapBestPriceQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.MULTI_TIER_FOK.name(), PricingMethod.class, MultiTierFOKQuoteAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.FA_MULTI_TIER.name(), PricingMethod.class, MultiTierFullAmountAggregatorC.class);
        CalculatorFactory.putCalculator(PricingMethod.FA_MULTI_QUOTE.name(), PricingMethod.class, SyntheticFullAmountFullBookAggregatorC.class);
    }

    public static void startHeartbeat(Organization broker) {
        Timer timer = BrokerAdaptorFactory.getInstance().getTimer();
        TimerTask heartbeat = StatusFactory.getInstance().newHeartbeatTask(broker);
        long heartbeatInterval = AdaptorConfigurationFactory.getAdaptorConfigurationMBean().getHeartbeatCheckInterval();
        timer.schedule(heartbeat, 0, heartbeatInterval);
        log.info ( "BAS.startHeartbeat - schedule to send heartbeat once server startup is complete for broker="
                + broker.getShortName () + ",interval=" + heartbeatInterval );
    }

    private void startManagers(Organization broker) {
        StartStop publisherManager = PublisherFactory.getInstance().getPublisherManager(broker);
        publisherManager.start();

        StartStop subscriberManager = SubscribeFactory.getInstance().getSubscriberManager(broker);
        subscriberManager.start();

        StartStop rateFilterManager = BrokerFilterFactory.getInstance().getRateFilterManager(broker);
        rateFilterManager.start();
        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(100);
            sb.append("startManagers : broker=").append(broker.getShortName());
            sb.append(", publisherManager=").append(publisherManager);
            sb.append(", subscriberManager=").append(subscriberManager);
            sb.append(", rateFilterManager=").append(rateFilterManager);
            sb.append(" }");
            log.debug(sb.toString());
        }
    }

    private void setOffmarketQuoteObserver(Organization broker) {
        OffMarketQuoteObserver offmarketQuoteObserver = BrokerQuoteFactory.getInstance().getOffMarketQuoteObserver(broker);
        RateFilterDefinition rateFilterDefinition = ModelFactory.getInstance().newRateFilterDefination();
        rateFilterDefinition.setOffmarketQuoteObserver(offmarketQuoteObserver, true);
    }

    private void registerBrokerStartup(Organization broker) {

        MarketMakerFactory marketMakerFactory = MarketMakerFactory.getInstance();
        // Warming up data for workflow
        try
        {
            PriceControl priceControl= marketMakerFactory.getPriceControlService().query(broker.getShortName());
            if( null!=priceControl)
            {
                log.info("Broker Price Control configuration " + priceControl);
            }
        }
        catch(Exception e)
        {
            log.warn("Failed to warmup market maker price control reference data ");
        }
        try
        {
            FullFill fullFill= marketMakerFactory.getExecutionService().queryFullFill(broker.getShortName());
            if( null!=fullFill)
            {
                log.info("Broker Market Maker Full Fill configuration " + fullFill);
            }
        }
        catch(Exception e)
        {
            log.warn("Failed to warmup market maker full fill reference data ");
        }
        try
        {
            MasterControl masterControl= marketMakerFactory.getMasterControlService().query(broker.getShortName());
            if( null!=masterControl)
            {
                log.info("Broker Market Maker Master Control configuration " + masterControl);
            }
        }
        catch(Exception e)
        {
            log.warn("Failed to warmup market maker MasterControl reference data ");
        }
        log.info("registerBrokerStartup : MarketMaker API Listener Registration START for " + broker.getShortName());
        EMSProvisionManager emsProvisionManager = EMSProvisionManagerFactory.getEMSProvisionManager(broker);
        MarketMakerPersistenceService persistenceService = MarketMakerFactory.getInstance().getPersistenceService();

        persistenceService.addObserver(emsProvisionManager, broker.getNamespace().getShortName(), PersistentFullFillControl.class);
        log.info("registerBrokerStartup MarketMaker FullFill Listener Registration done for " + broker.getShortName());

        persistenceService.addObserver(WatcherFactory.getProductWatcher(), broker.getNamespace().getShortName(), PersistentPriceControl.class);
        log.info("registerBrokerStartup MarketMaker PriceControl Listener Registration done for " + broker.getShortName());
        log.info("registerBrokerStartup : Market Maker API Listener Registration END for " + broker.getShortName());

        

    }

    
}
