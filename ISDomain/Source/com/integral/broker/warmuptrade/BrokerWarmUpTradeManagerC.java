package com.integral.broker.warmuptrade;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Map;
import java.util.StringTokenizer;

import com.integral.adaptor.request.OrderHandler;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.request.SpacesBrokerRequestHandlerFactory;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.dealing.Quote;
import com.integral.is.common.Provider;
import com.integral.is.common.util.ISUtilImpl;
import com.integral.is.message.ResponseMessage;
import com.integral.is.message.TradeReject;
import com.integral.is.message.TradeRequest;
import com.integral.is.message.TradeResponse;
import com.integral.is.message.TradeResponses;
import com.integral.is.message.TradeVerify;
import com.integral.is.warmuptrade.ISWarmUpTradeManagerC;
import com.integral.is.warmuptrade.WarmUpCptyTradeDetails;
import com.integral.is.warmuptrade.WarmUpTradeFactory;
import com.integral.is.warmuptrade.WarmUpTradeUtilC;
import com.integral.is.warmuptrade.WarmUpTransactionDetail;
import com.integral.maker.service.MarketMakerFactory;
import com.integral.user.Organization;
import com.integral.user.User;

/**
 * Created by IntelliJ IDEA.
 * User: kumaru
 * Date: Nov 7, 2007
 * Time: 5:59:00 PM
 * To change this template use File | Settings | File Templates.
 */
public class BrokerWarmUpTradeManagerC extends ISWarmUpTradeManagerC {


    public BrokerWarmUpTradeManagerC() {
        super();
    }

    protected void initializeTestTradeForAllCptyBs(String testTradeProviderList) {

        StringTokenizer st = new StringTokenizer(testTradeProviderList, ",");
        while (st.hasMoreTokens()) {
            String providerName = st.nextToken();
            additionalProviderList.add(providerName);
        }

        StringTokenizer stFIs = new StringTokenizer(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getPrioritizedCounterPartyList(), ",");
        while (stFIs.hasMoreTokens()) {
            String fiName = stFIs.nextToken();
            Organization fi = ISUtilImpl.getInstance().getOrg(fiName);
            if(fi !=null)
              additionalCounterparties.add(fi);
        }

        Collection providers = com.integral.lp.ProviderManagerC.getInstance().getProviderMap().values();
        Iterator providerItr = providers.iterator();
        Collection<Organization> brokerOrgs = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizations();
        if( brokerOrgs == null || brokerOrgs.isEmpty() )
        	return;

        for ( Organization brokerOrg: brokerOrgs ) {
            try {
                MarketMakerFactory.getInstance().getPriceControlService().query(brokerOrg.getShortName());
                //referenceDataService.retrieveAllEntities(PriceControl.class, brokerOrg.getNamespace().getName(), null);
            } catch (Exception e) {
                log.warn("Warmup Of PriceControl Failed for Broker : " + brokerOrg.getNamespace(), e);
            }
            ArrayList cptyBOrgs = (ArrayList) WarmUpTradeUtilC.getInstance().getAllCounterPartyBOrgs(brokerOrg);
            //Iterator cptyBIterator = cptyBOrgs.iterator();

            while (providerItr.hasNext()) {
                String providerName = ((Provider) providerItr.next()).getName();
                Organization providerOrg = ISUtilImpl.getInstance().getOrg(providerName);
                if (cptyBOrgs.contains(providerOrg)) {
                    WarmUpCptyTradeDetails cptyDetail = new WarmUpCptyTradeDetails(providerOrg);
                    ArrayList cptyAOrgs = new ArrayList();
                    cptyAOrgs.add(brokerOrg);
                    cptyDetail.setCptyAs(cptyAOrgs);
                    cptyDetail.setNumberOfTestRequired(testRuntimeCurrencyPairs.size());
                    warmupTradeDetails.put(providerName, cptyDetail);

                }
            }
            //For Broker Self initiated requests


            cptyBOrgs.add(brokerOrg);
            ArrayList cptyAOrgs = (ArrayList) WarmUpTradeUtilC.getInstance().getAllCounterPartyAOrgs(brokerOrg);
            WarmUpCptyTradeDetails cptyDetail = new WarmUpCptyTradeDetails(brokerOrg);
            cptyDetail.setCptyAs(cptyAOrgs);
            cptyDetail.setNumberOfTestRequired(testRuntimeCurrencyPairs.size());
            warmupTradeDetails.put( brokerOrg.getShortName(), cptyDetail );
        }

    }


    protected void initializeTestSupportedCurrencyPairs() {

        testRuntimeCurrencyPairs = (ArrayList<String>) WarmUpTradeUtilC.getInstance().getTestTradeMBean().getBrokerAdaptorMonitoredCcyPairs();
        testStartUpCurrencyPairs = (ArrayList<String>) WarmUpTradeUtilC.getInstance().getTestTradeMBean().getStartUpBrokerAdaptorMonitoredCcyPairs();


    }


    protected void initiateTestRequests(String providerName) {

        /*if (!"CITI".equals(providerName)) {
            return;
        }*/
        Organization cptyBOrg = ISUtilImpl.getInstance().getOrg(providerName);


        log.info("BrokerWarmUpTradeManagerC.initiateTestRequests:  Start Warmup for Provider Org = " + providerName);

        WarmUpCptyTradeDetails cptyBOrgDetail = warmupTradeDetails.get(providerName);

        if (cptyBOrgDetail == null)
            return;

        ArrayList cptyAOrgs = cptyBOrgDetail.getCptyAs();

        if (cptyAOrgs.size() == 0)
            return;


        ArrayList<String> currencyPairs = cptyBOrgDetail.isFirst() ? testStartUpCurrencyPairs : testRuntimeCurrencyPairs;

        if (currencyPairs.size() == 0)
            return;

        Collection<Organization> brokerOrgs = BrokerConfigurationServiceFactory.getBrokerConfigurationService().getDeployedBrokerOrganizations();
        for ( Organization brokerOrg: brokerOrgs )
        {
            WarmUpCptyTradeDetails brokerOrgDetail = warmupTradeDetails.get( brokerOrg.getShortName() );
            User brokerUser = WarmUpTradeUtilC.getInstance().getUser(brokerOrg);

			String ccyPair = currencyPairs.get(0);
            String baseCurrency = CurrencyFactory.getBaseCurrency(ccyPair);
            String termCurrency = CurrencyFactory.getTermCurrency(ccyPair);
            ArrayList<String> allSupportedCurrencyPairs = WarmUpTradeUtilC.getInstance().getAllSupportedCurrencyPairs(brokerOrg);

            if ("ALL".equalsIgnoreCase(baseCurrency) || "ALL".equalsIgnoreCase(termCurrency)) {
                currencyPairs = allSupportedCurrencyPairs;
            }

            if (currencyPairs.size() == 0)
                return;

            cptyBOrgDetail.setNumberOfTestRequired(currencyPairs.size() * cptyAOrgs.size());

            for (int k = 0; k < cptyAOrgs.size(); k++) {
                Organization cptyAOrg = (Organization) cptyAOrgs.get(k);
                User user = WarmUpTradeUtilC.getInstance().getUser(cptyAOrg);


                for (String currencyPair : currencyPairs) {

                    if (cptyAOrg != null && cptyBOrg != null && user != null && cptyAOrg != cptyBOrg && cptyBOrg.isActive() && cptyAOrg.isActive() &&
                            brokerUser != null && brokerOrg != null && brokerOrg != cptyBOrg && brokerOrg.isActive() && allSupportedCurrencyPairs.contains(currencyPair)) {

                        int fiCount = 0;

                        baseCurrency = CurrencyFactory.getBaseCurrency(currencyPair);
                        log.info("BASE CURRENCY = " + baseCurrency);
                        termCurrency = CurrencyFactory.getTermCurrency(currencyPair);
                        log.info("TERM CURRENCY = " + termCurrency);
                        Quote testQuote = null;

                        if (WarmUpTradeUtilC.getInstance().getTestTradeMBean().isClientWorkflowEnabledForBA()) {

                            testQuote = WarmUpTradeFactory.newTestQuote(cptyBOrg, cptyAOrg, baseCurrency, termCurrency);
                            if (testQuote == null) {
                                continue;
                            }
                            WarmUpTradeUtilC.addQuoteToCache(testQuote);
                            try {

                                initiateAppServerTradeRequest(testQuote, cptyBOrg, brokerOrg, brokerUser);
                                Thread.sleep(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDelayBetweenAcceptances());

                            } catch (Exception e) {
                                log.error("BrokerWarmUpTradeManagerC.initiateTestRequests: Error on caling initiateAppServerTradeRequest() " + e.getMessage());
                                if (log.isDebugEnabled())
                                    e.printStackTrace();
                            }

                            //remove from cache
                            WarmUpTradeUtilC.removeQuoteFromCache(testQuote);

                        }
                        ArrayList brokerCptyAOrgs = brokerOrgDetail.getCptyAs();

                        if (additionalCounterparties.size() > 0)
                            brokerCptyAOrgs = prioritizeOrgs(brokerCptyAOrgs, additionalCounterparties);
                        //Iterator brokerCptyAOrgsItr = brokerCptyAOrgs.iterator();

                        for (int j = 0; j < brokerCptyAOrgs.size() && (fiCount < WarmUpTradeUtilC.getInstance().getTestTradeMBean().getMaxFIPerBroker()); j++) {
                            // for (brokerCptyAOrgsItr.hasNext() && fiCount < WarmUpTradeUtilC.getInstance().getTestTradeMBean().getMaxFIPerBroker()) {

                            cptyAOrg = (Organization) brokerCptyAOrgs.get(j);
                            user = WarmUpTradeUtilC.getInstance().getUser(cptyAOrg);
                            ArrayList<String> allBrokerStreamCcyPairs = WarmUpTradeUtilC.getInstance().getAllStreamCurrencyPairs(cptyAOrg, brokerOrg);


                            if (cptyAOrg != null && user != null && cptyAOrg != brokerOrg && cptyAOrg.isActive()
                                    && allBrokerStreamCcyPairs.contains(currencyPair)) {// initiate trade if currency pair applicable to the cpty's stream

                                fiCount++;

                                testQuote = WarmUpTradeFactory.newTestQuote(cptyBOrg, cptyAOrg, baseCurrency, termCurrency);
                                if (testQuote == null) {
                                    continue;
                                }
                                Quote testBrokerQuote = WarmUpTradeFactory.newTestQuote(brokerOrg, cptyAOrg, baseCurrency, termCurrency);
                                if (testBrokerQuote == null) {
                                    continue;
                                }
//                            testBrokerQuote.setNotes(testQuote.getGUID());

                                WarmUpTradeUtilC.addQuoteToCache(testQuote);
                                WarmUpTradeUtilC.addQuoteToCache(testBrokerQuote);

                                try {

                                    initiateSelfBrokerTradeRequest(testBrokerQuote, brokerOrg, cptyAOrg, user, testQuote.getGUID());
                                    Thread.sleep(WarmUpTradeUtilC.getInstance().getTestTradeMBean().getDelayBetweenAcceptances());

                                } catch (Exception e) {
                                    log.error("BrokerWarmUpTradeManagerC.initiateTestRequests: Error on caling initiateAppServerTradeRequest() " + e.getMessage());
                                    if (log.isDebugEnabled())
                                        e.printStackTrace();


                                }
                                //remove from cache
                                WarmUpTradeUtilC.removeQuoteFromCache(testBrokerQuote);
                                WarmUpTradeUtilC.removeQuoteFromCache(testQuote);
                            }

                        }
                    } else {
                        warmupTradeComplete(cptyBOrg.getShortName());
                    }
                }
            }


            cptyBOrgDetail.setFirst(false);
            log.info("BrokerWarmUpTradeManagerC.initiateTestRequests:  End Warmup for Provider Org = " + providerName);
        }
    }


    protected void initiateSelfBrokerTradeRequest
            (Quote
                    testQuote, Organization
                    brokerOrg, Organization
                    cptyAOrg, User
                    user, String providerQuoteId) {

        log.info("BrokerWarmUpTradeManagerC.initiateSelfBrokerTradeRequest:  Start  for CptyB Org= " + brokerOrg.getShortName() + "  CptyA Org = " + cptyAOrg.getShortName() + " User= " + user.getShortName());


        initializeUserSession(user);//initialize trading user session

        // create Test Trade Request
        TradeRequest testTradeRequest = WarmUpTradeFactory.newTestTradeRequest(testQuote, brokerOrg, user);
        testTradeRequest.setProviderQuoteId(providerQuoteId);
        ISUtilImpl.getInstance().setAsWarmUpObject(testTradeRequest);
        String transactionID = testTradeRequest.getTradeId();
        traderequestCache.put(transactionID, testTradeRequest);
        OrderHandler oh = SpacesBrokerRequestHandlerFactory.getInstance(brokerOrg).getOrderHandler();

        try {

            log.info("BrokerWarmUpTradeManagerC.initiateSelfBrokerTradeRequest  request Message" + testTradeRequest);

            ResponseMessage resp = oh.quoteAccepted(testTradeRequest);
            int acceptanceResponseStatus = resp.getStatus();

            if (ResponseMessage.SUCCESS == acceptanceResponseStatus) {
                WarmUpTransactionDetail warmUpTransaction = new WarmUpTransactionDetail(transactionID);
                warmUpTransaction.setUser(user);
//                log.info("BrokerWarmUpTradeManagerC.initiateSelfBrokerTradeRequest " + warmupTradeDetails);
                WarmUpCptyTradeDetails cptyTradeDetails = warmupTradeDetails.get(brokerOrg.getShortName());
                Map<String, WarmUpTransactionDetail> stringWarmUpTransactionDetailMap = cptyTradeDetails.getTestTransactionsDetail();
                stringWarmUpTransactionDetailMap.put(transactionID, warmUpTransaction);
            } else {
                warmupTradeComplete(brokerOrg.getShortName());
                traderequestCache.remove(transactionID);
            }

            log.info("BrokerWarmUpTradeManagerC.initiateSelfBrokerTradeRequest  response Message" + resp);
        }
        catch (Exception re) {
            traderequestCache.remove(transactionID);
            warmupTradeComplete(brokerOrg.getShortName());
            log.error("BrokerWarmUpTradeManagerC.initiateSelfBrokerTradeRequest  error " + re.getMessage());
            if (log.isDebugEnabled())
                re.printStackTrace();
        }


        log.info("BrokerWarmUpTradeManagerC.initiateSelfBrokerTradeRequest:  End  for Cpty B= " + brokerOrg.getShortName() + " CptyA= " + cptyAOrg.getShortName() + " User= " + user.getShortName());


    }


    public void tradeResponseCallBack
            (TradeResponses
                    tradeResponses) {

        log.info("BrokerWarmUpTradeManagerC.tradeResponseCallBack(tradeResponses):  Start ");
        Collection<TradeResponse> responses = tradeResponses.getTradeResponses();
        for (TradeResponse response : responses) {
            tradeResponseCallBack(response);
        }
        log.info("BrokerWarmUpTradeManagerC.tradeResponseCallBack(tradeResponses):  End ");
    }

    public void tradeResponseCallBack
            (TradeResponse
                    tradeResponse) {
        log.info("BrokerWarmUpTradeManagerC.tradeResponseCallBack(tradeResponse):  Start ");


        if (!(tradeResponse instanceof TradeReject) && !(tradeResponse instanceof TradeVerify)) {
            log.info("BrokerWarmUpTradeManagerC.tradeResponseCallBack:  End : Not Verify or Reject ");
            return;
        }
        String transactionID = tradeResponse.getTradeId();
        TradeRequest tradeRequest = traderequestCache.get(transactionID);
        String cptyB = tradeRequest.getProviderShortName();
        String cptyA = tradeRequest.getOrgShortName();

        log.info("TRADE VERIFICATION/REJECTION RECEIVED FOR TRANSACTION ID=  " + transactionID + " for CptyA=" + cptyA + " CptyB= " + cptyB);

        WarmUpCptyTradeDetails cptyWarmUpTradeDeatil = warmupTradeDetails.get(cptyB);
        Map testTransactionDetails = cptyWarmUpTradeDeatil.getTestTransactionsDetail();
        testTransactionDetails.remove(transactionID);
        traderequestCache.remove(transactionID);

        //warmupTradeComplete(cptyB);

        // :todo removeQuoteFromCache(null);   if synchronous removal doesn't work
        log.info("BrokerWarmUpTradeManagerC.tradeResponseCallBack(tradeResponse):  End ");
    }


}
