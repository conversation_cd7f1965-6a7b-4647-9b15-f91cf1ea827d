package com.integral.broker;

import com.google.common.util.concurrent.AtomicDouble;
import com.integral.util.MathUtilC;
// Copyright (c) 2001-2004 Integral Development Corp.  All rights reserved.

/**
 * <AUTHOR> Development Corp.
 */

public class SynchronizedDouble extends AtomicDouble
{

    /**
     * Make a new SynchronizedDouble with the given initial value,
     * and using its own internal lock.
     **/
    public SynchronizedDouble(double initialValue) {
        super(initialValue);
    }

    /**
     * Add amount to value (i.e., set value += amount)
     * @return the new value
     **/
    public double add(double amount) {
        return addAndGet( amount );
    }

    /**
     * Subtract amount from value (i.e., set value -= amount)
     * @return the new value
     **/
    public double subtract(double amount) {
        return addAndGet( -amount );
    }
}

