package com.integral.broker;

import com.integral.broker.config.BrokerConfigurationService;
import com.integral.broker.config.BrokerConfigurationServiceFactory;
import com.integral.broker.marketmaker.customergroup.StreamMMConfigPriceService;
import com.integral.broker.subscribe.BrokerAutoSubscriptionService;
import com.integral.broker.subscribe.BrokerMarketMakerAutoSubscriptionService;
import com.integral.broker.subscription.BrokerAutoSubsriber;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.message.Message;
import com.integral.message.MessageHandler;
import com.integral.system.configuration.ConfigurationFactory;
import com.integral.system.runtime.RuntimeFactory;
import com.integral.system.runtime.StartupTask;
import com.integral.user.Organization;

import java.util.Collection;
import java.util.Hashtable;

/**
 * Created with IntelliJ IDEA.
 * User: anatarajan
 * Date: 8/26/14
 * Time: 1:02 PM
 * To change this template use File | Settings | File Templates.
 */
public class BrokerAutoSubscriptionStartupC implements StartupTask
{

    private static final Log log = LogFactory.getLog ( BrokerAutoSubscriptionStartupC.class );

    public BrokerAutoSubscriptionStartupC ( )
    {
        super ();
    }

    @Override
    public String startup ( String aName, Hashtable args ) throws Exception
    {
		ConfigurationFactory.getServerMBean ().addPostStartupHandler ( BrokerAutoSubscriptionStartupC.class.getSimpleName (), new BrokerAutoSubscriptionHandler () );
        return null;
    }

    private static class BrokerAutoSubscriptionHandler implements MessageHandler
    {
        public Message handle ( Message workflowMessage )
        {
			try
			{
				StringBuilder sb = new StringBuilder ( 200 );
				String vsShortName = RuntimeFactory.getServerRuntimeMBean ().getVirtualServer ().getShortName ();
				sb.append ( "BASS.handle -  vs:" ).append ( vsShortName );

				BrokerConfigurationService configurationService = BrokerConfigurationServiceFactory.getBrokerConfigurationService ();
				Collection<Organization> brokerOrgs = configurationService.getDeployedBrokerOrganizations ();
				if ( brokerOrgs != null && !brokerOrgs.isEmpty () )
				{
					sb.append ( " starting MM AutoSubscription for: " );
					for ( Organization brokerOrg : brokerOrgs )
					{
						(new BrokerAutoSubsriber ( brokerOrg )).autoSubscribe ();
						BrokerAutoSubscriptionService.getInstance ().doAutoSubscription ( brokerOrg );
						sb.append ( brokerOrg.getShortName () ).append ( "," );
						BrokerMarketMakerAutoSubscriptionService.getInstance ().initialize ( brokerOrg );
						StreamMMConfigPriceService.getInstance ().doAutoPublish ( brokerOrg );
					}
				}
				else
				{
					sb.append ( ", Brokers not configured on this vs" );
				}
				log.info ( sb.toString () );
			}
			catch ( Exception e )
			{
				log.error ( "BASS.handle - Exception while doing broker auto subscription.", e );
			}
			return workflowMessage;
        }
    }
}
