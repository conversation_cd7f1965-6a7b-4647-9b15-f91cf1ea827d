package com.integral.broker;

import com.integral.broker.price.PriceFacade;
import com.integral.broker.price.PriceFacadeC;
import com.integral.broker.price.rfs.RFSPriceFacade;
import com.integral.broker.price.rfs.RFSPriceFacadeC;
import com.integral.broker.quote.QuoteFacade;
import com.integral.broker.quote.QuoteFacadeC;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class FacadePolicyC {

    protected Log log = LogFactory.getLog( this.getClass() );

    private static FacadePolicyC instance = new FacadePolicyC();
    public  static FacadePolicyC getInstance() { return instance; }

// any way to factor out all this 'same prcocessing' (only types getters and setters differ....

    private static QuoteFacade quoteFacade = new QuoteFacadeC();
    private static PriceFacade priceFacade = new PriceFacadeC();
    private static RFSPriceFacade rfsPriceFacade = new RFSPriceFacadeC();

    public QuoteFacade getQuoteFacade() {
        return quoteFacade;
    }

    public PriceFacade getPriceFacade() {
        return priceFacade;
    }

    public RFSPriceFacade getRfsPriceFacade() {
        return rfsPriceFacade;
    }
}
